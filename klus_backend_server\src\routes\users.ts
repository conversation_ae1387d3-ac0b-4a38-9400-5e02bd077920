import { Router } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { validateRequired, validateEmail } from '../middleware/validation';
import { adminAuthMiddleware, AuthenticatedRequest } from '../middleware/auth';
import { supabaseAdmin } from '../config/supabase';
import { config } from '../config/environment';

const router = Router();

interface CreateUserPayload {
  email: string;
  first_name: string;
  last_name: string;
  user_type: 'vakman' | 'klusaanvrager' | 'admin';
}

interface DeleteUserPayload {
  id: string;
}

// Create admin user
router.post('/admin/create',
  adminAuthMiddleware,
  validateRequired(['email', 'first_name', 'last_name', 'user_type']),
  validateEmail,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { email, first_name, last_name, user_type }: CreateUserPayload = req.body;

    // Create temporary password
    const tempPassword = Math.random().toString(36).slice(-8);

    // Create user
    const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password: tempPassword,
      email_confirm: true,
      user_metadata: {
        first_name,
        last_name,
        user_type,
      },
    });

    if (createError) {
      throw createError;
    }

    res.json({ data: newUser, success: true });
  })
);

// Delete admin user
router.delete('/admin/:id',
  adminAuthMiddleware,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;

    // Delete auth user
    const { data, error: deleteFromAuthError } = await supabaseAdmin.auth.admin.deleteUser(id);

    if (deleteFromAuthError) {
      throw deleteFromAuthError;
    }

    res.json({ success: true });
  })
);

// Delete user (general)
router.delete('/:id',
  adminAuthMiddleware,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;

    // Delete auth user
    const { data, error: deleteFromAuthError } = await supabaseAdmin.auth.admin.deleteUser(id);

    if (deleteFromAuthError) {
      throw deleteFromAuthError;
    }

    res.json({ success: true });
  })
);

// Send password reset
router.post('/password-reset',
  validateRequired(['email']),
  validateEmail,
  asyncHandler(async (req, res) => {
    const { email } = req.body;
    const siteUrl = process.env.SITE_URL || 'http://localhost:3000';

    console.log('Generating reset password link for:', email);

    const { data, error: resetError } = await supabaseAdmin.auth.admin.generateLink({
      type: 'recovery',
      email: email,
      options: {
        redirectTo: `${siteUrl}/opnieuw_instellen`,
      },
    });

    if (resetError) {
      console.error('Error generating reset link:', resetError);
      throw resetError;
    }

    if (!data?.properties?.action_link) {
      throw new Error('No reset link generated');
    }

    // Send email with Resend
    console.log('Sending reset password email via Resend');
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.resend.apiKey}`,
      },
      body: JSON.stringify({
        from: 'KlusVrij <<EMAIL>>',
        to: [email],
        subject: 'Reset je wachtwoord',
        html: `
          <h2>Wachtwoord resetten</h2>
          <p>Je hebt een verzoek gedaan om je wachtwoord te resetten.</p>
          <p>Klik op onderstaande link om een nieuw wachtwoord in te stellen:</p>
          <p><a href="${data.properties.action_link}">Reset wachtwoord</a></p>
          <p>Als je dit verzoek niet hebt gedaan, kun je deze e-mail negeren.</p>
          <p>De link is 24 uur geldig.</p>
        `,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('Error sending reset email:', error);
      throw new Error('Error sending reset email');
    }

    console.log('Password reset email sent successfully');
    res.json({ message: 'Password reset email sent successfully' });
  })
);

export default router;
