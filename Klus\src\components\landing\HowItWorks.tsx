/**
 * @description This component explains the Klusgebied platform process in three clear, visual steps with engaging animations and interactions.
 * It guides users through posting a job, receiving quotes, and selecting the best professional with intuitive iconography.
 * The component features staggered animations, responsive cards, hover effects, and clear call-to-action flow to improve user understanding.
 * Key variables include stepsData array containing process information, step numbers for visual progression, and interactive hover states.
 */
import React, { useState, useRef, useEffect } from "react";
import {
	Edit3,
	MessageSquare,
	UserCheck,
	ArrowRight,
	CheckCircle,
} from "lucide-react";

const HowItWorks = () => {
	const [isVisible, setIsVisible] = useState(false);
	const sectionRef = useRef();

	useEffect(() => {
		const observer = new IntersectionObserver(
			([entry]) => {
				if (entry.isIntersecting) {
					setIsVisible(true);
				}
			},
			{ threshold: 0.2 }
		);

		if (sectionRef.current) {
			observer.observe(sectionRef.current);
		}

		return () => observer.disconnect();
	}, []);

	const stepsData = [
		{
			step: 1,
			icon: Edit3,
			title: "Plaats je klus gratis",
			description:
				"<PERSON><PERSON>rijf je klus in een paar minuten. Upload foto's en ontvang reacties van vakmannen binnen 24 uur.",
			color: "from-blue-500 to-blue-600",
			textColor: "text-blue-500",
			bgColor: "bg-blue-50",
			features: ["Gratis plaatsen", "Binnen 24u reactie", "Foto's uploaden"],
		},
		{
			step: 2,
			icon: MessageSquare,
			title: "Ontvang gratis offertes",
			description:
				"Vergelijk profielen, reviews en prijzen. Chat direct met vakmannen en stel al je vragen voordat je kiest.",
			color: "from-teal-500 to-teal-600",
			textColor: "text-teal-500",
			bgColor: "bg-teal-50",
			features: ["Meerdere offertes", "Direct chatten", "Reviews bekijken"],
		},
		{
			step: 3,
			icon: UserCheck,
			title: "Kies de beste professional",
			description:
				"Selecteer de vakman die het beste bij je past en plan de uitvoering. Betaal pas na een succesvol afgeronde klus.",
			color: "from-green-500 to-green-600",
			textColor: "text-green-500",
			bgColor: "bg-green-50",
			features: ["Vrije keuze", "Planning maken", "Achteraf betalen"],
		},
	];

	return (
		<section
			ref={sectionRef}
			id="hoe-het-werkt"
			className="py-12 md:py-16 lg:py-20 bg-gradient-to-br from-white via-slate-50 to-white relative overflow-hidden"
		>
			{/* Background Pattern */}
			<div className="absolute inset-0 opacity-5">
				<div
					className="absolute inset-0"
					style={{
						backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2314B8A6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0l8 8-8 8V8h-8v8h-2V8h-8v8L0 8l8-8v8h8V0h8z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
					}}
				></div>
			</div>

			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
				<div
					className={`text-center mb-12 md:mb-20 transition-all duration-700 ${
						isVisible ? "motion-preset-fade-in-up" : "opacity-0 translate-y-5"
					}`}
				>
					<h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
						In 3 simpele stappen de juiste vakman
					</h2>
					<p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
						Van klus plaatsen tot afronding - wij maken het proces zo eenvoudig
						mogelijk voor je.
					</p>
				</div>

				<div className="grid lg:grid-cols-3 gap-8 lg:gap-12">
					{stepsData.map((step, index) => (
						<div
							key={step.step}
							className={`relative group transition-all duration-700 ${
								isVisible
									? `motion-preset-fade-in-up motion-delay-${200 + index * 200}`
									: "opacity-0 translate-y-5"
							}`}
						>
							{/* Connecting Line (desktop only) */}
							{index < stepsData.length - 1 && (
								<div className="hidden lg:block absolute top-20 left-full w-full h-0.5 bg-gradient-to-r from-slate-200 to-transparent z-0">
									<div className="absolute right-8 top-1/2 transform -translate-y-1/2">
										<ArrowRight className="w-5 h-5 text-slate-300 group-hover:text-teal-400 transition-all duration-300 group-hover:translate-x-1" />
									</div>
								</div>
							)}

							{/* Step Card */}
							<div
								className={`relative bg-white rounded-3xl p-6 sm:p-8 lg:p-12 shadow-lg group-hover:shadow-2xl group-hover:shadow-teal-500/10 transition-all duration-500 border border-slate-200/80 group-hover:border-teal-300/50 group-hover:scale-105 group-hover:-translate-y-2`}
							>
								{/* Step Number */}
								<div
									className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${step.color} text-white rounded-2xl text-2xl font-bold mb-8 shadow-lg group-hover:scale-110 group-hover:-rotate-6 transition-all duration-300`}
								>
									{step.step}
								</div>

								{/* Icon */}
								<div className="flex justify-center mb-8">
									<div
										className={`p-5 ${step.bgColor} rounded-2xl group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-sm`}
									>
										<step.icon className="w-10 h-10 text-slate-600" />
									</div>
								</div>

								{/* Content */}
								<h3 className="text-2xl lg:text-3xl font-bold text-slate-800 mb-6 text-center">
									{step.title}
								</h3>
								<p className="text-base text-slate-600 leading-relaxed text-center mb-8">
									{step.description}
								</p>

								{/* Features List */}
								<div className="space-y-3">
									{step.features.map((feature, featureIndex) => (
										<div
											key={featureIndex}
											className={`flex items-center space-x-3 text-sm font-medium text-slate-700 transition-all duration-300 group-hover:translate-x-2`}
										>
											<CheckCircle
												className={`w-4 h-4 ${step.textColor} flex-shrink-0`}
											/>
											<span>{feature}</span>
										</div>
									))}
								</div>

								{/* Hover Effect Glow */}
								<div
									className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${step.color} opacity-0 group-hover:opacity-10 transition-all duration-500 pointer-events-none`}
								></div>
							</div>
						</div>
					))}
				</div>
			</div>
		</section>
	);
};

export default HowItWorks;
