-- RLS Policies for blog-images bucket
-- These policies control access to the blog-images storage bucket

-- 1. Allow authenticated users to view/read blog images (public read access)
CREATE POLICY "Allow public read access to blog images"
ON storage.objects
FOR SELECT
USING (bucket_id = 'blog-images');

-- 2. Allow admin users to upload blog images
CREATE POLICY "Allow admin users to upload blog images"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'blog-images' 
  AND auth.uid() IN (
    SELECT id FROM profiles 
    WHERE user_type = 'admin'
  )
);

-- 3. Allow admin users to update blog images
CREATE POLICY "Allow admin users to update blog images"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'blog-images' 
  AND auth.uid() IN (
    SELECT id FROM profiles 
    WHERE user_type = 'admin'
  )
)
WITH CHECK (
  bucket_id = 'blog-images' 
  AND auth.uid() IN (
    SELECT id FROM profiles 
    WHERE user_type = 'admin'
  )
);

-- 4. Allow admin users to delete blog images
CREATE POLICY "Allow admin users to delete blog images"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'blog-images' 
  AND auth.uid() IN (
    SELECT id FROM profiles 
    WHERE user_type = 'admin'
  )
);

-- Alternative: If you want to allow blog authors (not just admins) to manage their own blog images
-- Uncomment the policies below and comment out the admin-only policies above

/*
-- Allow blog authors to upload images for their own blogs
CREATE POLICY "Allow blog authors to upload their blog images"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'blog-images' 
  AND auth.uid() IN (
    SELECT id FROM profiles 
    WHERE user_type IN ('admin', 'vakman', 'klusaanvrager')
  )
);

-- Allow blog authors to update their own blog images
CREATE POLICY "Allow blog authors to update their blog images"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'blog-images' 
  AND (
    auth.uid() IN (
      SELECT id FROM profiles WHERE user_type = 'admin'
    )
    OR owner = auth.uid()
  )
)
WITH CHECK (
  bucket_id = 'blog-images' 
  AND (
    auth.uid() IN (
      SELECT id FROM profiles WHERE user_type = 'admin'
    )
    OR owner = auth.uid()
  )
);

-- Allow blog authors to delete their own blog images
CREATE POLICY "Allow blog authors to delete their blog images"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'blog-images' 
  AND (
    auth.uid() IN (
      SELECT id FROM profiles WHERE user_type = 'admin'
    )
    OR owner = auth.uid()
  )
);
*/

-- Enable RLS on the storage.objects table (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Optional: Create a more permissive policy for public read access if needed
-- This allows anyone (even unauthenticated users) to view blog images
/*
CREATE POLICY "Allow anonymous read access to blog images"
ON storage.objects
FOR SELECT
USING (bucket_id = 'blog-images');
*/
