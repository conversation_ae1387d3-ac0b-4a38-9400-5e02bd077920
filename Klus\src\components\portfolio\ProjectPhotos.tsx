import { Project } from "./types";

interface ProjectPhotosProps {
  photos: Project["photos"];
}

export const ProjectPhotos = ({ photos }: ProjectPhotosProps) => {
  if (!photos || photos.length === 0) return null;

  return (
    <div className="px-6 pb-6">
      <div className="grid grid-cols-2 gap-4">
        {photos.map((photo) => (
          <div key={photo.id} className="relative group">
            <img
              src={photo.photo_url}
              alt="Project foto"
              className="w-full h-32 object-cover rounded-md"
            />
          </div>
        ))}
      </div>
    </div>
  );
};