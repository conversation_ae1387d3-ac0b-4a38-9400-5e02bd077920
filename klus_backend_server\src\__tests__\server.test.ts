import request from 'supertest';
import app from '../server';

describe('Server', () => {
  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('environment');
      expect(response.body).toHaveProperty('version');
    });
  });

  describe('404 Handler', () => {
    it('should return 404 for unknown routes', async () => {
      const response = await request(app)
        .get('/unknown-route')
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Route /unknown-route not found');
    });
  });

  describe('CORS', () => {
    it('should handle OPTIONS requests', async () => {
      await request(app)
        .options('/api/payments/mollie/create')
        .expect(200);
    });

    it('should include CORS headers', async () => {
      const response = await request(app)
        .get('/health');

      expect(response.headers['access-control-allow-origin']).toBe('*');
    });
  });
});
