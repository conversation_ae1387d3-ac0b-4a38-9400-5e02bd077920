import { Router } from "express";
import { asyncHand<PERSON> } from "../middleware/errorHandler";
import { validateRequired, validateAmount } from "../middleware/validation";
import { config } from "../config/environment";
import { supabaseAdmin } from "../config/supabase";

const router = Router();

// Create Mollie payment
router.post(
  "/mollie/create",
  validateRequired([
    "amount",
    "userId",
    "transactionId",
    "description",
    "redirectUrl",
    "webhookUrl",
  ]),
  validateAmount,
  asyncHandler(async (req, res) => {
    const {
      amount,
      userId,
      transactionId,
      description,
      redirectUrl,
      webhookUrl,
    } = req.body;

    const response = await fetch("https://api.mollie.com/v2/payments", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${config.mollie.apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        amount: {
          value: amount.toFixed(2),
          currency: "EUR",
        },
        description,
        redirectUrl,
        webhookUrl,
        metadata: {
          userId,
          transactionId,
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Mollie API error:", errorData);
      throw new Error(
        `Mollie API error: ${errorData.detail || "Unknown error"}`
      );
    }

    const data = await response.json();
    console.log("Payment created successfully:", data.id);

    res.json({
      paymentUrl: data._links.checkout.href,
      paymentId: data.id,
    });
  })
);

// Create Mollie test payment
router.post(
  "/mollie/create-test",
  validateRequired([
    "amount",
    "userId",
    "transactionId",
    "description",
    "redirectUrl",
    "webhookUrl",
  ]),
  validateAmount,
  asyncHandler(async (req, res) => {
    const {
      amount,
      userId,
      transactionId,
      description,
      redirectUrl,
      webhookUrl,
    } = req.body;

    const response = await fetch("https://api.mollie.com/v2/payments", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${config.mollie.testApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        amount: {
          value: amount.toFixed(2),
          currency: "EUR",
        },
        description,
        redirectUrl,
        webhookUrl,
        metadata: {
          userId,
          transactionId,
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Mollie API error:", errorData);
      throw new Error(
        `Mollie API error: ${errorData.detail || "Unknown error"}`
      );
    }

    const data = await response.json();
    console.log("Test payment created successfully:", data.id);

    res.json({
      paymentUrl: data._links.checkout.href,
      paymentId: data.id,
    });
  })
);

// Mollie webhook handler
router.post(
  "/mollie/webhook",
  asyncHandler(async (req, res) => {
    console.log("Processing Mollie webhook...");

    const paymentId = req.body.id;
    if (!paymentId) {
      throw new Error("No payment ID provided");
    }

    console.log("Fetching payment details for:", paymentId);
    const response = await fetch(
      `https://api.mollie.com/v2/payments/${paymentId}`,
      {
        headers: {
          Authorization: `Bearer ${config.mollie.apiKey}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Mollie API error:", errorData);
      throw new Error(
        `Mollie API error: ${errorData.detail || "Unknown error"}`
      );
    }

    const payment = await response.json();
    console.log("Payment status:", payment.status);

    const { userId, transactionId } = payment.metadata;

    if (payment.status === "paid") {
      console.log("Payment successful, updating transaction and balance...");

      // Update transaction status
      const { error: updateError } = await supabaseAdmin
        .from("balance_transactions")
        .update({
          status: "accepted",
          completed_at: new Date().toISOString(),
          mollie_payment_id: paymentId,
        })
        .eq("id", transactionId);

      if (updateError) {
        console.error("Error updating transaction:", updateError);
        throw updateError;
      }

      // Increment user balance
      const { error: balanceError } = await supabaseAdmin.rpc(
        "increment_balance",
        {
          user_id: userId,
          increment_amount: parseFloat(payment.amount.value),
        }
      );

      if (balanceError) {
        console.error("Error incrementing balance:", balanceError);
        throw balanceError;
      }

      // Create in-app notification for balance update
      try {
        // Get user profile for email
        const { data: userProfile, error: profileError } = await supabaseAdmin
          .from("profiles")
          .select("email, first_name")
          .eq("id", userId)
          .single();

        if (!profileError && userProfile) {
          // Create notification
          const { error: notificationError } = await supabaseAdmin
            .from("notifications")
            .insert({
              user_id: userId,
              title: "Saldo verhoogd",
              message: `Je saldo is verhoogd met €${parseFloat(
                payment.amount.value
              ).toFixed(2)}. Bedankt voor je betaling!`,
              type: "success",
              action_url: "/evenwicht",
              metadata: {
                transaction_id: transactionId,
                payment_id: paymentId,
                amount: parseFloat(payment.amount.value),
              },
            });

          if (notificationError) {
            console.error(
              "Error creating balance notification:",
              notificationError
            );
          } else {
            console.log("Balance notification created successfully");
          }
        }
      } catch (error) {
        console.error("Error creating balance notification:", error);
        // Don't throw - this is non-critical
      }

      console.log("Transaction completed successfully");
    } else if (["failed", "canceled", "expired"].includes(payment.status)) {
      console.log("Payment failed or canceled, updating transaction status...");

      const { error: updateError } = await supabaseAdmin
        .from("balance_transactions")
        .update({
          status: "failed",
          completed_at: new Date().toISOString(),
          mollie_payment_id: paymentId,
        })
        .eq("id", transactionId);

      if (updateError) {
        console.error("Error updating transaction:", updateError);
        throw updateError;
      }
    }

    res.json({ success: true });
  })
);

// Mollie test webhook handler
router.post(
  "/mollie/test-webhook",
  asyncHandler(async (req, res) => {
    console.log("Processing Mollie test webhook...");

    const paymentId = req.body.id;
    if (!paymentId) {
      throw new Error("No payment ID provided");
    }

    console.log("Fetching test payment details for:", paymentId);
    const response = await fetch(
      `https://api.mollie.com/v2/payments/${paymentId}`,
      {
        headers: {
          Authorization: `Bearer ${config.mollie.testApiKey}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Mollie API error:", errorData);
      throw new Error(
        `Mollie API error: ${errorData.detail || "Unknown error"}`
      );
    }

    const payment = await response.json();
    console.log("Test payment status:", payment.status);

    const { userId, transactionId } = payment.metadata;

    if (payment.status === "paid") {
      console.log(
        "Test payment successful, updating transaction and balance..."
      );

      // Update transaction status
      const { error: updateError } = await supabaseAdmin
        .from("balance_transactions")
        .update({
          status: "accepted",
          completed_at: new Date().toISOString(),
          mollie_payment_id: paymentId,
        })
        .eq("id", transactionId);

      if (updateError) {
        console.error("Error updating transaction:", updateError);
        throw updateError;
      }

      // Increment user balance
      const { error: balanceError } = await supabaseAdmin.rpc(
        "increment_balance",
        {
          user_id: userId,
          increment_amount: parseFloat(payment.amount.value),
        }
      );

      if (balanceError) {
        console.error("Error incrementing balance:", balanceError);
        throw balanceError;
      }

      console.log("Test transaction completed successfully");
    } else if (["failed", "canceled", "expired"].includes(payment.status)) {
      console.log(
        "Test payment failed or canceled, updating transaction status..."
      );

      const { error: updateError } = await supabaseAdmin
        .from("balance_transactions")
        .update({
          status: "failed",
          completed_at: new Date().toISOString(),
          mollie_payment_id: paymentId,
        })
        .eq("id", transactionId);

      if (updateError) {
        console.error("Error updating transaction:", updateError);
        throw updateError;
      }
    }

    res.json({ success: true });
  })
);

export default router;
