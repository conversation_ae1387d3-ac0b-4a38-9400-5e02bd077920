import { Router } from "express";
import { as<PERSON><PERSON>and<PERSON> } from "../middleware/errorHandler";
import { validateRequired, validateEmail } from "../middleware/validation";
import { config } from "../config/environment";
import { Webhook } from "standardwebhooks";

const router = Router();

interface EmailRequest {
  to: string[];
  subject: string;
  html: string;
}

// Send email via Resend
router.post(
  "/email/send",
  validateRequired(["to", "subject", "html"]),
  asyncHandler(async (req, res) => {
    const emailRequest: EmailRequest = req.body;
    console.log("Sending email to", emailRequest.to.length, "recipients");

    // Take first recipient for 'to' field, rest go to 'bcc'
    const [firstRecipient, ...bccRecipients] = emailRequest.to;

    const response = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${config.resend.apiKey}`,
      },
      body: JSON.stringify({
        from: "Klusgebied <<EMAIL>>",
        to: firstRecipient, // Required field with first recipient
        bcc: bccRecipients, // Rest of recipients as BCC
        subject: emailRequest.subject,
        html: emailRequest.html,
      }),
    });

    if (response.ok) {
      const data = await response.json();
      console.log("Email sent successfully:", data);
      res.json(data);
    } else {
      const error = await response.text();
      console.error("Error from Resend API:", error);
      throw new Error(`Email sending failed: ${error}`);
    }
  })
);

// Send SMS via MessageBird
router.post(
  "/sms/send",
  validateRequired(["to", "content"]),
  asyncHandler(async (req, res) => {
    const { to, content } = req.body;

    const response = await fetch(
      `https://conversations.messagebird.com/v1/conversations`,
      {
        method: "POST",
        headers: {
          Authorization: `AccessKey ${config.messageBird.accessKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          to: to,
          channelId: config.messageBird.channelId,
          type: "text",
          content: {
            text: content,
          },
        }),
      }
    );

    if (response.ok) {
      const data = await response.json();
      console.log("SMS sent successfully:", data);
      res.json(data);
    } else {
      const error = await response.text();
      console.error("Error from MessageBird API:", error);
      throw new Error(`SMS sending failed: ${error}`);
    }
  })
);

// SMS hook for 2FA
router.post(
  "/sms/hook",
  asyncHandler(async (req, res) => {
    const payload = JSON.stringify(req.body);
    const base64Secret = process.env.SEND_SMS_HOOK_SECRET;

    if (!base64Secret) {
      throw new Error("SMS hook secret not configured");
    }

    const headers = req.headers as Record<string, string>;
    const wh = new Webhook(base64Secret);

    try {
      const { sms, user } = wh.verify(payload, headers);

      const messageBody = `Your code is ${sms.otp} for 2FA.`;
      const phoneNumber = formatDutchPhoneNumber(user.factors[0].phone);

      if (!phoneNumber) {
        return res.status(400).json({
          error: {
            http_code: 400,
            message: "Invalid phone number format.",
          },
        });
      }

      await sendTextMessage(messageBody, phoneNumber);

      res.json({ message: "SMS sent successfully." });
    } catch (error) {
      console.error("SMS hook error:", error);
      res.status(500).json({
        error: {
          http_code: 500,
          message: `Failed to send sms: ${JSON.stringify(error)}`,
        },
      });
    }
  })
);

// Helper functions
const sendTextMessage = async (
  messageBody: string,
  toNumber: string
): Promise<any> => {
  const messageBirdEndpoint = `https://api.bird.com/workspaces/${config.messageBird.workspaceId}/channels/${config.messageBird.channelId}/messages`;

  const result = await fetch(messageBirdEndpoint, {
    method: "POST",
    headers: {
      Authorization: `AccessKey ${config.messageBird.accessKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      receiver: {
        contacts: [
          {
            identifierValue: toNumber,
          },
        ],
      },
      body: {
        type: "text",
        text: {
          text: messageBody,
        },
      },
    }),
  });

  const data = await result.json();

  if (!result.ok) {
    throw new Error(data.message || "Failed to send SMS");
  }

  return result;
};

const formatDutchPhoneNumber = (phoneNumber: string): string | null => {
  // Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, "");

  // Check if it's a Dutch mobile number
  if (cleaned.startsWith("31")) {
    cleaned = cleaned;
  } else if (cleaned.startsWith("0")) {
    cleaned = "31" + cleaned.substring(1);
  } else if (cleaned.length === 9) {
    cleaned = "31" + cleaned;
  }

  // Validate the final format
  const phoneRegex = /^31[6][0-9]{8}$/;
  if (!phoneRegex.test(cleaned)) {
    return null;
  }

  return "+" + cleaned;
};

export default router;
