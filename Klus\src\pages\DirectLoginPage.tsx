import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Loader2 } from "lucide-react";

import { supabase } from "@/integrations/supabase/client";
import { Alert } from "@/components/ui/alert";
import { useAuth } from "@/components/auth/hooks/useAuth";

const DirectLoginPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { session } = useAuth();
  const [status, setStatus] = useState<"idle" | "loading" | "error">("idle");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (session) {
      navigate("/", { replace: true });
      return;
    }
    const email = searchParams.get("email");
    const password = searchParams.get("password");
    if (!email || !password) {
      setError("Geen e-mail of wachtwoord opgegeven in de link.");
      setStatus("error");
      return;
    }
    setStatus("loading");
    supabase.auth
      .signInWithPassword({ email, password })
      .then(({ error }) => {
        if (error) {
          setError(
            error.message ||
              "Inloggen mislukt. Controleer de link of probeer handmatig in te loggen."
          );
          setStatus("error");
        } else {
          setStatus("idle");
          navigate("/", { replace: true });
        }
      })
      .catch((err: any) => {
        setError(
          err?.message ||
            "Inloggen mislukt. Controleer de link of probeer handmatig in te loggen."
        );
        setStatus("error");
      });
    // eslint-disable-next-line
  }, [session, searchParams, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-100 px-4">
      <div className="w-full max-w-md bg-white rounded-xl shadow-lg p-8 flex flex-col items-center">
        <h1 className="text-2xl font-bold mb-4 text-center text-blue-900">
          Direct Inloggen
        </h1>
        <p className="mb-6 text-gray-600 text-center">
          Je wordt automatisch ingelogd met de gegevens uit de link.
          <br />
          Even geduld alstublieft...
        </p>
        {status === "loading" ? (
          <div className="flex flex-col items-center w-full">
            <Loader2 className="mb-4 h-8 w-8 animate-spin text-blue-700" />
            <span className="text-blue-700 font-medium">
              Bezig met inloggen...
            </span>
          </div>
        ) : null}
        {status === "error" && error ? (
          <Alert variant="destructive" className="w-full mb-4">
            {error}
          </Alert>
        ) : null}
        <button
          className="mt-6 text-blue-600 underline text-sm"
          onClick={() => navigate("/inloggen")}
        >
          Handmatig inloggen
        </button>
      </div>
    </div>
  );
};

export default DirectLoginPage;
