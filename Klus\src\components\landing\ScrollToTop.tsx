/**
 * @description This component automatically scrolls the window to the top whenever the route changes, ensuring a smooth user experience.
 * It utilizes the `useLocation` hook from `react-router-dom` to detect navigation events by listening to changes in the URL's pathname.
 * This ensures that every new page view starts from the top, providing a standard and expected behavior in a single-page application.
 * The main variable used is `pathname` from the location object, which triggers the scroll-to-top effect inside a `useEffect` hook.
 */
import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const ScrollToTop = () => {
	const { pathname } = useLocation();

	useEffect(() => {
		try {
			window.scroll({
				top: 0,
				left: 0,
				behavior: "smooth",
			});
		} catch (e) {
			// Fallback for older browsers
			window.scrollTo(0, 0);
		}
	}, [pathname]);

	return null;
};

export default ScrollToTop;
