/**
 * @description This component renders a comprehensive and SEO-optimized detail page for electrician services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for electricians.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Header from "../../components/landing/Header";
import Footer from "../../components/landing/Footer";
import ServiceGallery from "../../components/landing/ServiceGallery";
import FAQSection from "../../components/landing/FAQSection";
import usePageTitle from "../../hooks/usePageTitle";
import {
	ArrowLeft,
	Zap,
	ShieldCheck,
	Clock,
	ArrowRight,
	Lightbulb,
} from "lucide-react";

const ElectricianDetailPage = () => {
	usePageTitle(
		"Elektricien Nodig? | Klusgebied - Installatie & Storingsdienst"
	);
	const navigate = useNavigate();

	const electricianServices = [
		{
			title: "Groepenkast Vervangen",
			description: "Veilige installatie of uitbreiding van uw meterkast.",
		},
		{
			title: "Verlichting Installeren",
			description:
				"Aanleg van binnen- en buitenverlichting, inclusief LED en smart lighting.",
		},
		{
			title: "Storingen Verhelpen",
			description:
				"Snelle 24/7 storingsdienst voor kortsluiting en stroomuitval.",
		},
		{
			title: "Stopcontacten Aanleggen",
			description:
				"Plaatsen van extra stopcontacten en verleggen van leidingen.",
		},
	];

	const benefits = [
		{
			icon: <ShieldCheck className="w-8 h-8 text-white" />,
			title: "NEN 3140 Gecertificeerd",
			description:
				"Onze elektriciens werken volgens de strengste veiligheidsnormen.",
		},
		{
			icon: <Clock className="w-8 h-8 text-white" />,
			title: "24/7 Storingsdienst",
			description: "Direct hulp bij stroomstoringen, dag en nacht beschikbaar.",
		},
		{
			icon: <Lightbulb className="w-8 h-8 text-white" />,
			title: "Energieadvies",
			description:
				"Advies op maat om uw energieverbruik te verlagen en te verduurzamen.",
		},
	];

	const faqs = [
		{
			question: "Wat zijn de kosten voor een elektricien?",
			answer:
				"De kosten voor een elektricien liggen gemiddeld tussen de €45 en €70 per uur, afhankelijk van de complexiteit van de klus. Voor een exacte prijs kunt u het beste een offerte aanvragen via Klusgebied.",
		},
		{
			question: "Is het nodig om mijn groepenkast te vervangen?",
			answer:
				"Een verouderde groepenkast kan een veiligheidsrisico vormen. Als u veel nieuwe apparaten heeft of uw stoppen er vaak uitslaan, is een inspectie of vervanging aan te raden. Onze elektriciens kunnen u hierover adviseren.",
		},
		{
			question: "Kan ik zelf stopcontacten aanleggen?",
			answer:
				"Werken met elektriciteit is gevaarlijk en vereist vakkennis. Voor uw veiligheid en voor de verzekering raden wij sterk aan om dit door een gecertificeerde elektricien te laten doen.",
		},
		{
			question: "Bieden jullie ook service voor smart home installaties?",
			answer:
				"Ja, onze elektriciens zijn ook gespecialiseerd in het installeren van smart home systemen, zoals slimme verlichting, thermostaten en beveiligingssystemen.",
		},
	];

	const galleryMedia = [
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1664868287479-ad9870c4f8dd?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHw0fHxwbHVtYmVyJTJDJTIwZWxlY3RyaWNpYW58ZW58MHx8fHwxNzUxNzM5OTA3fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1659353587819-4621e4534dfd?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHw1fHxwbHVtYmVyJTJDJTIwZWxlY3RyaWNpYW58ZW58MHx8fHwxNzUxNzM5OTA3fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
		{
			type: "video",
			url: "https://cdn.pixabay.com/video/2023/04/19/159674-819378829_medium.mp4",
		},
		{
			type: "image",
			url: "https://images.unsplash.com/photo-1621905252507-b35492cc74b4?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwbHVtYmVyJTJDJTIwZWxlY3RyaWNpYW58ZW58MHx8fHwxNzUxNzM5OTA3fDA&ixlib=rb-4.1.0?w=1024&h=1024",
		},
	];

	return (
		<div className="min-h-screen bg-white">
			<Header />
			<main>
				{/* Hero Section */}
				<section className="relative pt-24 pb-20 lg:pt-32 lg:pb-28 text-white overflow-hidden">
					<div className="absolute inset-0">
						<img
							src="https://images.unsplash.com/photo-1664868287479-ad9870c4f8dd?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHw0fHxwbHVtYmVyJTJDJTIwZWxlY3RyaWNpYW58ZW58MHx8fHwxNzUxNzM5OTA3fDA&ixlib=rb-4.1.0?w=1024&h=1024"
							alt="Professionele elektricien aan het werk"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-slate-900/70"></div>
					</div>
					<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<button
							onClick={() => navigate("/diensten")}
							className="flex items-center gap-2 text-white/80 hover:text-white mb-8 transition-colors duration-300"
						>
							<ArrowLeft size={20} />
							<span>Alle diensten</span>
						</button>
						<div className="max-w-3xl">
							<h1 className="text-4xl md:text-6xl font-bold mb-6 motion-preset-slide-up">
								Elektricien Nodig?
							</h1>
							<p className="text-lg md:text-xl mb-8 motion-preset-slide-up motion-delay-200">
								Voor al uw elektra werkzaamheden, van groepenkast tot
								verlichting. Vind een erkende elektricien voor veilige en
								vakkundige installaties.
							</p>
							<div className="motion-preset-slide-up motion-delay-400">
								<button
									onClick={() =>
										window.open(
											"https://klusgebied.nl/plaats-een-klus/elektra-klussen",
											"_blank"
										)
									}
									className="bg-teal-500 text-white px-8 py-4 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-lg hover:shadow-teal-500/40 transform hover:-translate-y-1 text-lg"
								>
									Vind een elektricien{" "}
									<ArrowRight className="inline-block ml-2" />
								</button>
							</div>
						</div>
					</div>
				</section>

				{/* Services Section */}
				<section className="py-16 lg:py-24 bg-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
								Onze Elektra Diensten
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
								Veiligheid en vakmanschap staan voorop bij elke elektrische
								klus.
							</p>
						</div>
						<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
							{electricianServices.map((service, index) => (
								<div
									key={index}
									className="bg-slate-50 p-8 rounded-2xl shadow-sm hover:shadow-xl hover:-translate-y-1 transition-all duration-300"
								>
									<h3 className="text-xl font-bold text-slate-800 mb-2">
										{service.title}
									</h3>
									<p className="text-slate-600">{service.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				{/* Benefits Section */}
				<section className="py-16 lg:py-24 bg-slate-800 text-white">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="text-center mb-12">
							<h2 className="text-3xl md:text-4xl font-bold">
								De Zekerheid van Klusgebied
							</h2>
							<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
								Kies voor een elektricien via Klusgebied en wees verzekerd van
								kwaliteit.
							</p>
						</div>
						<div className="grid md:grid-cols-3 gap-8">
							{benefits.map((benefit, index) => (
								<div
									key={index}
									className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
									style={{ "--motion-delay": `${index * 150}ms` } as React.CSSProperties}
								>
									<div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500 mb-6">
										{benefit.icon}
									</div>
									<h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
									<p className="text-slate-300">{benefit.description}</p>
								</div>
							))}
						</div>
					</div>
				</section>

				<ServiceGallery media={galleryMedia} />
				<FAQSection faqs={faqs} />

				{/* Final CTA */}
				<section className="bg-gradient-to-r from-yellow-500 to-orange-500">
					<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
						<h2 className="text-3xl md:text-4xl font-bold mb-4">
							Storing of nieuwe installatie?
						</h2>
						<p className="text-lg opacity-90 mb-8">
							Plaats uw klus en ontvang snel offertes van de beste elektriciens
							bij u in de buurt. Veilig, snel en betrouwbaar.
						</p>
						<button
							onClick={() =>
								window.open(
									"https://klusgebied.nl/plaats-een-klus/elektra-klussen",
									"_blank"
								)
							}
							className="bg-white text-yellow-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
						>
							Plaats nu je elektra klus
						</button>
					</div>
				</section>
			</main>
			<Footer />
		</div>
	);
};

export default ElectricianDetailPage;
