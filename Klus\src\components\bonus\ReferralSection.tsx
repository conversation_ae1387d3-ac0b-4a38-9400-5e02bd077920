import { useState } from "react";
import { Users } from "lucide-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";

import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { ReferralForm } from "./ReferralForm";
import { ReferralList } from "./ReferralList";

export const ReferralSection = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: referrals } = useQuery({
    queryKey: ["referrals"],
    queryFn: async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return [];

      const { data, error } = await supabase
        .from("referrals")
        .select(
          `
          *,
          referred_user:referred_user_id (
            id,
            job_responses (
              id,
              status
            )
          )
        `
        )
        .eq("referrer_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching referrals:", error);
        throw error;
      }

      return data;
    },
  });

  const handleReferralSubmit = async (email: string) => {
    setIsSubmitting(true);

    try {
      // Validate user authentication
      const {
        data: { user },
        error: authError,
      } = await supabase.auth.getUser();
      if (authError || !user) {
        throw new Error(
          "Je moet ingelogd zijn om een uitnodiging te versturen"
        );
      }

      // Create referral record
      const referralData = {
        referrer_id: user.id,
        referred_email: email.toLowerCase().trim(), // Normalize email
        status: "pending",
      };

      const { error: referralError } = await supabase
        .from("referrals")
        .insert(referralData);

      if (referralError) {
        throw new Error(`Fout bij maken uitnodiging: ${referralError.message}`);
      }

      // Prepare and send email
      const emailContent = {
        to: [email],
        subject: "Uitnodiging van Klusgebied",
        html: `
          <p>Welkom bij Klusgebied! U bent uitgenodigd om deel te nemen aan ons platform.</p>
          <p>Klik <a href="https://klusgebied.nl/auth?signup=true">hier</a> om uw account aan te maken.</p>
          <p>Met vriendelijke groet,<br>Het Klusgebied Team</p>
        `,
      };

      try {
        const { backendApi } = await import("@/lib/backendApi");
        await backendApi.sendEmail(emailContent);
      } catch (error: any) {
        throw new Error(`Fout bij versturen email: ${error.message}`);
      }

      // Success notification
      toast({
        title: "Uitnodiging verstuurd!",
        description: `De uitnodiging is succesvol verstuurd naar ${email}`,
      });

      // Refresh referrals list
      await queryClient.invalidateQueries({ queryKey: ["referrals"] });
    } catch (error) {
      console.error("Referral submission error:", error);
      toast({
        title: "Fout bij versturen",
        description:
          error instanceof Error
            ? error.message
            : "Er ging iets mis bij het versturen van de uitnodiging",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteReferral = async (referralId: string) => {
    try {
      const { error } = await supabase
        .from("referrals")
        .delete()
        .eq("id", referralId);

      if (error) throw error;

      toast({
        title: "Uitnodiging verwijderd",
        description: "De uitnodiging is succesvol verwijderd",
      });

      queryClient.invalidateQueries({ queryKey: ["referrals"] });
    } catch (error: any) {
      console.error("Error deleting referral:", error);
      toast({
        title: "Fout bij verwijderen",
        description:
          error.message ||
          "Er ging iets mis bij het verwijderen van de uitnodiging",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="p-4 sm:p-6 mb-4 sm:mb-8 transform transition-all duration-300 hover:shadow-lg">
      <div className="flex flex-col sm:flex-row items-start gap-4 sm:gap-6">
        <div className="w-auto p-3 sm:p-4 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl animate-fade-in">
          <Users className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
        </div>
        <div className="flex-1 w-full space-y-4 sm:space-y-6">
          <div className="space-y-2 animate-fade-in">
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">
              Nodig andere vakmannen uit
            </h3>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
              Verdien 25 credits voor elke vakman die zich via jouw uitnodiging
              registreert en actief wordt op het platform.
            </p>
          </div>

          <ReferralForm
            onSubmit={handleReferralSubmit}
            isSubmitting={isSubmitting}
          />

          {referrals && referrals.length > 0 && (
            <ReferralList
              referrals={referrals}
              onDelete={handleDeleteReferral}
            />
          )}
        </div>
      </div>
    </Card>
  );
};
