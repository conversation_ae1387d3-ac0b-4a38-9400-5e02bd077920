import { LucideIcon } from "lucide-react";
import { ProfileFormData } from "./CreateProfileForm";

export interface Questionnaire {
  id: number;
  question: string;
  options?: string[];
  type?: string;
  placeholder?: string;
}

export interface QuestionnaireItem {
  id: string;
  label: string;
  icon: LucideIcon;
  questions: Questionnaire[];
  hasCustomQuestion?: boolean;
  category?: string;
  description?: string;
  services?: string[];
}

export interface CreateProfileFormProps {
  onBack: () => void;
  onSubmit: (value: ProfileFormData) => void;
  isLoading: boolean;
}
