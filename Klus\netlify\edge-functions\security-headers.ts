// Edge function for enhanced security headers and SSL enforcement
import type { Context } from "https://edge.netlify.com";

export default async (request: Request, context: Context) => {
  const url = new URL(request.url);

  // Force HTTPS redirect if not already HTTPS
  if (url.protocol === "http:") {
    const httpsUrl = url.toString().replace("http:", "https:");
    return Response.redirect(httpsUrl, 301);
  }

  // Get the response from the origin
  const response = await context.next();

  // Clone the response to modify headers
  const newResponse = new Response(response.body, response);

  // Enhanced security headers for SSL/HTTPS
  const securityHeaders = {
    // HSTS - Force HTTPS for 1 year, include subdomains, allow preloading
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",

    // Prevent MIME type sniffing
    "X-Content-Type-Options": "nosniff",

    // Prevent clickjacking
    "X-Frame-Options": "DENY",

    // XSS protection
    "X-XSS-Protection": "1; mode=block",

    // Referrer policy for privacy
    "Referrer-Policy": "strict-origin-when-cross-origin",

    // Permissions policy
    "Permissions-Policy":
      "camera=(), microphone=(), geolocation=(self), payment=(self)",

    // Content Security Policy
    "Content-Security-Policy": [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google.com https://www.gstatic.com https://maps.googleapis.com https://js.mollie.com https://www.googletagmanager.com https://www.google-analytics.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://bbyifnpcqefabwexvxdc.supabase.co wss://bbyifnpcqefabwexvxdc.supabase.co https://api.mollie.com https://www.google-analytics.com",
      "frame-src https://www.google.com https://js.mollie.com",
      "object-src 'none'",
      "base-uri 'self'",
      "upgrade-insecure-requests",
    ].join("; "),

    // Additional SEO and security headers
    "X-Robots-Tag": "index, follow",
    "X-DNS-Prefetch-Control": "on",
  };

  // Apply security headers
  Object.entries(securityHeaders).forEach(([key, value]) => {
    newResponse.headers.set(key, value);
  });

  // Add canonical URL header for SEO
  const canonicalUrl = `https://${url.hostname}${url.pathname}`;
  newResponse.headers.set("Link", `<${canonicalUrl}>; rel="canonical"`);

  return newResponse;
};

export const config = {
  path: "/*",
};
