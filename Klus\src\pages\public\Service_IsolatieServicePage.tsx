/**
 * @description This component renders a comprehensive and SEO-optimized detail page for insulation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for insulation technicians. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  ThermometerSun,
  ShieldCheck,
  ArrowRight,
  Star,
  Edit3,
  <PERSON>S<PERSON>re,
  <PERSON>r<PERSON>heck,
  CheckCircle,
  MapPin,
  Euro,
  Leaf,
  TrendingUp,
  Wind,
} from "lucide-react";

const Service_IsolatieServicePage = () => {
  usePageTitle("Isolatie Specialist Nodig? | Klusgebied - Bespaar op Energie");
  const navigate = useNavigate();

  const insulationServices = [
    {
      icon: Wind,
      title: "Spouwmuurisolatie",
      description:
        "De meest effectieve manier om uw energierekening direct te verlagen.",
      points: [
        "Vult de lege ruimte tussen binnen- en buitenmuur.",
        "Houdt warmte binnen in de winter, en buiten in de zomer.",
        "Terugverdientijd van slechts enkele jaren.",
        "Minimale overlast, vaak binnen één dag klaar.",
      ],
    },
    {
      icon: ThermometerSun,
      title: "Dakisolatie",
      description: "Voorkom dat warmte ontsnapt via het dak en bespaar flink.",
      points: [
        "Warmte stijgt op, dus dakisolatie is cruciaal.",
        "Maakt uw zolder een comfortabele leefruimte.",
        "Verhoogt de waarde van uw woning.",
        "Verschillende materialen mogelijk, passend bij uw dak.",
      ],
    },
    {
      icon: TrendingUp,
      title: "Vloerisolatie",
      description: "Nooit meer koude voeten en een comfortabeler leefklimaat.",
      points: [
        "Stopt optrekkende kou en vocht vanuit de kruipruimte.",
        "De vloer voelt direct warmer aan.",
        "Bespaart energie en verhoogt het wooncomfort.",
        "Ideaal in combinatie met vloerverwarming.",
      ],
    },
    {
      icon: Leaf,
      title: "HR++ Glas",
      description: "Vervang enkel of oud dubbel glas voor hoogrendementsglas.",
      points: [
        "Isoleert tot 5 keer beter dan enkel glas.",
        "Minder last van condens aan de binnenkant.",
        "Dempt geluid van buitenaf aanzienlijk.",
        "Subsidiemogelijkheden beschikbaar.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <TrendingUp className="w-8 h-8 text-white" />,
      title: "Direct Besparen",
      description:
        "Verlaag uw energierekening direct met honderden euro's per jaar.",
    },
    {
      icon: <ThermometerSun className="w-8 h-8 text-white" />,
      title: "Meer Comfort",
      description:
        "Een warmer huis in de winter, koeler in de zomer, en minder tocht.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Waardeverhoging",
      description:
        "Een goed geïsoleerd huis met een beter energielabel is meer waard.",
    },
  ];

  const faqs = [
    {
      question: "Welke isolatie levert het meeste op?",
      answer:
        "Spouwmuur- en dakisolatie hebben de grootste impact op uw energierekening en comfort, omdat hier het meeste warmteverlies optreedt. De terugverdientijd is vaak het kortst.",
    },
    {
      question: "Kan ik subsidie krijgen voor isolatie?",
      answer:
        "Ja, de overheid stimuleert isolatiemaatregelen met subsidies. De voorwaarden veranderen regelmatig. Onze specialisten kunnen u informeren over de actuele mogelijkheden.",
    },
    {
      question: "Hoe lang duurt het isoleren van een woning?",
      answer:
        "Spouwmuurisolatie is vaak binnen één dag voltooid. Dak- en vloerisolatie duren meestal 1 tot 3 dagen, afhankelijk van de grootte en complexiteit.",
    },
    {
      question: "Is isoleren een rommelige klus?",
      answer:
        "De overlast is minimaal. Onze vakmensen werken netjes en ruimen alles na afloop op. Bij spouwmuurisolatie wordt er volledig van buitenaf gewerkt.",
    },
  ];

  const reviews = [
    {
      name: "Familie de Boer",
      location: "Groningen",
      rating: 5,
      quote:
        "Onze spouwmuur is geïsoleerd en we merkten direct verschil. De thermostaat kan een paar graden lager. Top service!",
      highlighted: true,
    },
    {
      name: "Mark Willemsen",
      location: "Arnhem",
      rating: 5,
      quote:
        "De zolder was altijd ijskoud, nu is het een fijne werkplek dankzij de dakisolatie. Professioneel en snel uitgevoerd.",
      highlighted: false,
    },
    {
      name: "Linda de Vries",
      location: "Zwolle",
      rating: 5,
      quote:
        "Nooit meer koude voeten! De vloerisolatie is echt een aanrader. De mannen van Klusgebied werkten super netjes.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1631277190979-1704e8c7d574?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwaW5zdGFsbGF0aW9uJTJDJTIwd2FsbCUyMGluc3VsYXRpb24lMkMlMjBob21lJTIwaW1wcm92ZW1lbnR8ZW58MHx8fHwxNzUxNzQwODA2fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1547565687-e6475ec581cf?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1737948641148-1145c4273c70?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwbWF0ZXJpYWxzJTJDJTIwZW5lcmd5JTIwZWZmaWNpZW5jeSUyQyUyMGhvbWUlMjBpbnN1bGF0aW9ufGVufDB8fHx8MTc1MTc0MjQ4M3ww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1673190889421-c02cccd99e2f?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwdGVjaG5pY2lhbiUyQyUyMGhvbWUlMjBpbnN1bGF0aW9uJTJDJTIwZW5lcmd5JTIwZWZmaWNpZW5jeXxlbnwwfHx8fDE3NTIxODM2ODZ8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Vraag advies aan",
      description:
        "Beschrijf uw woning en wensen. Onze experts geven gratis en vrijblijvend advies.",
      microcopy: "Gratis advies op maat",
    },
    {
      icon: MessageSquare,
      title: "Ontvang offertes",
      description:
        "Krijg duidelijke offertes van geverifieerde isolatiespecialisten uit uw regio.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & laat isoleren",
      description:
        "Vergelijk de specialisten, kies de beste deal en geniet van een comfortabeler huis.",
      microcopy: "Vergelijk en bespaar",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Isolatiespecialisten in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "isolatiespecialist",
    color: "green",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Isolatie Specialist Nodig? | Klusgebied - Bespaar op Energie
        </title>
        <meta
          name="description"
          content="Vind een betrouwbare isolatiespecialist voor spouwmuur-, dak- en vloerisolatie. Bespaar direct op uw energierekening en verhoog uw wooncomfort. Vraag gratis offertes aan."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Isolatie Service",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.8",
              reviewCount: "241",
            },
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-green-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-green-100 border border-green-200/80 rounded-full px-4 py-2 mb-6">
                    <ThermometerSun className="w-5 h-5 text-green-600" />
                    <span className="text-green-800 font-semibold text-sm">
                      Isolatie Service
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Isolatie Specialist?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-teal-500 mt-2">
                      Comfort & Besparing
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Verhoog uw wooncomfort en verlaag uw energierekening met
                    professionele spouwmuur-, vloer- of dakisolatie.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus/huisisolatie")}
                      className="group inline-flex items-center justify-center bg-green-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw specialist
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1673190889421-c02cccd99e2f?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnN1bGF0aW9uJTIwdGVjaG5pY2lhbiUyQyUyMGhvbWUlMjBpbnN1bGF0aW9uJTJDJTIwZW5lcmd5JTIwZWZmaWNpZW5jeXxlbnwwfHx8fDE3NTIxODM2ODZ8MA&ixlib=rb-4.1.0?w=1024&h=1024"
                    alt="Isolatiemonteur aan het werk in een woning"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Uw Huis Geïsoleerd in 3 Stappen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte isolatiespecialist.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-green-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Isolatie Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Een compleet aanbod voor een comfortabel en energiezuinig huis.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {insulationServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-green-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-green-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost isolatie via Klusgebied?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten zijn afhankelijk van het type isolatie en de
                oppervlakte. U ontvangt altijd een duidelijke offerte vooraf.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Spouwmuurisolatie:{" "}
                    <strong className="text-slate-900">€15–€30</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Vloerisolatie:{" "}
                    <strong className="text-slate-900">€20–€40</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Dakisolatie:{" "}
                    <strong className="text-slate-900">€40–€70</strong> per m²
                  </span>
                </li>
              </ul>
              <button
                onClick={() => navigate("/plaats-een-klus/huisisolatie")}
                className="bg-green-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis offertes aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die hun huis lieten isoleren via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-green-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-green-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-green-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-green-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Voordelen van Isoleren
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Een slimme investering in uw portemonnee, comfort en het milieu.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-green-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-green-500 to-teal-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar om te besparen?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvend isolatieadvies aan en ontdek hoeveel u kunt
              besparen op uw energierekening.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/huisisolatie")}
              className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu advies aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/huisisolatie")}
          className="w-full group inline-flex items-center justify-center bg-green-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-green-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_IsolatieServicePage;
