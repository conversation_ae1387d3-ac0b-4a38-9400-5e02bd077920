/**
 * @description This component renders the Feedback page for the Klusgebied platform. It features a fully functional feedback form that allows users to submit suggestions, bug reports, compliments, or complaints. The form data is saved to a Supabase database, and an email notification is sent. The page is designed to encourage user input with a clean, engaging interface, world-class animations, and provides clear submission status feedback. Key variables include form state, submission handlers, and API integration logic.
 */
import React, { useState } from "react";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
  MessageSquare,
  Star,
  Send,
  Loader,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import config from "../../lib/data/project_config.json";
import { supabase } from "@/integrations/supabase/client";

const FeedbackPage = () => {
  usePageTitle("Feedback | Klusgebied");
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    feedback_type: "Suggestion",
    rating: 0,
    message: "",
  });
  const [status, setStatus] = useState("idle"); // idle, loading, success, error
  const [error, setError] = useState("");

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleRating = (rate) => {
    setFormData((prev) => ({ ...prev, rating: rate }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.email || !formData.message) {
      setError("Email en bericht zijn verplicht.");
      setStatus("error");
      return;
    }
    setStatus("loading");
    setError("");

    try {
      // 1. Check for duplicates
      const { data: existingSubmission, error: checkError } = await supabase
        .from("feedback_submissions")
        .select("id")
        .eq("email", formData.email)
        .eq("message", formData.message)
        .single();

      if (checkError && checkError.code !== "PGRST116") {
        throw new Error(`Database check error: ${checkError.message}`);
      }

      if (existingSubmission) {
        setStatus("error");
        setError("U heeft deze feedback al eerder verzonden.");
        return;
      }

      // 2. Send email notification
      let emailSent = true;
      try {
        const { backendApi } = await import("@/lib/backendApi");
        await backendApi.sendEmail({
          to: [config.user_email],
          subject: `[Klusgebied Feedback] Nieuwe feedback: ${formData.feedback_type}`,
          html: `<p>Nieuwe feedback ontvangen:</p><ul><li>Naam: ${formData.name}</li><li>Email: ${formData.email}</li><li>Type: ${formData.feedback_type}</li><li>Rating: ${formData.rating}/5</li><li>Bericht: ${formData.message}</li></ul>`,
        });
      } catch (error) {
        console.error("Error sending notification email:", error);
        emailSent = false;
      }

      // 3. Save to database
      const { error: insertError } = await supabase
        .from("feedback_submissions")
        .insert({
          ...formData,
          email_sent: emailSent,
          email_sent_at: new Date().toISOString(),
        });

      if (insertError) {
        throw new Error(`Database insert error: ${insertError.message}`);
      }

      setStatus("success");
      setFormData({
        name: "",
        email: "",
        feedback_type: "Suggestion",
        rating: 0,
        message: "",
      });
    } catch (err) {
      setStatus("error");
      setError(
        err.message || "Er is iets misgegaan. Probeer het later opnieuw."
      );
      console.error(err);
    }
  };

  return (
    <div className="bg-slate-50 min-h-screen flex flex-col">
      <main className="flex-grow">
        <section className="bg-teal-500 text-white pt-32 pb-20">
          <div className="container mx-auto px-4 text-center">
            <MessageSquare className="mx-auto h-16 w-16 mb-4" />
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              Geef ons uw feedback
            </h1>
            <p className="text-lg md:text-xl text-teal-100 max-w-3xl mx-auto">
              Uw mening is belangrijk voor ons. Help ons Klusgebied te
              verbeteren.
            </p>
          </div>
        </section>

        <section className="py-20">
          <div className="container mx-auto px-4 max-w-2xl">
            <div className="bg-white p-8 md:p-12 rounded-lg shadow-lg">
              <h2 className="text-xl md:text-2xl font-bold text-slate-800 mb-6 text-center">
                Deel uw ervaring
              </h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-slate-700 mb-1"
                    >
                      Naam
                    </label>
                    <input
                      type="text"
                      name="name"
                      id="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-slate-300 rounded-md focus:ring-teal-500 focus:border-teal-500"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-slate-700 mb-1"
                    >
                      Email *
                    </label>
                    <input
                      type="email"
                      name="email"
                      id="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-2 border border-slate-300 rounded-md focus:ring-teal-500 focus:border-teal-500"
                    />
                  </div>
                </div>
                <div>
                  <label
                    htmlFor="feedback_type"
                    className="block text-sm font-medium text-slate-700 mb-1"
                  >
                    Type feedback
                  </label>
                  <select
                    name="feedback_type"
                    id="feedback_type"
                    value={formData.feedback_type}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-slate-300 rounded-md focus:ring-teal-500 focus:border-teal-500"
                  >
                    <option>Suggestion</option>
                    <option>Bug Report</option>
                    <option>Compliment</option>
                    <option>Complaint</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Beoordeling
                  </label>
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3, 4, 5].map((rate) => (
                      <Star
                        key={rate}
                        className={`h-8 w-8 cursor-pointer transition-colors ${
                          formData.rating >= rate
                            ? "text-yellow-400"
                            : "text-slate-300"
                        }`}
                        onClick={() => handleRating(rate)}
                        fill={formData.rating >= rate ? "currentColor" : "none"}
                      />
                    ))}
                  </div>
                </div>
                <div>
                  <label
                    htmlFor="message"
                    className="block text-sm font-medium text-slate-700 mb-1"
                  >
                    Bericht *
                  </label>
                  <textarea
                    name="message"
                    id="message"
                    rows={5}
                    value={formData.message}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-slate-300 rounded-md focus:ring-teal-500 focus:border-teal-500"
                  ></textarea>
                </div>
                <div>
                  <button
                    type="submit"
                    disabled={status === "loading"}
                    className="w-full flex justify-center items-center bg-teal-500 text-white font-bold py-3 px-6 rounded-lg hover:bg-teal-600 transition-colors duration-300 disabled:bg-slate-400"
                  >
                    {status === "loading" ? (
                      <Loader className="animate-spin h-5 w-5" />
                    ) : (
                      <>
                        <Send className="h-5 w-5 mr-2" /> Feedback Verzenden
                      </>
                    )}
                  </button>
                </div>
                {status === "success" && (
                  <div className="text-green-600 flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Bedankt voor uw feedback!
                  </div>
                )}
                {status === "error" && (
                  <div className="text-red-600 flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2" />
                    {error}
                  </div>
                )}
              </form>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default FeedbackPage;
