/**
 * @description This component renders detailed service pages with comprehensive information, pricing, and professional listings for each service.
 * It dynamically displays content based on the service parameter from the URL, showing service descriptions, images, and call-to-action elements.
 * The component includes SEO-optimized content, responsive design, and animated elements for enhanced user engagement with service-specific targeting.
 * Key variables include serviceName from URL params, currentService object with service data, and professional listings for improved service targeting.
 */

import React, { useEffect, useState } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import {
  ArrowLeft,
  Star,
  CheckCircle,
  Clock,
  Euro,
  MapPin,
  Phone,
  Award,
  ShieldCheck,
  ThumbsUp,
  Wrench,
  Home,
  List,
  ArrowRight,
} from "lucide-react";
import Footer from "../../components/landing/Footer";
import servicesData from "../../lib/data/services.json";

const ServiceDetailPage = () => {
  const { serviceSlug } = useParams();
  const navigate = useNavigate();
  const [service, setService] = useState(null);
  const [relatedServices, setRelatedServices] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAllData = async () => {
      setLoading(true);
      setService(null);
      setRelatedServices([]);

      try {
        // Fetch current service
        const currentServiceData = servicesData.find(
          (item) => item.slug === serviceSlug
        );

        if (!currentServiceData) {
          console.error("Error fetching service:", currentServiceData);
          navigate("/");
          return;
        }

        if (
          currentServiceData.seo_title &&
          currentServiceData.seo_title.length > 60
        ) {
          console.warn(
            `SEO title for service "${currentServiceData.name}" is too long (${currentServiceData.seo_title.length} characters). Recommended max is 60.`
          );
        }
        if (
          currentServiceData.seo_description &&
          currentServiceData.seo_description.length > 155
        ) {
          console.warn(
            `SEO description for service "${currentServiceData.name}" is too long (${currentServiceData.seo_description.length} characters). Recommended max is 155.`
          );
        }

        const serviceData = {
          ...currentServiceData,
          faq: [
            {
              question: `Wat kost een ${currentServiceData.name.toLowerCase()}?`,
              answer:
                "De kosten hangen af van de omvang en complexiteit van de klus. Voor een precieze prijsopgave raden we aan om gratis en vrijblijvend een klus te plaatsen. U ontvangt dan offertes van onze geverifieerde vakmannen.",
            },
            {
              question: "Hoe snel kan een vakman beginnen?",
              answer:
                "Voor spoedklussen kan er vaak al binnen enkele uren een professional ter plaatse zijn. Voor reguliere projecten kunt u in overleg met de vakman een startdatum plannen die u het beste uitkomt.",
            },
            {
              question: "Zijn de aangesloten professionals betrouwbaar?",
              answer:
                "Ja, alle vakmannen op Klusgebied worden door ons gescreend. We controleren inschrijvingen bij de Kamer van Koophandel, certificeringen en reviews om de kwaliteit en betrouwbaarheid te waarborgen.",
            },
          ],
        };
        setService(serviceData);

        // Fetch related services
        const otherServices = servicesData.filter(
          (item) => item.slug !== serviceSlug
        );

        if (otherServices) {
          const sameCategory = otherServices.filter(
            (s) =>
              s.category === serviceData.category && s.slug !== serviceData.slug
          );
          const differentCategory = otherServices.filter(
            (s) =>
              s.category !== serviceData.category && s.slug !== serviceData.slug
          );

          const shuffledRelated = [...sameCategory, ...differentCategory]
            .sort(() => 0.5 - Math.random())
            .slice(0, 5);

          setRelatedServices(shuffledRelated);
        }
      } catch (error) {
        console.error("Error:", error);
        navigate("/");
      } finally {
        setLoading(false);
      }
    };

    if (serviceSlug) {
      fetchAllData();
    }
  }, [serviceSlug, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-500 mx-auto"></div>
          <p className="mt-4 text-slate-600">Laden...</p>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="min-h-screen bg-white">
        <div className="pt-20 pb-16 text-center">
          <h1 className="text-2xl font-bold text-slate-800 mb-4">
            Service niet gevonden
          </h1>
          <button
            onClick={() => navigate("/")}
            className="bg-teal-500 text-white px-6 py-3 rounded-xl font-semibold hover:bg-teal-600 transition-colors"
          >
            Terug naar home
          </button>
        </div>
        <Footer />
      </div>
    );
  }

  // Mock data for professionals (since we don't have a professionals table yet)
  const professionals = [
    {
      name: "Jan van der Berg",
      rating: 4.9,
      reviews: 127,
      experience: "8+ jaar ervaring",
      location: "Amsterdam",
      phone: "06-12345678",
      specialties: ["Spoedklussen", "Weekendservice"],
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.1.0&w=400&h=400",
    },
    {
      name: "Maria Janssen",
      rating: 4.8,
      reviews: 98,
      experience: "6+ jaar ervaring",
      location: "Utrecht",
      phone: "06-87654321",
      specialties: ["Garantie werk", "Gratis advies"],
      image:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.1.0&w=400&h=400",
    },
    {
      name: "Pieter de Vries",
      rating: 4.7,
      reviews: 156,
      experience: "10+ jaar ervaring",
      location: "Rotterdam",
      phone: "06-11223344",
      specialties: ["24/7 service", "Eigen materiaal"],
      image:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.1.0&w=400&h=400",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      {service && (
        <Helmet>
          <title>{service.seo_title || `${service.name} | Klusgebied`}</title>
          <meta
            name="description"
            content={
              service.seo_description ||
              `Vind snel en eenvoudig een geverifieerde ${service.name.toLowerCase()} in jouw regio.`
            }
          />
          <link
            rel="canonical"
            href={`https://www.klusgebied.nl/#/diensten/${service.slug}`}
          />
          <meta
            property="og:title"
            content={service.seo_title || `${service.name} | Klusgebied`}
          />
          <meta
            property="og:description"
            content={
              service.seo_description ||
              `Vind snel en eenvoudig een geverifieerde ${service.name.toLowerCase()} in jouw regio.`
            }
          />
          <meta
            property="og:image"
            content={
              service.image_url ||
              "https://heyboss.heeyo.ai/chat-images/ChatGPT%20Image%2025%20jun%202025,%2015_48_09_32j6tyj5.png"
            }
          />
          <meta name="twitter:card" content="summary_large_image" />
          <script type="application/ld+json">
            {JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Service",
              name: service.name,
              serviceType: service.name,
              description:
                service.seo_description ||
                `Vind snel en eenvoudig een geverifieerde ${service.name.toLowerCase()} in jouw regio.`,
              provider: {
                "@type": "LocalBusiness",
                name: "Klusgebied",
                image:
                  "https://heyboss.heeyo.ai/chat-images/ChatGPT%20Image%2025%20jun%202025,%2015_48_09_32j6tyj5.png",
                url: "https://www.klusgebied.nl/",
                telephone: "+31-85-1234567",
                priceRange: "€€",
                address: {
                  "@type": "PostalAddress",
                  streetAddress: "Kabelweg 43",
                  addressLocality: "Amsterdam",
                  postalCode: "1014 BA",
                  addressCountry: "NL",
                },
              },
              areaServed: {
                "@type": "Country",
                name: "Netherlands",
              },
            })}
          </script>
          {service.faq && service.faq.length > 0 && (
            <script type="application/ld+json">
              {JSON.stringify({
                "@context": "https://schema.org",
                "@type": "FAQPage",
                mainEntity: service.faq.map((q) => ({
                  "@type": "Question",
                  name: q.question,
                  acceptedAnswer: {
                    "@type": "Answer",
                    text: q.answer,
                  },
                })),
              })}
            </script>
          )}
        </Helmet>
      )}
      <div className="pt-20 pb-16 text-center">
        <h1 className="text-2xl font-bold text-slate-800 mb-4">
          Service niet gevonden
        </h1>
        <button
          onClick={() => navigate("/")}
          className="bg-teal-500 text-white px-6 py-3 rounded-xl font-semibold hover:bg-teal-600 transition-colors"
        >
          Terug naar home
        </button>
      </div>
      <Footer />
    </div>
  );
};

export default ServiceDetailPage;
