import { useState } from "react";
import { Link } from "react-router-dom";
import {
  CheckCircle,
  Phone,
  FileText,
  MapPin,
  Calendar,
  Info,
  Home,
  Wrench,
  Maximize,
  Shield,
  AlertTriangle,
} from "lucide-react";
import {
  ReactCompareSlider,
  ReactCompareSliderImage,
} from "react-compare-slider";
import ContactUsModal from "@/components/modal/ContactUsModal";
import FAQItem from "@/components/FAQItem";
import TestimonialCarousel from "@/components/TestimonialCarousel";

const DakkapelLanding = () => {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      rating: 5,
      shortText:
        "Onze dakkapel is perfect geplaatst! Binnen 2 dagen klaar en veel meer ruimte op zolder. Zeer professioneel team...",
      fullText:
        "Onze dakkapel is perfect geplaatst! Binnen 2 dagen klaar en veel meer ruimte op zolder. Zeer professioneel team dat alles netjes heeft afgewerkt. De vergunning werd ook geregeld. Absolute aanrader!",
      verified: true,
    },
    {
      id: 2,
      name: "Annemarie",
      rating: 5,
      shortText:
        "Geweldige service van A tot Z. Goede planning, kwaliteit materialen en vakkundig uitgevoerd. De dakkapel is prachtig...",
      fullText:
        "Geweldige service van A tot Z. Goede planning, kwaliteit materialen en vakkundig uitgevoerd. De dakkapel is prachtig geworden en geeft zoveel extra licht en ruimte. Zeer tevreden met het resultaat.",
      verified: true,
    },
    {
      id: 3,
      name: "Michiel",
      rating: 5,
      shortText:
        "Snelle plaatsing en uitstekende kwaliteit. De nieuwe dakkapel heeft onze zolder getransformeerd. Alles volgens planning...",
      fullText:
        "Snelle plaatsing en uitstekende kwaliteit. De nieuwe dakkapel heeft onze zolder getransformeerd. Alles volgens planning en binnen budget. Het team was zeer professioneel en heeft alles netjes opgeruimd.",
      verified: true,
    },
    {
      id: 4,
      name: "Saskia",
      rating: 5,
      shortText:
        "Perfecte dakkapel geplaatst! Van advies tot oplevering alles top geregeld. Goede communicatie en vakmanschap...",
      fullText:
        "Perfecte dakkapel geplaatst! Van advies tot oplevering alles top geregeld. Goede communicatie en vakmanschap. De specialisten hebben ons goed geadviseerd over het type dakkapel en de plaatsing. Zeer tevreden!",
      verified: true,
    },
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Contact Modal */}
      <ContactUsModal
        isOpen={isContactModalOpen}
        setIsOpen={setIsContactModalOpen}
        jobType="dakkapel-plaatsen-vervangen"
      />

      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 bg-white w-full z-50 shadow-sm">
        <div className="container mx-auto px-4 py-3 flex sm:flex-row flex-col gap-2 items-center justify-between">
          <Link to="/" className="flex gap-2 items-center">
            <img src="/logo.png" alt="Klusgebied Logo" className="w-12 h-12" />
            <h1 className="text-2xl font-bold tracking-wide">Klusgebied</h1>
          </Link>
          <div className="flex gap-4">
            <button
              onClick={() => setIsContactModalOpen(true)}
              className="sm:flex items-center hidden gap-2 bg-[#14213d] text-white px-4 py-2 rounded-md text-base tracking-wide"
            >
              <Phone size={18} />
              <span>Contact opnemen</span>
            </button>
            <button
              onClick={() => setIsContactModalOpen(true)}
              className="flex items-center gap-2 bg-[#40cfc1] text-white px-4 py-2 rounded-md text-base tracking-wide"
            >
              <FileText size={18} />
              <span>Vrijblijvende offerte</span>
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-80 relative">
        <div className="absolute inset-0 bg-gray-800 opacity-80 z-10" />
        <div className="relative z-10 container mx-auto px-4 pb-24 md:pb-32 text-center text-white">
          <h2 className="text-xl mb-2 tracking-wide leading-relaxed">
            Dakkapel verouderd, te klein of wil je er een laten plaatsen?
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-6"></div>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 max-w-4xl mx-auto tracking-wide leading-relaxed">
            Wij plaatsen en renoveren dakkapellen voor meer ruimte, licht en
            comfort in je woning
          </h1>
          <p className="max-w-2xl mx-auto mb-8 text-xl tracking-wide leading-relaxed">
            We zijn actief in heel Nederland en kunnen vaak al binnen enkele
            weken starten. Je krijgt 100% garantie en de zekerheid van
            gecertificeerde vakmensen.
          </p>
          <button
            onClick={() => setIsContactModalOpen(true)}
            className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-lg tracking-wide"
          >
            <Phone size={18} />
            <span>Contact opnemen</span>
          </button>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16 text-white">
            <div className="flex flex-col items-center">
              <MapPin className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Actief in heel Nederland
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Snelle plaatsing binnen 1-2 dagen
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Maatwerk en prefab oplossingen
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <CheckCircle className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Transparante tarieven zonder verrassingen
              </h3>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-gray-900">
          <img
            src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-arlindphotography-31480155.jpg"
            className="w-full h-full object-cover"
            alt="Dakkapel plaatsen"
          />
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-12 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <p className="text-gray-800 mb-6 text-lg tracking-wide leading-relaxed">
              Onze dakkapelspecialisten hebben ervaring met alle bekende
              materialen en merken.
            </p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-12">
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-1.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-2.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-3.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[500px] sm:h-[500px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-vlasceanu-19361853.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-arlindphotography-31480155.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Oude, te kleine of geen dakkapel?
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Een verouderde of ontbrekende dakkapel beperkt de mogelijkheden
                van je zolder. Weinig licht, beperkte hoogte en slechte
                ventilatie maken de ruimte oncomfortabel en onbruikbaar.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Met een nieuwe of gerenoveerde dakkapel transformeer je je
                zolder tot een volwaardige woonruimte. Meer licht, hoogte en
                frisse lucht maken het verschil tussen een stoffige bergzolder
                en een comfortabele kamer.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Een professioneel geplaatste dakkapel verhoogt niet alleen je
                wooncomfort, maar ook de waarde van je woning aanzienlijk.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Dakkapel Service
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-8"></div>
          <p className="text-center max-w-3xl mx-auto mb-12 text-lg tracking-wide leading-relaxed">
            Wij lossen alle dakkapel-uitdagingen op, van nieuwe plaatsing tot
            complete renovatie. Onze diensten zijn beschikbaar in heel
            Nederland.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Service Card 1 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Home
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Nieuwe dakkapel plaatsen
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Beschikbaar in hout, kunststof of onderhoudsarm zink. Levering
                als maatwerk of prefab. Volledig afgewerkt inclusief isolatie en
                binnenafwerking.
              </p>
            </div>

            {/* Service Card 2 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Wrench
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Dakkapel renoveren
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Vervang je oude kozijnen, dakbedekking, gevelbekleding of
                isolatie. Klaar voor de komende 30 jaar met moderne materialen.
              </p>
            </div>

            {/* Service Card 3 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Maximize
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Dakkapel uitbreiden
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Vergroot je bestaande dakkapel voor nog meer ruimte en licht.
                Inclusief draaikiepramen, rolluiken en ventilatieroosters.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            {/* Service Card 4 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Shield
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Dakkapel onderhoud
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Preventief onderhoud aan je dakkapel om lekkages en schade te
                voorkomen. Controle van dakbedekking, kozijnen en afvoer.
              </p>
            </div>

            {/* Service Card 5 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <AlertTriangle
                  className="w-[140px] h-[140px] text-black"
                  strokeWidth={1}
                />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Dakkapel reparatie
              </h3>
              <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                Snelle reparatie van lekkages, beschadigde kozijnen of
                dakbedekking. Vaak dezelfde dag nog opgelost voor minimale
                overlast.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-didsss-11174437.jpg"
                  className="w-[360px] h-[300px]"
                  alt="Dakkapel resultaat"
                />
              </div>
              <p className="text-base tracking-wide leading-relaxed">
                Een oude, lekkende dakkapel is volledig gerenoveerd met nieuwe
                kozijnen, dakbedekking en isolatie voor optimaal comfort.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-filiamariss-19059448.jpg"
                  className="w-[360px] h-[300px]"
                  alt="Dakkapel resultaat"
                />
              </div>
              <p className="text-base tracking-wide leading-relaxed">
                Een nieuwe prefab dakkapel geplaatst binnen één dag, inclusief
                binnenafwerking en HR++ beglazing voor maximale
                energie-efficiëntie.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-laura-tancredi-7078288.jpg"
                  className="w-[360px] h-[300px]"
                  alt="Dakkapel resultaat"
                />
              </div>
              <p className="text-base tracking-wide leading-relaxed">
                Een maatwerk houten dakkapel met rolluiken en draaikiepramen,
                perfect afgestemd op de architectuur van de woning.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 24/7 Service Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Snelle service – Direct hulp bij jouw dakkapel project
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Wij staan klaar voor je dakkapel project.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Of het nu gaat om een nieuwe dakkapel, renovatie of spoedige
                reparatie, we kunnen vaak binnen enkele weken starten.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Neem contact met ons op en we plannen een vrijblijvende
                inspectie in. Zo heb je snel meer ruimte en licht zonder gedoe.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Onze eigen gecertificeerde dakkapelspecialisten werken met een
                persoonlijke en vakkundige aanpak. Geen lange wachttijden,
                gewoon een snelle en professionele oplossing.
              </p>
              <p className="mb-6 text-lg tracking-wide leading-relaxed">
                Wil je meer weten over de mogelijkheden? Neem contact met ons
                op.
              </p>
              <button
                onClick={() => setIsContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-lg tracking-wide"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-laura-tancredi-7078294.jpg"
                  className="w-[530px] h-[353px] object-cover"
                  alt="Dakkapel service"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-leah-newhouse-50725-9493793.jpg"
                  className="w-[530px] h-[339px]"
                  alt="Over Klusgebied dakkapellen"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Over Klusgebied
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Bij Klusgebied zorgen we ervoor dat je dakkapel project altijd
                in goede handen is. Of het nu gaat om plaatsing, renovatie of
                onderhoud, wij verbinden je met lokaal gecertificeerde vakmensen
                die het vakkundig en snel oplossen.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                We geloven in service zonder gedoe. Onze specialisten staan
                klaar om net dat extra stapje te zetten, zodat jij helemaal
                tevreden bent. Dat zie je terug in onze 4.8 uit 5
                klantbeoordeling – tevreden klanten zijn voor ons de standaard.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Wil je een dakkapel laten plaatsen of renoveren? Wij maken het
                eenvoudig en zorgen dat je snel wordt geholpen door onze lokale
                expert bij jou in de buurt.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Project Examples Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold text-center mb-4 tracking-wide leading-relaxed">
              Voorbeelden van geplaatste dakkapellen
            </h2>
            <div className="w-20 h-1 bg-primary mx-auto mb-12"></div>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                  <img
                    src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-lebele-8227559.jpg"
                    alt="kunststof dakkapel laten plaatsen"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-xl font-semibold mb-2 tracking-wide leading-relaxed">
                  Prefab kunststof dakkapel
                </h3>
                <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                  Prefab kunststof dakkapel geplaatst binnen één dag
                </p>
              </div>
              <div className="text-center">
                <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                  <img
                    src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-oscar-sanchez197-12796232.jpg"
                    alt="dakkapel renovatie houten model"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-xl font-semibold mb-2 tracking-wide leading-relaxed">
                  Houten dakkapel renovatie
                </h3>
                <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                  Renovatie houten dakkapel inclusief nieuwe dakbedekking en
                  gevel
                </p>
              </div>
              <div className="text-center">
                <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                  <img
                    src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-radubradu-395822838-16291985.jpg"
                    alt="zinken dakkapel op maat"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-xl font-semibold mb-2 tracking-wide leading-relaxed">
                  Maatwerk zinken dakkapel
                </h3>
                <p className="text-gray-600 text-base tracking-wide leading-relaxed">
                  Maatwerk dakkapel met zinken afwerking en HR++ glas
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialCarousel testimonials={testimonials} />

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[550px] sm:h-[550px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-didsss-11174437.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-filiamariss-19059448.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-8 tracking-wide leading-relaxed">
                Jouw voordelen
              </h2>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Toegewijde en deskundige dakkapelspecialisten
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Snelle plaatsing binnen 1-2 dagen bij prefab
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Snelle en vakkundige klantenservice; elke vraag is welkom
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Uitstekend beoordeeld door klanten
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Transparante prijzen zonder verrassingen
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Prijzen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Pricing Card 1 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Prefab dakkapel
                </h3>
                <p className="text-lg tracking-wide">Kunststof</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Snelle plaatsing in 1 dag
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Onderhoudsarm materiaal
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      HR++ beglazing
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Inclusief isolatie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Binnenafwerking
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1">vanaf</span>
                    <span className="text-4xl font-bold">€3.500,-</span>
                  </div>
                  <button
                    onClick={() => setIsContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 2 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Maatwerk dakkapel
                </h3>
                <p className="text-lg tracking-wide">Hout</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Volledig op maat gemaakt
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Uitstraling naar wens
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Duurzaam hout
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      HR++ beglazing
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Inclusief isolatie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Binnenafwerking
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Plaatsing in 2-4 dagen
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1">vanaf</span>
                    <span className="text-4xl font-bold">€5.000,-</span>
                  </div>
                  <button
                    onClick={() => setIsContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 3 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">Renovatie</h3>
                <p className="text-lg tracking-wide">Bestaande dakkapel</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Nieuwe kozijnen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Nieuwe dakbedekking
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Verbeterde isolatie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Nieuwe afwerking
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      HR++ beglazing
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Klaar voor 30 jaar
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="flex items-center text-base tracking-wide leading-relaxed">
                      Waterdichte afwerking
                      <Info size={16} className="ml-1 text-gray-400" />
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1">vanaf</span>
                    <span className="text-4xl font-bold">€2.500,-</span>
                  </div>
                  <button
                    onClick={() => setIsContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Veelgestelde vragen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <FAQItem
              question="Heb ik een vergunning nodig voor een dakkapel?"
              answer="Tot 1,75 meter hoog en aan de achterzijde vaak niet, maar we controleren het altijd voor je. Voor grotere dakkapellen of aan de voorzijde is meestal wel een vergunning nodig. Wij helpen je graag met het aanvragen."
            />
            <FAQItem
              question="Hoe lang duurt de plaatsing van een dakkapel?"
              answer="Een prefab dakkapel staat vaak binnen één dag. Maatwerk duurt ca. 2–4 dagen, afhankelijk van de grootte en complexiteit. Renovatie van een bestaande dakkapel duurt meestal 1-2 dagen."
            />
            <FAQItem
              question="Kunnen jullie mijn huidige dakkapel renoveren?"
              answer="Ja, wij vervangen kozijnen, dakbedekking, bekleding en isolatie. Een renovatie is vaak voordeliger dan volledige vervanging en geeft je dakkapel weer jaren mee."
            />
            <FAQItem
              question="Zit er garantie op het werk?"
              answer="Ja, wij bieden standaard 10 jaar garantie op plaatsing en materialen. Voor de dakbedekking geldt vaak een langere fabrieksgarantie."
            />
            <FAQItem
              question="Werken jullie ook met spoed?"
              answer="In overleg kunnen we vaak binnen enkele weken starten, afhankelijk van model en locatie. Voor spoedgevallen zoals lekkages proberen we zo snel mogelijk te helpen."
            />
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Neem nu contact met ons op voor jouw dakkapel project
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-6 text-lg tracking-wide leading-relaxed">
                Onze dakkapel experts zijn bereikbaar en kunnen vaak binnen
                enkele weken starten met jouw project. Klik op de onderstaande
                knop om contact op te nemen.
              </p>
              <button
                onClick={() => setIsContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-lg tracking-wide"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-64 h-64 rounded-full overflow-hidden">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Dakkapel/pexels-vasily-kleymenov-137433939-10903196.jpg"
                  alt="Dakkapel specialist"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
          <div className="text-center mt-4 text-base italic tracking-wide leading-relaxed">
            Jan van der Berg – dakkapel-expert in heel Nederland, geselecteerd
            door Klusgebied.
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 justify-center">
            <div>
              <div className="flex items-center mb-4 gap-2">
                <img
                  src="/logo.png"
                  alt="Klusgebied Logo"
                  className="w-12 h-12"
                />
                <h3 className="text-xl font-bold tracking-wide">Klusgebied</h3>
              </div>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Plaats vandaag nog je klus en ontvang gratis offertes van
                zorgvuldig geselecteerde vakmensen bij jou in de buurt! Binnen
                no-time staat een betrouwbare professional voor je klaar om de
                klus vakkundig uit te voeren. Laat het werk met een gerust hart
                uit handen nemen.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4 tracking-wide">Contact</h3>
              <ul className="space-y-2">
                <li>
                  <p className="font-medium text-base tracking-wide">
                    Klusgebied
                  </p>
                </li>
                <li>
                  <p className="text-base tracking-wide">Slotermeerlaan 58</p>
                </li>
                <li>
                  <p className="text-base tracking-wide">1064 HC Amsterdam</p>
                </li>
                <li>
                  <p className="text-base tracking-wide">KVK: 93475101</p>
                </li>
                <li>
                  <p className="text-base tracking-wide"><EMAIL></p>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-base text-gray-400 tracking-wide">
            <p>
              &copy; 2025 - Alle rechten voorbehouden -{" "}
              <a href="#" className="hover:text-[#40cfc1]">
                Privacyverklaring
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default DakkapelLanding;
