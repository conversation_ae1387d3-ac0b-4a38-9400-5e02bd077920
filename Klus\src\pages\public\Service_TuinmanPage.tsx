/**
 * @description This component renders a comprehensive and SEO-optimized detail page for gardener services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for gardeners. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  ShieldCheck,
  Clock,
  ArrowRight,
  Star,
  Edit3,
  MessageS<PERSON>re,
  <PERSON>r<PERSON>heck,
  CheckCircle,
  MapPin,
  Euro,
  TreePine,
  Leaf,
  Scissors,
  Grip,
  Flower2,
} from "lucide-react";

const Service_TuinmanPage = () => {
  usePageTitle("Tuinman Nodig? | Klusgebied - Tuinonderhoud & Tuinaanleg");
  const navigate = useNavigate();

  const gardenerServices = [
    {
      icon: Leaf,
      title: "Tuinonderhoud",
      description:
        "Regulier onderhoud om uw tuin het hele jaar mooi te houden.",
      points: [
        "Grasmaaien, onkruid wieden en bemesten.",
        "Seizoensgebonden onderhoudscontracten.",
        "Hagen en struiken snoeien voor een verzorgde look.",
        "Advies voor een gezonde en bloeiende tuin.",
      ],
    },
    {
      icon: Flower2,
      title: "Tuinaanleg & Ontwerp",
      description: "Complete nieuwe tuinen ontwerpen en aanleggen.",
      points: [
        "Van tuinontwerp tot volledige realisatie.",
        "Aanleg van gazons, borders en beplanting.",
        "Advies over plantenkeuze en duurzaamheid.",
        "Creëer uw droomtuin samen met onze experts.",
      ],
    },
    {
      icon: Scissors,
      title: "Snoeien & Kappen",
      description: "Professioneel snoeien van bomen en struiken.",
      points: [
        "Vakkundig snoeien voor optimale groei en bloei.",
        "Veilig kappen en verwijderen van bomen.",
        "Advies over de beste snoeiperiode.",
        "Inclusief afvoer van groenafval.",
      ],
    },
    {
      icon: Grip,
      title: "Bestrating & Terrassen",
      description: "Aanleg van paden, terrassen en sfeervolle zithoeken.",
      points: [
        "Aanleg van terrassen, tuinpaden en opritten.",
        "Keuze uit diverse materialen zoals tegels en klinkers.",
        "Sierbestrating voor een unieke uitstraling.",
        "Zorgt voor een solide en duurzame basis.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "Flexibele Planning",
      description:
        "Onderhoud op vaste tijden of op afroep, wat u het beste uitkomt.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Ervaren Tuinmannen",
      description:
        "Onze hoveniers hebben groene vingers en passie voor hun vak.",
    },
    {
      icon: <Leaf className="w-8 h-8 text-white" />,
      title: "Duurzaam Tuinieren",
      description:
        "Wij werken met respect voor de natuur en geven advies over milieuvriendelijke oplossingen.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een tuinman per uur?",
      answer:
        "Het uurtarief van een tuinman ligt gemiddeld tussen de €45 en €65, afhankelijk van de ervaring en de aard van de werkzaamheden. Voor grotere projecten zoals tuinaanleg wordt vaak een totaalprijs afgesproken.",
    },
    {
      question: "Wanneer is de beste tijd om de tuin aan te leggen?",
      answer:
        "Het voorjaar (maart-mei) en het najaar (september-november) zijn de beste periodes voor tuinaanleg. De bodem is dan vochtig en de temperaturen zijn mild, wat ideaal is voor nieuwe planten.",
    },
    {
      question: "Doen jullie ook aan tuinontwerp?",
      answer:
        "Jazeker! Onze tuinmannen kunnen u helpen met een compleet tuinontwerp dat aansluit bij uw wensen, de stijl van uw huis en de ligging van uw tuin.",
    },
    {
      question: "Hoe vaak moet mijn tuin onderhouden worden?",
      answer:
        "Dit hangt af van het type tuin en uw wensen. Een gazon heeft regelmatig onderhoud nodig, terwijl een onderhoudsvriendelijke tuin misschien genoeg heeft aan een paar keer per jaar. We bieden flexibele onderhoudscontracten aan.",
    },
    {
      question: "Verwijderen jullie ook het groenafval?",
      answer:
        "Ja, na snoei- en onderhoudswerkzaamheden zorgen wij ervoor dat al het groenafval netjes wordt opgeruimd en afgevoerd.",
    },
    {
      question: "Kan ik ook een onderhoudscontract afsluiten?",
      answer:
        "Ja, wij bieden diverse onderhoudscontracten aan, van wekelijks tot seizoensgebonden onderhoud. Zo bent u het hele jaar door verzekerd van een prachtige tuin.",
    },
  ];

  const reviews = [
    {
      name: "Linda Groen",
      location: "Amstelveen",
      rating: 5,
      quote:
        "Onze verwilderde tuin is omgetoverd tot een paradijs! De tuinman dacht creatief mee en het resultaat is boven verwachting.",
      highlighted: true,
    },
    {
      name: "Mark de Boer",
      location: "Haarlem",
      rating: 5,
      quote:
        "Zeer tevreden over het reguliere onderhoud. Onze tuin heeft er nog nooit zo strak bijgelegen. Betrouwbaar en professioneel.",
      highlighted: false,
    },
    {
      name: "Familie Janssen",
      location: "Breda",
      rating: 4,
      quote:
        "De nieuwe bestrating van ons terras is prachtig gelegd. Goede communicatie en een eerlijke prijs.",
      highlighted: false,
    },
    {
      name: "Sophie van Vliet",
      location: "Leiden",
      rating: 5,
      quote:
        "De bomen zijn vakkundig gesnoeid en al het afval is netjes meegenomen. Top service!",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1558904541-efa843a96f01?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1747659629851-a92bd71149f6?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxnYXJkZW5lciUyQyUyMGxhbmRzY2FwaW5nJTJDJTIwcHJvZmVzc2lvbmFsJTIwZ2FyZGVuaW5nfGVufDB8fHx8MTc1MjE4NDQyM3ww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description:
        "Beschrijf je tuinwensen. Voeg foto's toe voor een duidelijker beeld.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Vakmannen reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde tuinmannen.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & geniet",
      description:
        "Vergelijk profielen en kies de beste hovenier. Plan de klus en geniet van je tuin!",
      microcopy: "Vergelijk profielen en beoordelingen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Tuinmannen in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "tuinman",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>Tuinman Nodig? | Klusgebied - Tuinonderhoud & Tuinaanleg</title>
        <meta
          name="description"
          content="Vind een betrouwbare tuinman voor tuinonderhoud, tuinaanleg, snoeien en bestrating. Plaats gratis je klus en ontvang offertes van geverifieerde hoveniers in jouw regio."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Tuinman",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.8",
              reviewCount: "215",
            },
            review: reviews.map((review) => ({
              "@type": "Review",
              author: { "@type": "Person", name: review.name },
              reviewRating: {
                "@type": "Rating",
                ratingValue: review.rating,
                bestRating: "5",
              },
              reviewBody: review.quote,
            })),
            offers: {
              "@type": "Offer",
              priceCurrency: "EUR",
              priceSpecification: {
                "@type": "PriceSpecification",
                priceCurrency: "EUR",
                minPrice: "45",
                maxPrice: "65",
                valueAddedTaxIncluded: true,
                unitText: "HOUR",
                description:
                  "Standaardtarief per uur. Projectprijzen op aanvraag.",
              },
            },
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            mainEntity: faqs.map((faq) => ({
              "@type": "Question",
              name: faq.question,
              acceptedAnswer: {
                "@type": "Answer",
                text: faq.answer,
              },
            })),
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-green-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-green-100 border border-green-200/80 rounded-full px-4 py-2 mb-6 pl-[16px] pr-[14px]">
                    <TreePine className="w-5 h-5 text-green-600" />
                    <span className="text-green-800 font-semibold text-sm">
                      Tuinman Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Tuinman nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-emerald-500 mt-[10px] !pt-[9px] !pb-[9px]">
                      Groene vingers, strak resultaat
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-[226px] mt-[12px]">
                    Voor professioneel tuinonderhoud, tuinaanleg en advies. Maak
                    van uw tuin een groene oase.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate("/plaats-een-klus/tuin-bestrating")
                      }
                      className="group inline-flex items-center justify-center bg-green-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw tuinman
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.8/5</span>
                    <span>gebaseerd op 215 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Professionele tuinman aan het werk in een prachtige tuin"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte hovenier voor jouw tuin.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-green-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Tuinman Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Van onderhoud tot complete herinrichting van uw buitenruimte.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {gardenerServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-green-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-green-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een tuinman via Klusgebied?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                Bij Klusgebied betaal je een eerlijke prijs voor ervaren
                hoveniers. Je ontvangt altijd eerst een duidelijk prijsvoorstel.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Uurtarief:{" "}
                    <strong className="text-slate-900">€45–€65</strong> (incl.
                    BTW)
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <span>Projectprijs: Op aanvraag voor tuinaanleg/ontwerp</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Onderhoudscontract: Vanaf{" "}
                    <strong className="text-slate-900">€50</strong> per maand
                  </span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Geen verrassingen. Altijd een heldere afspraak.
              </div>
              <button
                onClick={() => navigate("/plaats-een-klus/tuin-bestrating")}
                className="bg-green-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een tuinman vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-green-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-green-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-green-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-green-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center mt-12">
              <a
                href="#"
                className="text-green-600 font-semibold hover:underline"
              >
                Bekijk alle beoordelingen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                Waarom kiezen voor Klusgebied?
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Betrouwbare hoveniers die staan voor kwaliteit en een prachtige
                tuin.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/over-ons")}
                className="text-green-300 font-semibold hover:text-white transition-colors"
              >
                Lees meer over onze missie{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </button>
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-green-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-green-500 to-emerald-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar om uw tuin te transformeren?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Plaats uw klus en ontvang snel reacties van de beste hoveniers bij
              u in de buurt.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/tuin-bestrating")}
              className="bg-white text-green-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je tuinklus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/tuin-bestrating")}
          className="w-full group inline-flex items-center justify-center bg-green-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-green-600 transition-all duration-300 shadow-lg hover:shadow-green-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_TuinmanPage;
