export type Message = {
  Row: {
    content: string
    created_at: string
    id: string
    job_id: string
    read: boolean | null
    receiver_id: string
    sender_id: string
  }
  Insert: {
    content: string
    created_at?: string
    id?: string
    job_id: string
    read?: boolean | null
    receiver_id: string
    sender_id: string
  }
  Update: {
    content?: string
    created_at?: string
    id?: string
    job_id?: string
    read?: boolean | null
    receiver_id?: string
    sender_id?: string
  }
  Relationships: [
    {
      foreignKeyName: "messages_job_id_fkey"
      columns: ["job_id"]
      isOneToOne: false
      referencedRelation: "jobs"
      referencedColumns: ["id"]
    },
    {
      foreignKeyName: "messages_receiver_id_fkey"
      columns: ["receiver_id"]
      isOneToOne: false
      referencedRelation: "profiles"
      referencedColumns: ["id"]
    },
    {
      foreignKeyName: "messages_sender_id_fkey"
      columns: ["sender_id"]
      isOneToOne: false
      referencedRelation: "profiles"
      referencedColumns: ["id"]
    }
  ]
}