import { LayoutDashboard } from "lucide-react";

interface DashboardHeaderProps {
  fullName?: string;
  firstName?: string;
  lastName?: string;
}

export const DashboardHeader = ({ firstName }: DashboardHeaderProps) => {
  const capitalizeFirstLetter = (string?: string) => {
    if (!string) return "";
    return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
  };

  return (
    <div className="flex items-center gap-4 bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 p-6 rounded-lg shadow-sm mb-8">
      <div className="p-3 bg-primary/10 rounded-full">
        <LayoutDashboard className="h-8 w-8 text-primary" />
      </div>
      <div className="flex flex-col items-start">
        <p className="text-sm text-muted-foreground">Welkom terug</p>
        <h2 className="text-3xl font-bold text-accent dark:text-white">
          {capitalizeFirstLetter(firstName) || "Klusaanvrager"}
        </h2>
      </div>
    </div>
  );
};
