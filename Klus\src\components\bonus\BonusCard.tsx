import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

interface BonusCardProps {
  item: {
    title: string;
    description: string;
    icon: React.ReactNode;
    reward: string;
    details: string[];
    challenge: string;
    tips: string[];
    progress: number;
    target: number;
    nextMilestone: string;
    gradient?: string; // Made gradient optional with ?
  };
}

export const BonusCard = ({ item }: BonusCardProps) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Card
          role="button"
          aria-haspopup="dialog"
          aria-expanded="false"
          className="p-4 sm:p-6 hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:-translate-y-1 hover:bg-gray-50 dark:hover:bg-gray-800"
        >
          <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
              {item.icon}
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {item.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                {item.description}
              </p>
              <div className="space-y-3">
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      Voortgang naar {item.nextMilestone}
                    </span>
                    <span className="text-gray-900 dark:text-gray-100 font-medium">
                      {item.progress}/{item.target}
                    </span>
                  </div>
                  <Progress
                    value={(item.progress / item.target) * 100}
                    className="h-2"
                  />
                </div>
                <p className="text-sm font-medium text-primary">
                  Beloning: {item.reward}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <span className="p-2 bg-gray-100 rounded-lg">{item.icon}</span>
            {item.title}
          </DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[calc(90vh-8rem)] pr-4">
          <div className="space-y-4 sm:space-y-6">
            <div>
              <h4 className="font-semibold text-lg mb-2">Voortgang</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">
                    Voortgang naar {item.nextMilestone}
                  </span>
                  <span className="text-gray-900 dark:text-gray-100 font-medium">
                    {item.progress}/{item.target}
                  </span>
                </div>
                <Progress
                  value={(item.progress / item.target) * 100}
                  className="h-3"
                />
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-2">De Uitdaging</h4>
              <p className="text-gray-600">{item.challenge}</p>
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-2">Beloningen</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-600">
                {item.details.map((detail, idx) => (
                  <li key={idx}>{detail}</li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-lg mb-2">Tips voor Succes</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-600">
                {item.tips.map((tip, idx) => (
                  <li key={idx}>{tip}</li>
                ))}
              </ul>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};
