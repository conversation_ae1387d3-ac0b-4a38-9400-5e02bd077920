import { supabase } from '@/integrations/supabase/client';

// Backend API configuration
const BACKEND_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3000';

class BackendApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = BACKEND_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async getAuthToken(): Promise<string | null> {
    const { data: { session } } = await supabase.auth.getSession();
    return session?.access_token || null;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = await this.getAuthToken();
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  private async makeFormRequest<T>(
    endpoint: string,
    formData: FormData
  ): Promise<T> {
    const token = await this.getAuthToken();
    
    const headers: HeadersInit = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  // Email API
  async sendEmail(data: {
    to: string[];
    subject: string;
    html: string;
  }) {
    return this.makeRequest('/api/communications/email/send', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // SMS API
  async sendSms(data: {
    to: string;
    content: string;
  }) {
    return this.makeRequest('/api/communications/sms/send', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Payment API
  async createMolliePayment(data: {
    amount: number;
    userId: string;
    transactionId: string;
    description: string;
    redirectUrl: string;
    webhookUrl: string;
  }) {
    return this.makeRequest<{ paymentUrl: string; paymentId: string }>('/api/payments/mollie/create', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async createMollieTestPayment(data: {
    amount: number;
    userId: string;
    transactionId: string;
    description: string;
    redirectUrl: string;
    webhookUrl: string;
  }) {
    return this.makeRequest<{ paymentUrl: string; paymentId: string }>('/api/payments/mollie/create-test', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Utilities API
  async verifyRecaptcha(token: string) {
    return this.makeRequest('/api/utilities/recaptcha/verify', {
      method: 'POST',
      body: JSON.stringify({ token }),
    });
  }

  async geocodeAddress(address: string) {
    return this.makeRequest<{ lat: number; lng: number }>('/api/utilities/geocode', {
      method: 'POST',
      body: JSON.stringify({ address }),
    });
  }

  async uploadChatAttachment(formData: FormData) {
    return this.makeFormRequest<{ message: string; fileUrl: string }>('/api/utilities/chat/upload', formData);
  }

  // Admin API
  async createAdminUser(data: {
    email: string;
    first_name: string;
    last_name: string;
    user_type: 'vakman' | 'klusaanvrager' | 'admin';
  }) {
    return this.makeRequest('/api/users/admin/create', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async deleteAdminUser(id: string) {
    return this.makeRequest(`/api/users/admin/${id}`, {
      method: 'DELETE',
    });
  }

  async deleteUser(id: string) {
    return this.makeRequest(`/api/users/${id}`, {
      method: 'DELETE',
    });
  }

  async sendPasswordReset(email: string) {
    return this.makeRequest('/api/users/password-reset', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  // Cron Jobs API
  async scheduleCronJob(data: {
    job_name: string;
    cron_expression: string;
    command: string;
  }) {
    return this.makeRequest('/api/cron/schedule', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async unscheduleCronJob(job_name: string) {
    return this.makeRequest('/api/cron/unschedule', {
      method: 'DELETE',
      body: JSON.stringify({ job_name }),
    });
  }

  async getCronJobSchedule(job_name: string) {
    return this.makeRequest('/api/cron/schedule/get', {
      method: 'POST',
      body: JSON.stringify({ job_name }),
    });
  }

  // HubSpot API
  async syncHubspot() {
    return this.makeRequest('/api/hubspot/sync', {
      method: 'POST',
    });
  }

  async triggerHubspotSync() {
    return this.makeRequest('/api/hubspot/trigger-sync', {
      method: 'POST',
    });
  }

  // PDF API
  async generateTransactionPdf(transactionId: string): Promise<Blob> {
    const token = await this.getAuthToken();
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`${this.baseUrl}/api/pdf/transaction`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ transactionId }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.blob();
  }
}

// Export singleton instance
export const backendApi = new BackendApiClient();

// Export types for better TypeScript support
export type EmailData = Parameters<typeof backendApi.sendEmail>[0];
export type PaymentData = Parameters<typeof backendApi.createMolliePayment>[0];
export type GeocodeResult = { lat: number; lng: number };
export type UploadResult = { message: string; fileUrl: string };
