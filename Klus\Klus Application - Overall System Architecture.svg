<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1953.765625 884" style="max-width: 1953.765625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512"><style>#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .error-icon{fill:#a44141;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .edge-thickness-normal{stroke-width:1px;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .marker.cross{stroke:lightgrey;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 p{margin:0;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .cluster-label text{fill:#F9FFFE;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .cluster-label span{color:#F9FFFE;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .cluster-label span p{background-color:transparent;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .label text,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 span{fill:#ccc;color:#ccc;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .node rect,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .node circle,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .node ellipse,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .node polygon,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .rough-node .label text,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .node .label text,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .image-shape .label,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .icon-shape .label{text-anchor:middle;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .rough-node .label,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .node .label,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .image-shape .label,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .icon-shape .label{text-align:center;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .node.clickable{cursor:pointer;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .arrowheadPath{fill:lightgrey;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .cluster text{fill:#F9FFFE;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .cluster span{color:#F9FFFE;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 rect.text{fill:none;stroke-width:0;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .icon-shape,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .icon-shape p,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .icon-shape rect,#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph3" class="cluster"><rect height="128" width="1150.453125" y="748" x="8" style=""></rect><g transform="translate(521.859375, 748)" class="cluster-label"><foreignObject height="24" width="122.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="128" width="767.3125" y="748" x="1178.453125" style=""></rect><g transform="translate(1507.28125, 748)" class="cluster-label"><foreignObject height="24" width="109.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Database Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="384" width="1749.65625" y="314" x="103.1484375" style=""></rect><g transform="translate(925.9765625, 314)" class="cluster-label"><foreignObject height="24" width="104"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Backend Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="256" width="328.671875" y="8" x="759.40625" style=""></rect><g transform="translate(869.3203125, 8)" class="cluster-label"><foreignObject height="24" width="108.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M923.742,111L923.742,115.167C923.742,119.333,923.742,127.667,923.742,135.333C923.742,143,923.742,150,923.742,153.5L923.742,157"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M923.742,239L923.742,243.167C923.742,247.333,923.742,255.667,923.742,264C923.742,272.333,923.742,280.667,923.742,289C923.742,297.333,923.742,305.667,923.742,313.333C923.742,321,923.742,328,923.742,331.5L923.742,335"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M923.742,417L923.742,421.167C923.742,425.333,923.742,433.667,923.742,441.333C923.742,449,923.742,456,923.742,459.5L923.742,463"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M923.742,545L923.742,549.167C923.742,553.333,923.742,561.667,923.742,569.333C923.742,577,923.742,584,923.742,587.5L923.742,591"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M1018.203,649.311L1068.267,657.426C1118.331,665.541,1218.458,681.77,1268.522,694.052C1318.586,706.333,1318.586,714.667,1318.586,723C1318.586,731.333,1318.586,739.667,1318.586,747.333C1318.586,755,1318.586,762,1318.586,765.5L1318.586,769"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_G_5" d="M1018.203,643.287L1110.951,652.406C1203.698,661.525,1389.193,679.762,1481.94,693.048C1574.688,706.333,1574.688,714.667,1574.688,723C1574.688,731.333,1574.688,739.667,1574.688,747.333C1574.688,755,1574.688,762,1574.688,765.5L1574.688,769"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_H_6" d="M1018.203,640.759L1151.538,650.299C1284.872,659.839,1551.542,678.92,1684.876,692.626C1818.211,706.333,1818.211,714.667,1818.211,723C1818.211,731.333,1818.211,739.667,1818.211,747.333C1818.211,755,1818.211,762,1818.211,765.5L1818.211,769"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_I_7" d="M829.281,641.745L714.926,651.121C600.57,660.496,371.859,679.248,257.504,692.791C143.148,706.333,143.148,714.667,143.148,723C143.148,731.333,143.148,739.667,143.148,747.333C143.148,755,143.148,762,143.148,765.5L143.148,769"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_J_8" d="M829.281,644.944L752.954,653.786C676.628,662.629,523.974,680.315,447.647,693.324C371.32,706.333,371.32,714.667,371.32,723C371.32,731.333,371.32,739.667,371.32,747.333C371.32,755,371.32,762,371.32,765.5L371.32,769"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_K_9" d="M829.281,651.91L788.767,659.592C748.253,667.273,667.224,682.637,626.71,694.485C586.195,706.333,586.195,714.667,586.195,723C586.195,731.333,586.195,739.667,586.195,747.333C586.195,755,586.195,762,586.195,765.5L586.195,769"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_L_10" d="M855.126,673L847.795,677.167C840.464,681.333,825.802,689.667,818.471,698C811.141,706.333,811.141,714.667,811.141,723C811.141,731.333,811.141,739.667,811.141,747.333C811.141,755,811.141,762,811.141,765.5L811.141,769"></path><path marker-end="url(#mermaid-b9b073b9-ad0f-49de-b4c5-2d5a7ecc9512_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_M_11" d="M992.359,673L999.69,677.167C1007.02,681.333,1021.682,689.667,1029.013,698C1036.344,706.333,1036.344,714.667,1036.344,723C1036.344,731.333,1036.344,739.667,1036.344,747.333C1036.344,755,1036.344,762,1036.344,765.5L1036.344,769"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(923.7421875, 72)" id="flowchart-A-231" class="node default"><rect height="78" width="181.40625" y="-39" x="-90.703125" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-60.703125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="121.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>React Frontend<br/>TypeScript + Vite</p></span></div></foreignObject></g></g><g transform="translate(923.7421875, 200)" id="flowchart-B-232" class="node default"><rect height="78" width="258.671875" y="-39" x="-129.3359375" style="" class="basic label-container"></rect><g transform="translate(-99.3359375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="198.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Backend API Client<br/>Centralized Communication</p></span></div></foreignObject></g></g><g transform="translate(923.7421875, 378)" id="flowchart-C-233" class="node default"><rect height="78" width="207.03125" y="-39" x="-103.515625" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-73.515625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="147.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Express.js Server<br/>Node.js + TypeScript</p></span></div></foreignObject></g></g><g transform="translate(923.7421875, 506)" id="flowchart-D-234" class="node default"><rect height="78" width="253.46875" y="-39" x="-126.734375" style="" class="basic label-container"></rect><g transform="translate(-96.734375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="193.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Authentication Middleware<br/>JWT Validation</p></span></div></foreignObject></g></g><g transform="translate(923.7421875, 634)" id="flowchart-E-235" class="node default"><rect height="78" width="188.921875" y="-39" x="-94.4609375" style="" class="basic label-container"></rect><g transform="translate(-64.4609375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="128.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Routes<br/>RESTful Endpoints</p></span></div></foreignObject></g></g><g transform="translate(1318.5859375, 812)" id="flowchart-F-236" class="node default"><rect height="78" width="210.265625" y="-39" x="-105.1328125" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-75.1328125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="150.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Supabase PostgreSQL<br/>Primary Database</p></span></div></foreignObject></g></g><g transform="translate(1574.6875, 812)" id="flowchart-G-237" class="node default"><rect height="78" width="201.9375" y="-39" x="-100.96875" style="" class="basic label-container"></rect><g transform="translate(-70.96875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="141.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Supabase Auth<br/>User Authentication</p></span></div></foreignObject></g></g><g transform="translate(1818.2109375, 812)" id="flowchart-H-238" class="node default"><rect height="78" width="185.109375" y="-39" x="-92.5546875" style="" class="basic label-container"></rect><g transform="translate(-62.5546875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="125.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Supabase Storage<br/>File Storage</p></span></div></foreignObject></g></g><g transform="translate(143.1484375, 812)" id="flowchart-I-239" class="node default"><rect height="78" width="200.296875" y="-39" x="-100.1484375" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-70.1484375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="140.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Mollie API<br/>Payment Processing</p></span></div></foreignObject></g></g><g transform="translate(371.3203125, 812)" id="flowchart-J-240" class="node default"><rect height="78" width="156.046875" y="-39" x="-78.0234375" style="" class="basic label-container"></rect><g transform="translate(-48.0234375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Resend API<br/>Email Service</p></span></div></foreignObject></g></g><g transform="translate(586.1953125, 812)" id="flowchart-K-241" class="node default"><rect height="78" width="173.703125" y="-39" x="-86.8515625" style="" class="basic label-container"></rect><g transform="translate(-56.8515625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="113.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MessageBird API<br/>SMS Service</p></span></div></foreignObject></g></g><g transform="translate(811.140625, 812)" id="flowchart-L-242" class="node default"><rect height="78" width="176.1875" y="-39" x="-88.09375" style="" class="basic label-container"></rect><g transform="translate(-58.09375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="116.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Google Maps API<br/>Geocoding</p></span></div></foreignObject></g></g><g transform="translate(1036.34375, 812)" id="flowchart-M-243" class="node default"><rect height="78" width="174.21875" y="-39" x="-87.109375" style="" class="basic label-container"></rect><g transform="translate(-57.109375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="114.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HubSpot API<br/>CRM Integration</p></span></div></foreignObject></g></g></g></g></g></svg>