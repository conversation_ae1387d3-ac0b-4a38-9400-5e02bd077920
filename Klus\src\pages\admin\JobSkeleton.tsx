import { Briefcase } from "lucide-react";

import { Card } from "@/components/ui/card";

export const JobSkeleton = () => {
  return (
    <Card className="group overflow-hidden transition-all duration-1500 hover:shadow-lg bg-card border-border/30 animate-pulse">
      <div className="flex flex-col h-full">
        <div className="flex flex-col">
          <div className="px-6 pt-6">
            <div className="h-6 bg-muted rounded-md w-3/4" />
          </div>
          <div className="relative mt-4">
            <div className="h-48 bg-muted flex items-center justify-center">
              <Briefcase className="w-10 h-10 text-muted-foreground/20" />
            </div>
            <div className="absolute top-0 right-0 p-4">
              <div className="h-7 w-24 bg-muted rounded-full" />
            </div>
          </div>
        </div>
        <div className="p-6 flex flex-col flex-grow space-y-4">
          <div className="space-y-2">
            <div className="h-4 bg-muted rounded w-full" />
            <div className="h-4 bg-muted rounded w-4/5" />
          </div>
          <div className="flex sm:flex-row justify-between gap-2 sm:gap-4 flex-col mt-auto">
            <div className="h-10 bg-muted rounded w-full" />
          </div>
        </div>
      </div>
    </Card>
  );
};
