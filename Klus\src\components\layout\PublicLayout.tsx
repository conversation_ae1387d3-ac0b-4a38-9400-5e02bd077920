import { Link, useLocation } from "react-router-dom"; // <-- Import useLocation
import { Menu } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next"; // <-- 1. Import hook

import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuItem,
} from "@/components/ui/navigation-menu";
import { Button } from "@/components/ui/button";
import {
  <PERSON>et,
  <PERSON>etContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { ADMIN_DOMAIN, ROUTE_PATHS } from "@/config/routes";
import { LanguageSwitcher } from "../ui/LanguageSelector";
import Header from "../landing/Header";

export const PublicLayout = ({ children }: { children: React.ReactNode }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useTranslation(); // <-- 3. Initialize translator
  const location = useLocation(); // <-- Get location for pathname

  const menuItems = [
    {
      label: t("menuFindStaff"),
      path: "/personeel-vinden",
    },
    {
      label: t("menuPostJob"),
      path: "/plaats-een-klus",
    },
  ];

  const isJobCreatePage = location.pathname.includes("/plaats-een-klus");
  const isAdminDomain =
    typeof window !== "undefined" && window.location.hostname === ADMIN_DOMAIN;

  const headerSection =
    !isAdminDomain && isJobCreatePage ? (
      <header className="border-b border-b-[#CCCCD9] bg-white sticky top-0 z-50">
        <div className="mx-auto max-w-6xl py-2 px-4 sm:px-0">
          <div className="flex items-center justify-between h-20">
            <Link to="/" className="flex items-center gap-2">
              <img
                src="/logo.png"
                alt="Klusgebied Logo"
                className="w-12 h-12"
              />
              <div className="flex flex-col">
                <span className="text-xl font-semibold text-gray-900">
                  Klusgebied
                </span>
                <span className="text-sm text-gray-500">{t("slogan")}</span>
              </div>
            </Link>

            <div className="flex items-center gap-4">
              {/* Desktop Navigation */}
              <NavigationMenu className="hidden md:flex">
                <NavigationMenuList>
                  {menuItems.map((item) => (
                    <NavigationMenuItem key={item.path}>
                      <Link
                        to={item.path}
                        className="px-4 py-2 text-sm font-medium text-gray-900 hover:text-primary transition-colors"
                      >
                        {item.label}
                      </Link>
                    </NavigationMenuItem>
                  ))}
                </NavigationMenuList>
              </NavigationMenu>

              <div className="hidden md:flex items-center gap-4">
                <LanguageSwitcher /> {/* <-- 4. Add switcher for desktop */}
                <Link
                  to={ROUTE_PATHS.AUTH}
                  className="inline-flex px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors"
                >
                  {t("registerAsProfessional")}
                </Link>
              </div>

              {/* Mobile Menu */}
              <div className="flex items-center gap-2 md:hidden">
                <LanguageSwitcher /> {/* <-- 5. Add switcher for mobile */}
                <Sheet open={isOpen} onOpenChange={setIsOpen}>
                  <SheetTrigger asChild>
                    <Button variant="ghost" size="icon" aria-label="Open Menu">
                      <Menu className="h-5 w-5" />
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                    <SheetHeader>
                      <SheetTitle>{t("mobileMenuTitle")}</SheetTitle>
                    </SheetHeader>
                    <nav className="flex flex-col gap-4 mt-8">
                      {menuItems.map((item) => (
                        <Link
                          key={item.path}
                          to={item.path}
                          className={cn(
                            "text-lg font-medium text-gray-700 hover:text-primary transition-colors",
                            "py-2 px-4 rounded-lg hover:bg-gray-100"
                          )}
                          onClick={() => setIsOpen(false)}
                        >
                          {item.label}
                        </Link>
                      ))}
                      <Link
                        to={ROUTE_PATHS.AUTH}
                        className="text-lg font-medium text-white bg-primary hover:bg-primary/90 py-2 px-4 rounded-lg transition-colors text-center"
                        onClick={() => setIsOpen(false)}
                      >
                        {t("registerAsProfessional")}
                      </Link>
                    </nav>
                  </SheetContent>
                </Sheet>
              </div>
            </div>
          </div>
        </div>
      </header>
    ) : null;

  // Zoom scale for public pages (change this value to adjust zoom everywhere)
  const isMobile = typeof window !== "undefined" && window.innerWidth < 768;
  const ZOOM_SCALE = isMobile ? 0.9 : 0.8;

  // Check if the current path contains "/plaats-een-klus"
  const shouldExcludeFromZoom = location.pathname.includes("/plaats-een-klus");

  // Calculate compensation values based on ZOOM_SCALE
  const widthPercent = `${100 / ZOOM_SCALE}%`;
  const marginLeftPercent = `${-(100 / ZOOM_SCALE - 100) / 2}%`;
  const heightVh = `${100 / ZOOM_SCALE}vh`;

  // Apply zoom style using the constant
  const zoomStyle = shouldExcludeFromZoom
    ? {}
    : {
        transform: `scale(${ZOOM_SCALE})`,
        transformOrigin: "top center",
        width: widthPercent, // Compensate for the scale to maintain full width
        marginLeft: marginLeftPercent, // Center the scaled content
        height: heightVh, // Fill the viewport vertically
        minHeight: heightVh, // Ensure minimum height
      };

  // List of routes that should NOT have padding
  const noPaddingRoutes = ["/", "/login"];
  // List of route substrings that should NOT have padding
  const noPaddingIncludes = ["/diensten"];

  const shouldHavePadding =
    !noPaddingRoutes.includes(location.pathname) &&
    !noPaddingIncludes.some((substr) => location.pathname.includes(substr));

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {!isAdminDomain && <Header />}
      {/* Main content */}
      <main className="flex-1">
        <div
          className={cn("w-full", shouldHavePadding && "pt-28 lg:pt-32")}
          style={zoomStyle}
        >
          {children}
        </div>
      </main>
      {/* Footer */}
      {/* {!isJobCreatePage && !isAdminDomain && (
        <footer className="bg-gray-900 text-white py-12">
          <div className="container mx-auto max-w-5xl px-4 py-2">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <p className="text-lg font-semibold mb-4">{t("footerContactTitle")}</p>
                <p className="text-gray-400">
                  Email: <EMAIL>
                  <br />
                  Tel: +31 6 84614705
                </p>
                <ul className="text-gray-400">
                  <li className="mt-4">
                    <Link to="/weblogs" className="hover:text-primary">
                      {t("footerBlogLink")}
                    </Link>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-4">{t("footerServicesTitle")}</h3>
                <ul className="space-y-2 text-gray-400">
                  <li><Link to="/ventilatie" className="hover:text-primary">{t("footerServiceVentilation")}</Link></li>
                  <li><Link to="/ikea-meubels" className="hover:text-primary">{t("footerServiceIkea")}</Link></li>
                  <li><Link to="/dakkapel" className="hover:text-primary">{t("footerServiceDormer")}</Link></li>
                  <li><Link to="/badkamer" className="hover:text-primary">{t("footerServiceBathroom")}</Link></li>
                  <li><Link to="/lekkage" className="hover:text-primary">{t("footerServiceLeakage")}</Link></li>
                  <li><Link to="/isolatie" className="hover:text-primary">{t("footerServiceInsulation")}</Link></li>
                  <li><Link to="/traprenovatie" className="hover:text-primary">{t("footerServiceStairs")}</Link></li>
                  <li><Link to="/cv-ketel" className="hover:text-primary">{t("footerServiceBoiler")}</Link></li>
                  <li><Link to="/tuinbestrating" className="hover:text-primary">{t("footerServicePaving")}</Link></li>
                  <li><Link to="/aannemer" className="hover:text-primary">{t("footerServiceContractor")}</Link></li>
                  <li><Link to="/bouwbedrijf" className="hover:text-primary">{t("footerServiceConstruction")}</Link></li>
                  <li><Link to="/dakbedekking" className="hover:text-primary">{t("footerServiceRoofing")}</Link></li>
                  <li><Link to="/waterleiding-vervangen" className="hover:text-primary">{t("footerServiceWaterPipe")}</Link></li>
                </ul>
              </div>
              <div>
                <p className="text-lg font-semibold mb-4">{t("footerAboutTitle")}</p>
                <p className="text-gray-400">{t("footerAboutText")}</p>
                <p className="mt-6 flex flex-col justify-center items-start gap-2">
                  <Link to="/privacy" className="inline-flex items-center gap-2 text-sm font-medium text-gray-400 hover:text-primary">
                    {t("privacyPolicy")}
                  </Link>
                  <Link to="/termen" className="inline-flex items-center gap-2 text-sm font-medium text-gray-400 hover:text-primary">
                    {t("termsAndConditions")}
                  </Link>
                </p>
              </div>
            </div>
            <div className="mt-8 pt-8 border-t border-gray-800 text-center text-gray-400">
              <p>
                © {new Date().getFullYear()} Klusgebied. {t("copyright")}
              </p>
            </div>
          </div>
        </footer>
      )} */}
    </div>
  );
};
