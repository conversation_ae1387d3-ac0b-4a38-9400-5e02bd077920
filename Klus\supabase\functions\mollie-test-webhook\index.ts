import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.3";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("Processing Mollie webhook...");
    const formData = await req.formData();
    const paymentId = formData.get("id");

    if (!paymentId) {
      throw new Error("No payment ID provided");
    }

    console.log("Fetching payment details for:", paymentId);
    const response = await fetch(
      `https://api.mollie.com/v2/payments/${paymentId}`,
      {
        headers: {
          Authorization: `Bearer ${Deno.env.get("MOLLIE_TEST_API_KEY")}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Mollie API error:", errorData);
      throw new Error(
        `Mollie API error: ${errorData.detail || "Unknown error"}`
      );
    }

    const payment = await response.json();
    console.log("Payment status:", payment.status);

    const { userId, transactionId } = payment.metadata;

    // Create Supabase client
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { db: { schema: "public" } }
    );

    if (payment.status === "paid") {
      console.log("Payment successful, updating transaction and balance...");

      // Update transaction status
      const { error: updateError } = await supabaseAdmin
        .from("balance_transactions")
        .update({
          status: "accepted",
          completed_at: new Date().toISOString(),
          mollie_payment_id: paymentId,
        })
        .eq("id", transactionId);

      if (updateError) {
        console.error("Error updating transaction:", updateError);
        throw updateError;
      }

      // Increment user balance
      const { error: balanceError } = await supabaseAdmin.rpc(
        "increment_balance",
        {
          user_id: userId,
          increment_amount: parseFloat(payment.amount.value),
        }
      );

      if (balanceError) {
        console.error("Error incrementing balance:", balanceError);
        throw balanceError;
      }

      // Create in-app notification for balance update
      try {
        // Get user profile for email
        const { data: userProfile, error: profileError } = await supabaseAdmin
          .from("profiles")
          .select("email, first_name")
          .eq("id", userId)
          .single();

        if (!profileError && userProfile) {
          // Create notification
          const { error: notificationError } = await supabaseAdmin
            .from("notifications")
            .insert({
              user_id: userId,
              title: "Saldo verhoogd",
              message: `Je saldo is verhoogd met €${parseFloat(
                payment.amount.value
              ).toFixed(2)}. Bedankt voor je betaling!`,
              type: "success",
              action_url: "/evenwicht",
              metadata: {
                transaction_id: transactionId,
                payment_id: paymentId,
                amount: parseFloat(payment.amount.value),
              },
            });

          if (notificationError) {
            console.error(
              "Error creating balance notification:",
              notificationError
            );
          } else {
            console.log("Balance notification created successfully");
          }
        }
      } catch (error) {
        console.error("Error creating balance notification:", error);
        // Don't throw - this is non-critical
      }

      console.log("Transaction completed successfully");
    } else if (["failed", "canceled", "expired"].includes(payment.status)) {
      console.log("Payment failed or canceled, updating transaction status...");

      const { error: updateError } = await supabaseAdmin
        .from("balance_transactions")
        .update({
          status: "failed",
          completed_at: new Date().toISOString(),
          mollie_payment_id: paymentId,
        })
        .eq("id", transactionId);

      if (updateError) {
        console.error("Error updating transaction:", updateError);
        throw updateError;
      }
    }

    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Webhook processing error:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 400,
    });
  }
});
