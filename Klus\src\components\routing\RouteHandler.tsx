import { Routes, Route } from "react-router-dom";
import { getLandingRouteConfig } from "@/config/routes";

// Landing page components
import VentilationLanding from "@/pages/VentilationLanding";
import IkeaLanding from "@/pages/IkeaLanding";
import DakkapelLanding from "@/pages/DakkapelLanding";
import BadkamerLanding from "@/pages/BadkamerLanding";
import LekkageLanding from "@/pages/LekkageLanding";
import IsolatieLanding from "@/pages/IsolatieLanding";
import TraprenovatieLanding from "@/pages/TraprenovatieLanding";
import CVLanding from "@/pages/CVLanding";
import TuinbestratingLanding from "@/pages/TuinbestratingLanding";
import ContractorLanding from "@/pages/ContractorLanding";
import ConstructionLanding from "@/pages/ConstructionLanding";
import RoofingLanding from "@/pages/RoofingLanding";
import WaterleidingLanding from "@/pages/WaterleidingLanding";
import DirectLoginPage from "@/pages/DirectLoginPage";

// Component mapping for landing pages
const LANDING_COMPONENTS = {
  VentilationLanding,
  IkeaLanding,
  DakkapelLanding,
  BadkamerLanding,
  LekkageLanding,
  IsolatieLanding,
  TraprenovatieLanding,
  CVLanding,
  TuinbestratingLanding,
  ContractorLanding,
  ConstructionLanding,
  RoofingLanding,
  WaterleidingLanding,
} as const;

interface LandingRouteHandlerProps {
  pathname: string;
}

export const LandingRouteHandler = ({ pathname }: LandingRouteHandlerProps) => {
  const config = getLandingRouteConfig(pathname);

  if (!config) {
    return null;
  }

  const Component =
    LANDING_COMPONENTS[config.component as keyof typeof LANDING_COMPONENTS];

  if (!Component) {
    console.error(
      `Component ${config.component} not found for route ${pathname}`
    );
    return null;
  }

  return (
    <Routes>
      <Route path={config.path} element={<Component />} />
    </Routes>
  );
};

// Public routes handler
import Auth from "@/pages/Auth";
import WerkwijzePage from "@/pages/WerkwijzePage";
import CompanyFormPage from "@/pages/CompanyFormPage";
import WaaromPage from "@/pages/WaaromPage";
import ResetPasswordPage from "@/pages/ResetPasswordPage";
import { UnAuthorizedNewJobForm } from "@/components/werkwijze/new-jobs/UnAuthorizedNewJobForm";
import { ROUTE_PATHS } from "@/config/routes";
import JobVanLink from "@/pages/JobVanLink";

import HomePage from "@/pages/public/HomePage";
import OverOnsPage from "@/pages/public/OverOnsPage";
import FAQPage from "@/pages/public/FAQPage";
import PressMediaPage from "@/pages/public/PressMediaPage";
import SupportPage from "@/pages/public/SupportPage";
import ContactPage from "@/pages/public/ContactPage";
import CustomerServicePage from "@/pages/public/CustomerServicePage";
import GuaranteePage from "@/pages/public/GuaranteePage";
import DisputesPage from "@/pages/public/DisputesPage";
import FeedbackPage from "@/pages/public/FeedbackPage";
import LoginPage from "@/pages/public/LoginPage";
import AppLandingPage from "@/pages/public/AppLandingPage";
import KlussenAfgerondPage from "@/pages/public/KlussenAfgerondPage";
import VakmanLandingPage from "@/pages/public/VakmanLandingPage";
import InvesteringsmogelijkhedenPage from "@/pages/public/InvesteringsmogelijkhedenPage";
import BoekhoudingVoorVakmensenPage from "@/pages/public/BoekhoudingVoorVakmensenPage";
import BedrijvenRecruitmentPage from "@/pages/public/BedrijvenRecruitmentPage";
import Service_LoodgieterPage from "@/pages/public/Service_LoodgieterPage";
import Service_ElektricienPage from "@/pages/public/Service_ElektricienPage";
import Service_SchilderPage from "@/pages/public/Service_SchilderPage";
import Service_TimmermanPage from "@/pages/public/Service_TimmermanPage";
import Service_KlusjesmanPage from "@/pages/public/Service_KlusjesmanPage";
import Service_DakdekkerPage from "@/pages/public/Service_DakdekkerPage";
import Service_TuinmanPage from "@/pages/public/Service_TuinmanPage";
import Service_CVInstallateurPage from "@/pages/public/Service_CVInstallateurPage";
import Service_TegelvloerSpecialistPage from "@/pages/public/Service_TegelvloerSpecialistPage";
import Service_BeveiligingsmonteurPage from "@/pages/public/Service_BeveiligingsmonteurPage";
import Service_MonteurPage from "@/pages/public/Service_MonteurPage";
import Service_InterieurontwerperPage from "@/pages/public/Service_InterieurontwerperPage";
import Service_IsolatiemonteurPage from "@/pages/public/Service_IsolatiemonteurPage";
import Service_ParketvloerSpecialistPage from "@/pages/public/Service_ParketvloerSpecialistPage";
import Service_AircoInstallateurPage from "@/pages/public/Service_AircoInstallateurPage";
import Service_VerlichtingSpecialistPage from "@/pages/public/Service_VerlichtingSpecialistPage";
import Service_BeveiligingscameraPage from "@/pages/public/Service_BeveiligingscameraPage";
import Service_SmartHomeSpecialistPage from "@/pages/public/Service_SmartHomeSpecialistPage";
import Service_TelefoonInternetPage from "@/pages/public/Service_TelefoonInternetPage";
import Service_GaragedeurMonteurPage from "@/pages/public/Service_GaragedeurMonteurPage";
import Service_StoffeerderPage from "@/pages/public/Service_StoffeerderPage";
import Service_KozijnSpecialistPage from "@/pages/public/Service_KozijnSpecialistPage";
import Service_AannemerServicePage from "@/pages/public/Service_AannemerServicePage";
import Service_BadkamerRenovatiePage from "@/pages/public/Service_BadkamerRenovatiePage";
import Service_BouwbedrijfServicePage from "@/pages/public/Service_BouwbedrijfServicePage";
import Service_DakbedekkingPage from "@/pages/public/Service_DakbedekkingPage";
import Service_DakkapelPlaatsingPage from "@/pages/public/Service_DakkapelPlaatsingPage";
import Service_IKEAMontageServicePage from "@/pages/public/Service_IKEAMontageServicePage";
import Service_IsolatieServicePage from "@/pages/public/Service_IsolatieServicePage";
import Service_TraprenovatiePage from "@/pages/public/Service_TraprenovatiePage";
import Service_TuinbestratingPage from "@/pages/public/Service_TuinbestratingPage";
import Service_VentilatieServicePage from "@/pages/public/Service_VentilatieServicePage";
import Service_LekkageOpsporenPage from "@/pages/public/Service_LekkageOpsporenPage";
import Service_CVKetelServicePage from "@/pages/public/Service_CVKetelServicePage";
import Service_WaterleidingVervangenPage from "@/pages/public/Service_WaterleidingVervangenPage";
import ServiceDetailPage from "@/pages/public/ServiceDetailPage";
import AllServicesPage from "@/pages/public/AllServicesPage";
import CityPage from "@/pages/public/CityPage";
import CvKetelContractPage from "@/pages/public/CvKetelContractPage";
import DakgootContractPage from "@/pages/public/DakgootContractPage";
import KlusjesmanContractPage from "@/pages/public/KlusjesmanContractPage";
import HuisInspectieContractPage from "@/pages/public/HuisInspectieContractPage";
import OnderhoudscontractenPage from "@/pages/public/OnderhoudscontractenPage";
import KlusgebiedPlusPage from "@/pages/public/KlusgebiedPlusPage";
import KlusgebiedProPage from "@/pages/public/KlusgebiedProPage";
import KlusgebiedPartnerPage from "@/pages/public/KlusgebiedPartnerPage";

export const PublicRouteHandler = () => {
  return (
    <Routes>
      {/* Blog routes */}
      <Route path={ROUTE_PATHS.BLOGS} element={<BlogHomePage />} />
      <Route path={ROUTE_PATHS.BLOGDETAILS} element={<BlogPostPage />} />

      {/* <Route path={ROUTE_PATHS.AUTH} element={<Auth />} />
      <Route path={ROUTE_PATHS.PLACE_JOB} element={<WerkwijzePage />} />
      <Route path={ROUTE_PATHS.FIND_PERSONNEL} element={<CompanyFormPage />} />
      <Route path="/plaats-een-klus/:id" element={<UnAuthorizedNewJobForm />} />
      <Route path={ROUTE_PATHS.LOGIN} element={<WaaromPage />} />
      <Route
        path={ROUTE_PATHS.RESET_PASSWORD}
        element={<ResetPasswordPage />}
      />
      <Route path={ROUTE_PATHS.PRIVACY} element={<Privacy />} />
      <Route path={ROUTE_PATHS.TERMS} element={<Terms />} /> */}
      <Route path={ROUTE_PATHS.PLACE_JOB} element={<WerkwijzePage />} />
      <Route path="/plaats-een-klus/:id" element={<UnAuthorizedNewJobForm />} />
      <Route path="/" element={<HomePage />} />
      <Route path="/over-ons" element={<OverOnsPage />} />
      <Route path="/faq" element={<FAQPage />} />
      <Route path="/pers-en-media" element={<PressMediaPage />} />
      <Route path="/support" element={<SupportPage />} />
      <Route path="/contact" element={<ContactPage />} />
      <Route path="/klantenservice" element={<CustomerServicePage />} />
      <Route path="/garantie" element={<GuaranteePage />} />
      <Route path="/geschillen" element={<DisputesPage />} />
      <Route path="/feedback" element={<FeedbackPage />} />
      <Route path="/login" element={<LoginPage />} />
      <Route path="/app" element={<AppLandingPage />} />
      <Route path="/klussen-afgerond" element={<KlussenAfgerondPage />} />
      <Route path="/vakman" element={<VakmanLandingPage />} />
      <Route path="/investeren" element={<InvesteringsmogelijkhedenPage />} />
      <Route path="/boekhouding" element={<BoekhoudingVoorVakmensenPage />} />
      <Route path="/bedrijven" element={<BedrijvenRecruitmentPage />} />
      <Route path="/diensten/loodgieter" element={<Service_LoodgieterPage />} />
      <Route
        path="/diensten/elektricien"
        element={<Service_ElektricienPage />}
      />
      <Route path="/diensten/schilder" element={<Service_SchilderPage />} />
      <Route path="/diensten/timmerman" element={<Service_TimmermanPage />} />
      <Route path="/diensten/klusjesman" element={<Service_KlusjesmanPage />} />
      <Route path="/diensten/dakdekker" element={<Service_DakdekkerPage />} />
      <Route path="/diensten/tuinman" element={<Service_TuinmanPage />} />
      <Route
        path="/diensten/cv-installateur"
        element={<Service_CVInstallateurPage />}
      />
      <Route
        path="/diensten/tegelvloer-specialist"
        element={<Service_TegelvloerSpecialistPage />}
      />
      <Route
        path="/diensten/beveiligingsmonteur"
        element={<Service_BeveiligingsmonteurPage />}
      />
      <Route path="/diensten/monteur" element={<Service_MonteurPage />} />
      <Route
        path="/diensten/interieurontwerper"
        element={<Service_InterieurontwerperPage />}
      />
      <Route
        path="/diensten/isolatiemonteur"
        element={<Service_IsolatiemonteurPage />}
      />
      <Route
        path="/diensten/parketvloer-specialist"
        element={<Service_ParketvloerSpecialistPage />}
      />
      <Route
        path="/diensten/airco-installateur"
        element={<Service_AircoInstallateurPage />}
      />
      <Route
        path="/diensten/verlichting-specialist"
        element={<Service_VerlichtingSpecialistPage />}
      />
      <Route
        path="/diensten/beveiligingscamera"
        element={<Service_BeveiligingscameraPage />}
      />
      <Route
        path="/diensten/smart-home-specialist"
        element={<Service_SmartHomeSpecialistPage />}
      />
      <Route
        path="/diensten/telefoon-internet"
        element={<Service_TelefoonInternetPage />}
      />
      <Route
        path="/diensten/garagedeur-monteur"
        element={<Service_GaragedeurMonteurPage />}
      />
      <Route
        path="/diensten/stoffeerder"
        element={<Service_StoffeerderPage />}
      />
      <Route
        path="/diensten/kozijn-specialist"
        element={<Service_KozijnSpecialistPage />}
      />
      <Route
        path="/diensten/aannemer-service"
        element={<Service_AannemerServicePage />}
      />
      <Route
        path="/diensten/badkamer-renovatie"
        element={<Service_BadkamerRenovatiePage />}
      />
      <Route
        path="/diensten/bouwbedrijf-service"
        element={<Service_BouwbedrijfServicePage />}
      />
      <Route
        path="/diensten/dakbedekking"
        element={<Service_DakbedekkingPage />}
      />
      <Route
        path="/diensten/dakkapel-plaatsing"
        element={<Service_DakkapelPlaatsingPage />}
      />
      <Route
        path="/diensten/ikea-montage-service"
        element={<Service_IKEAMontageServicePage />}
      />
      <Route
        path="/diensten/isolatie-service"
        element={<Service_IsolatieServicePage />}
      />
      <Route
        path="/diensten/traprenovatie"
        element={<Service_TraprenovatiePage />}
      />
      <Route
        path="/diensten/tuinbestrating"
        element={<Service_TuinbestratingPage />}
      />
      <Route
        path="/diensten/ventilatie-service"
        element={<Service_VentilatieServicePage />}
      />
      <Route
        path="/diensten/lekkage-opsporen"
        element={<Service_LekkageOpsporenPage />}
      />
      <Route
        path="/diensten/cv-ketel-service"
        element={<Service_CVKetelServicePage />}
      />
      <Route
        path="/diensten/waterleiding-vervangen"
        element={<Service_WaterleidingVervangenPage />}
      />
      <Route path="/diensten/:serviceSlug" element={<ServiceDetailPage />} />
      <Route path="/diensten" element={<AllServicesPage />} />
      <Route path="/stad/:cityName" element={<CityPage />} />
      <Route
        path="/onderhoudscontract/cv-ketel"
        element={<CvKetelContractPage />}
      />
      <Route
        path="/onderhoudscontract/dakgoot"
        element={<DakgootContractPage />}
      />
      <Route
        path="/onderhoudscontract/klusjesman"
        element={<KlusjesmanContractPage />}
      />
      <Route
        path="/onderhoudscontract/huis-inspectie"
        element={<HuisInspectieContractPage />}
      />
      <Route
        path="/onderhoudscontracten"
        element={<OnderhoudscontractenPage />}
      />

      {/* Package detail pages */}
      <Route
        path={ROUTE_PATHS.PACKAGE_KLUSGEBIED_PLUS}
        element={<KlusgebiedPlusPage />}
      />
      <Route
        path={ROUTE_PATHS.PACKAGE_KLUSGEBIED_PRO}
        element={<KlusgebiedProPage />}
      />
      <Route
        path={ROUTE_PATHS.PACKAGE_KLUSGEBIED_PARTNER}
        element={<KlusgebiedPartnerPage />}
      />
    </Routes>
  );
};

// Protected routes handler
import { Navigate } from "react-router-dom";
import Index from "@/pages/Index";
import Balance from "@/pages/Balance";
import Reviews from "@/pages/Reviews";
import BonusSystem from "@/pages/BonusSystem";
import { ProfileSection } from "@/components/ProfileSection";
import { JobList } from "@/components/JobList";
import { MyResponses } from "@/components/MyResponses";
import { VakmanList } from "@/components/VakmanList";
import { JobDetail } from "@/components/JobDetail";
import { CreateJobForm } from "@/components/CreateJobForm";
import { PortfolioPage } from "@/components/portfolio/PortfolioPage";
import ChatPage from "@/pages/ChatPage";
import { GeneralNewJobForm } from "@/components/werkwijze/new-jobs/GeneralNewJobForm";
import BoekhoudingUitbestedenPage from "@/pages/BoekhoudingUitbestedenPage";
import { NewMFA } from "@/components/security/NewMFA";

// Admin components
import AdminDashboard from "@/pages/AdminDashboard";
import DatabasePage from "@/pages/admin/DatabasePage";
import AnalyticsPage from "@/pages/admin/AnalyticsPage";
import ReportsPage from "@/pages/admin/ReportsPage";
import MessagesPage from "@/pages/admin/MessagesPage";
import { CompanyFormsList } from "@/pages/admin/CompanyFormListPage";
import CraftsmenMapView from "@/pages/admin/CraftsmenMapView";
import CraftmanUsersPage from "@/pages/admin/CraftmanUsersPage";
import KlasaanvragerUsersPage from "@/pages/admin/KlusaanvragerUsersPage";
import JobTemplatePage from "@/pages/admin/JobTemplatePage";

// Blog components
import BlogHomePage from "@/pages/blog/BlogHomePage";
import BlogPostPage from "@/pages/blog/BlogPostPage";
import AdminPostsPage from "@/pages/admin/blog/AdminPostsPage";
import BlogFormPage from "@/pages/admin/blog/BlogFormPage";
import BlogEditPage from "@/pages/admin/blog/BlogEditPage";
import Maintenance from "@/pages/Maintenance";
import ContractSignupPage from "@/pages/ContractSignUp";
import NotificationsPage from "@/pages/NotificationsPage";

export const ProtectedRouteHandler = () => {
  return (
    <Routes>
      <Route path={ROUTE_PATHS.HOME} element={<Index />} />
      <Route path={ROUTE_PATHS.BALANCE} element={<Balance />} />
      <Route path={ROUTE_PATHS.PROFILE} element={<ProfileSection />} />
      <Route path={ROUTE_PATHS.PROFILE_MFA} element={<NewMFA />} />
      <Route path={ROUTE_PATHS.PORTFOLIO} element={<PortfolioPage />} />
      <Route path={ROUTE_PATHS.JOBS} element={<JobList />} />
      <Route path={ROUTE_PATHS.MAINTENANCE} element={<Maintenance />} />
      <Route
        path={ROUTE_PATHS.CONTRACT_SIGNUP}
        element={<ContractSignupPage />}
      />

      <Route path={ROUTE_PATHS.JOBS_NEW} element={<CreateJobForm />} />
      <Route path="/banen/new/:id" element={<GeneralNewJobForm />} />
      <Route path={ROUTE_PATHS.JOBS_DETAIL} element={<JobDetail />} />
      <Route path={ROUTE_PATHS.MY_RESPONSES} element={<MyResponses />} />
      <Route path={ROUTE_PATHS.CHOOSE_CRAFTSMAN} element={<VakmanList />} />
      <Route path={ROUTE_PATHS.REVIEWS} element={<Reviews />} />
      <Route path={ROUTE_PATHS.CONVERSATIONS} element={<ChatPage />} />
      <Route path={ROUTE_PATHS.NOTIFICATIONS} element={<NotificationsPage />} />
      <Route path={ROUTE_PATHS.BONUS} element={<BonusSystem />} />
      <Route
        path={ROUTE_PATHS.BOEKHOUDING_UITBESTEDEN}
        element={<BoekhoudingUitbestedenPage />}
      />

      {/* Admin Routes */}
      <Route path={ROUTE_PATHS.ADMIN} element={<AdminDashboard />} />
      <Route path={ROUTE_PATHS.ADMIN_DATABASE} element={<DatabasePage />} />
      <Route path={ROUTE_PATHS.ADMIN_ANALYTICS} element={<AnalyticsPage />} />
      <Route path={ROUTE_PATHS.ADMIN_REPORTS} element={<ReportsPage />} />
      <Route path={ROUTE_PATHS.ADMIN_MESSAGES} element={<MessagesPage />} />
      <Route
        path={ROUTE_PATHS.ADMIN_COMPANY_REQUESTS}
        element={<CompanyFormsList />}
      />
      <Route
        path={ROUTE_PATHS.ADMIN_CRAFTSMEN_MAP}
        element={<CraftsmenMapView />}
      />
      <Route
        path={ROUTE_PATHS.ADMIN_CRAFTSMEN}
        element={<CraftmanUsersPage />}
      />
      <Route
        path={ROUTE_PATHS.ADMIN_CUSTOMERS}
        element={<KlasaanvragerUsersPage />}
      />

      <Route
        path={ROUTE_PATHS.ADMIN_JOB_TEMPLATES}
        element={<JobTemplatePage />}
      />

      {/* Blog Admin Routes */}
      <Route path={ROUTE_PATHS.ADMIN_BLOG_POSTS} element={<AdminPostsPage />} />
      <Route
        path={ROUTE_PATHS.ADMIN_BLOG_POSTS_NEW}
        element={<BlogFormPage />}
      />
      <Route
        path={ROUTE_PATHS.ADMIN_BLOG_POSTS_EDIT}
        element={<BlogEditPage />}
      />

      <Route path="*" element={<Navigate to={ROUTE_PATHS.HOME} replace />} />
    </Routes>
  );
};
