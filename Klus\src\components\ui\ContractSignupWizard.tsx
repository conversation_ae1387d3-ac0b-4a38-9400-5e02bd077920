/**
 * @description This component renders a multi-step wizard for signing up for a boiler maintenance contract.
 * It guides the user through a streamlined 4-step process, collecting necessary information and concluding with a full PDF contract view.
 * The component manages form state, validation, step progression, and dynamically generates the contract text.
 * Backend logic is simulated for front-end development.
 */
import React, { useState, useEffect, useRef } from 'react';
import { X, ArrowLeft, ArrowRight, User, Settings, FileText, CheckCircle, Loader, AlertCircle } from 'lucide-react';
import { PDFViewer } from '@react-pdf/renderer';
import ContractDocument from './ContractDocument';
import {contractTemplates} from "./contract-template"
// --- Sub-component for Digital Signature ---
import { supabase } from "@/integrations/supabase/client";

const SignatureCanvas = ({ onSave }) => {
  const canvasRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
    ctx.strokeStyle = '#1A1A1A';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
  }, []);

  const getCoords = (e) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const event = e.touches ? e.touches[0] : e;
    return [event.clientX - rect.left, event.clientY - rect.top];
  };

  const startDrawing = (e) => {
    const coords = getCoords(e);
    const ctx = canvasRef.current.getContext('2d');
    ctx.beginPath();
    ctx.moveTo(coords[0], coords[1]);
    setIsDrawing(true);
  };

  const draw = (e) => {
    if (!isDrawing) return;
    e.preventDefault();
    const coords = getCoords(e);
    const ctx = canvasRef.current.getContext('2d');
    ctx.lineTo(coords[0], coords[1]);
    ctx.stroke();
  };

  const stopDrawing = () => {
    if (!isDrawing) return;
    setIsDrawing(false);
    const dataUrl = canvasRef.current.toDataURL('image/png');
    onSave(dataUrl);
  };

  const clearCanvas = () => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    onSave(null);
  };

  return (
    <div>
      <canvas
        ref={canvasRef}
        className="bg-gray-100 border border-gray-300 rounded-lg w-full h-[150px]"
        onMouseDown={startDrawing}
        onMouseMove={draw}
        onMouseUp={stopDrawing}
        onMouseLeave={stopDrawing}
        onTouchStart={startDrawing}
        onTouchMove={draw}
        onTouchEnd={stopDrawing}
      />
      <button type="button" onClick={clearCanvas} className="mt-2 text-sm text-teal-600 hover:text-teal-800">
        Opnieuw tekenen
      </button>
    </div>
  );
};


// Define the props for your ContractSignupWizard component
export type ContractSignupWizardProps = {
  isOpen?: boolean;
  onClose?: () => void;
  selectedPackage?: any;
  packages?:any
};// --- Main Wizard Component ---
export const ContractSignupWizard =({ isOpen, onClose, selectedPackage, packages }:ContractSignupWizardProps) => {
  const [currentStep, setCurrentStep] = useState(1);

  const [formData, setFormData] = useState({
  woonplaats: '',
    merk: '', type_ketel: '', bouwjaar: '',
    voorkeursmaand: '', voorkeursdagdeel: 'Ochtend',
    pakket_keuze: '', pakket_prijs: 0,
    betaalmethode: 'Automatische incasso', iban: '',
    akkoord_voorwaarden: false,
    digitale_handtekening: null,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  useEffect(() => {
    if (isOpen) {
        // Reset state when the modal is opened or package changes
        setCurrentStep(1);
        setError('');
        setIsLoading(false);
        if (selectedPackage) {
          setFormData(prev => ({
            ...prev,
            pakket_keuze: selectedPackage.name,
            pakket_prijs: selectedPackage.price,
          }));
        }
    }
  }, [isOpen, selectedPackage]);

  if (!isOpen) return null;

  const totalSteps = 3;
  const stepDetails = [
    { icon: User, title: "Uw Gegevens & Ketel" },
    { icon: Settings, title: "Planning & Betaling" },
    { icon: FileText, title: "Overzicht & Akkoord" },
    { icon: CheckCircle, title: "Contract Bevestiging" },
  ];

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
  };

  const nextStep = () => setCurrentStep(prev => Math.min(prev + 1, totalSteps));
  const prevStep = () => setCurrentStep(prev => Math.max(prev - 1, 1));

  const handleSubmit = async () => {
      const {
           data: { user },
           
         } = await supabase.auth.getUser();
       
// 3. Prepare the data for insertion, mapping your form state to the DB columns
      const contractData = {
        user_id: user.id, // CRITICAL: Link the contract to the logged-in user for RLS
        voornaam: formData.voornaam,
        achternaam: formData.achternaam,
        email: formData.email,
        telefoonnummer: formData.telefoonnummer,
        straat: formData.straat,
        huisnummer: formData.huisnummer,
        postcode: formData.postcode,
        woonplaats: formData.woonplaats,
        merk_ketel: formData.merk,
        type_ketel: formData.type_ketel,
        bouwjaar_ketel: formData.bouwjaar,
        voorkeursmaand: formData.voorkeursmaand,
        voorkeursdagdeel: formData.voorkeursdagdeel,
        pakket_keuze: formData.pakket_keuze,
        pakket_prijs: formData.pakket_prijs,
        betaalmethode: formData.betaalmethode,
        iban: formData.iban,
        akkoord_voorwaarden: formData.akkoord_voorwaarden,
        digitale_handtekening: formData.digitale_handtekening,
      };

      // 4. Insert the prepared data into the 'contracts' table
      const { error: insertError } = await supabase
        .from('contracts')
        .insert([contractData]); // Pass the actual contract data here

      // If Supabase returns an error during insertion, throw it to the catch block
      if (insertError) {
        throw insertError;
      }

      // 5. SUCCESS: If we reach here, the insertion was successful.
      // We no longer need setTimeout. We advance the step directly.
      console.log("Contract successfully saved to Supabase!");
    setIsLoading(true);
    setError('');
    console.log("Simulating submission with form data:", formData);

    setTimeout(() => {
      setIsLoading(false);
      setCurrentStep(4); // Go to the final PDF step
    }, 1500);
  };

  const renderStep = () => {
    switch (currentStep) {
     
      case 1: return (
         <div>
          <h3 className="text-xl font-semibold mb-6">Stap 1: Planning & Betaling</h3>
          <div className="space-y-6">
            <div>
                <h4 className="font-medium text-gray-800">Planning Onderhoud</h4>
                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                    <input type="text" name="voorkeursmaand" placeholder="Voorkeursmaand (bv. Mei)" value={formData.voorkeursmaand} onChange={handleChange} className="w-full p-2 border rounded" />
                    <select name="voorkeursdagdeel" value={formData.voorkeursdagdeel} onChange={handleChange} className="w-full p-2 border rounded">
                        <option>Ochtend</option>
                        <option>Middag</option>
                    </select>
                </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-800">Betaalinformatie</h4>
              <div className="space-y-4 mt-2">
                 <select name="betaalmethode" value={formData.betaalmethode} onChange={handleChange} className="w-full p-2 border rounded">
                    <option>Automatische incasso</option>
                    <option>iDEAL</option>
                </select>
                {formData.betaalmethode === 'Automatische incasso' && (
                    <input type="text" name="iban" placeholder="IBAN: ******************" value={formData.iban} onChange={handleChange} className="w-full p-2 border rounded" required />
                )}
              </div>
            </div>
          </div>
        </div>
      );
      case 2: return (
        <div>
          <h3 className="text-xl font-semibold mb-4">Stap 2: Overzicht & Akkoord</h3>
          <div className="p-4 border rounded-lg  max-h-60 overflow-y-auto text-sm space-y-2 mb-4">
        
            <p><strong>Pakket:</strong> {formData.pakket_keuze} (€{formData.pakket_prijs.toFixed(2).replace('.',',')} /maand)</p>
            <p><strong>Betaling:</strong> {formData.betaalmethode}</p>
          </div>
          <div className="mt-4 flex items-start">
            <input type="checkbox" id="akkoord_voorwaarden" name="akkoord_voorwaarden" checked={formData.akkoord_voorwaarden} onChange={handleChange} className="h-4 w-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500 mt-1" />
            <label htmlFor="akkoord_voorwaarden" className="ml-2 block text-sm text-gray-900">Ik ga akkoord met de algemene voorwaarden en machtig hierbij de automatische incasso.</label>
          </div>
           <div className="mt-6">
            <label className="font-medium text-gray-800">Digitale Handtekening</label>
            <SignatureCanvas onSave={(data) => setFormData(prev => ({ ...prev, digitale_handtekening: data }))} />
          </div>
        </div>
      );
      case 3: {
        const baseText = contractTemplates[selectedPackage.name] || 'Fout: Contract template kon niet worden gevonden.';
        const processedText = baseText
        
          .replace(/\[bedrijfsadres\]/g, 'Voorbeeldstraat 1, 1234 AB Amsterdam') // <-- IMPORTANT: REPLACE WITH YOUR INFO
          .replace(/\[KvK-nummer\]/g, '87654321'); // <-- IMPORTANT: REPLACE WITH YOUR INFO

        return (
          <div className="text-center">
              <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
              <h3 className="mt-4 text-2xl font-bold">Aanvraag succesvol!</h3>
              <p className="mt-2 text-gray-600 mb-6">Uw contract is aangemaakt. U kunt het hieronder direct bekijken.</p>
              <div className="w-full h-[500px] border rounded-lg bg-gray-200">
                  <PDFViewer width="100%" height="100%" style={{ border: 'none' }}>
                      <ContractDocument 
                        formData={formData} 
                        contractText={processedText}
                      />
                  </PDFViewer>
              </div>
          </div>
        );
      }
      default: return null;
    }
  };

  return (
    <div className="">
      <div className="  max-w-3xl w-full max-h-[90vh] flex flex-col">
        <div className="p-6  border-b flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{stepDetails[currentStep - 1].title}</h2>
            {currentStep < totalSteps && <p className="text-sm text-gray-500">Stap {currentStep} van {totalSteps -1}</p>}
          </div>
          {/* <button onClick={onClose} className="p-2 rounded-full hover:bg-gray-100"><X size={24} /></button> */}
        </div>

        {currentStep < totalSteps && (
          <div className="p-2 ">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-teal-500 h-2 rounded-full transition-all duration-500" style={{ width: `${(currentStep / (totalSteps -1)) * 100}%` }}></div>
            </div>
          </div>
        )}

        <div className="p-8 overflow-y-auto flex-grow">
          {renderStep()}
        </div>

        <div className="p-6 border-t  flex justify-between items-center">
          {currentStep === 4 ? (
             <button onClick={onClose} className="w-full px-6 py-3 bg-teal-500 text-white font-semibold rounded-lg hover:bg-teal-600">
                Sluiten
             </button>
          ) : (
            <>
                <button onClick={prevStep} disabled={currentStep === 1 || isLoading} className="px-6 py-2 flex items-center border border-gray-300 rounded-lg hover:bg-gray-100 disabled:opacity-50">
                    <ArrowLeft size={16} className="mr-2" /> Terug
                </button>
                {error && <p className="text-red-500 text-sm flex items-center"><AlertCircle size={16} className="mr-1" /> {error}</p>}
                {currentStep < 3 ? (
                  <button onClick={nextStep} className="px-6 py-2 flex items-center bg-teal-500 text-white rounded-lg hover:bg-teal-600">
                    Volgende <ArrowRight size={16} className="ml-2" />
                  </button>
                ) : (
                  <button onClick={handleSubmit} disabled={isLoading || !formData.akkoord_voorwaarden || !formData.digitale_handtekening} className="px-6 py-2 flex items-center bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-400">
                    {isLoading ? <Loader className="animate-spin mr-2" size={16} /> : <CheckCircle size={16} className="mr-2" />}
                    Aanvraag Afronden
                  </button>
                )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};