import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { EuroIcon, MapIcon, FilterIcon } from "lucide-react";

import CraftsmenMap from "@/components/admin/map/CraftsmenMap";
import { supabase } from "@/integrations/supabase/client";
import { serviceCategories } from "@/components/profile/ProfileServices";
import { Button } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const categoryColors = {
  "Algemene klussen & bouw": "bg-emerald-400 hover:bg-emerald-500",
  "Installatie & leidingwerk": "bg-sky-400 hover:bg-sky-500",
  "Elektra & domotica": "bg-amber-400 hover:bg-amber-500",
  "Woningrenovatie & interieur": "bg-purple-400 hover:bg-purple-500",
  "Onderhoud & reparatie": "bg-rose-400 hover:bg-rose-500",
  "Tuin & buitenwerk": "bg-indigo-400 hover:bg-indigo-500",
  "Schoonmaak & specials": "bg-slate-400 hover:bg-slate-500",
};

const CraftsmenMapView = () => {
  const fetchCraftsmen = async () => {
    const { data, error } = await supabase
      .from("profiles")
      .select(
        `
        id,
        street_address,
        postal_code,
        full_name,
        services,
        balance,
        email,
        phone_number,
        kvk_number,
        profile_photo_url
      `
      )
      .eq("user_type", "vakman");

    if (error) {
      throw new Error(`Error fetching craftsmen: ${error.message}`);
    }

    return data || [];
  };

  const {
    data: craftsmen,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["craftsmen"],
    queryFn: fetchCraftsmen,
  });

  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [filteredCraftsmen, setFilteredCraftsmen] = useState(craftsmen || []);
  const [showOnlyPaid, setShowOnlyPaid] = useState(false);

  useEffect(() => {
    if (!craftsmen) return;

    let filtered = craftsmen;

    // First filter by payment status if enabled
    if (showOnlyPaid) {
      filtered = filtered.filter((craftsman) => craftsman.balance > 0);
    }

    // Then filter by selected categories
    if (selectedCategories.length > 0) {
      filtered = filtered.filter((craftsman) =>
        craftsman.services?.some((service: string) =>
          selectedCategories.includes(service)
        )
      );
    }

    setFilteredCraftsmen(filtered);
  }, [selectedCategories, craftsmen, showOnlyPaid]);

  const toggleSubcategory = (subcategory: string) => {
    setSelectedCategories((prev) =>
      prev.includes(subcategory)
        ? prev.filter((cat) => cat !== subcategory)
        : [...prev, subcategory]
    );
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto py-6 sm:px-2 px-6">
        <div className="animate-pulse">
          <div className="h-8 w-48 bg-gray-200 rounded mb-6"></div>
          <div className="h-[600px] w-full bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center">
        Error loading craftsmen data
      </div>
    );
  }

  return (
    <div className="overflow-y-auto h-[calc(100vh-85px)]">
      <div className="max-w-7xl mx-auto py-6 sm:px-2 px-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <div className="flex items-center gap-2">
            <MapIcon className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold">Vakmannen op de kaart</h1>
          </div>

          {/* Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <Button
              onClick={() => setShowOnlyPaid(!showOnlyPaid)}
              variant={showOnlyPaid ? "default" : "outline"}
              className="flex items-center gap-2 w-full sm:w-auto justify-center"
            >
              <EuroIcon className="w-4 h-4" />
              <span className="whitespace-nowrap">
                {showOnlyPaid ? "Alle vakmannen" : "Alleen betalend"}
              </span>
            </Button>

            <Button
              variant="outline"
              className="flex items-center gap-2 w-full sm:w-auto justify-center"
              onClick={() =>
                document
                  .getElementById("filters")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              <FilterIcon className="w-4 h-4" />
              <span>Filters</span>
              {selectedCategories.length > 0 && (
                <span className="bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full text-sm">
                  {selectedCategories.length}
                </span>
              )}
            </Button>
          </div>
        </div>

        {/* Map Section */}
        <div className="bg-white shadow-sm mb-8">
          <CraftsmenMap craftsmen={filteredCraftsmen} />
        </div>

        {/* Filters Section */}
        <div id="filters" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold flex items-center gap-2">
              <FilterIcon className="w-5 h-5 text-gray-500" />
              Filter op diensten
            </h2>
            {selectedCategories.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedCategories([])}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Reset filters
              </Button>
            )}
          </div>

          <Accordion type="multiple" className="space-y-3">
            {serviceCategories.map((category) => (
              <AccordionItem
                key={category.name}
                value={category.name}
                className={`rounded-lg border shadow-sm transition-all hover:shadow-md ${
                  categoryColors[category.name]
                }`}
              >
                <AccordionTrigger className="px-4 hover:no-underline">
                  <div className="flex items-center gap-3 w-full">
                    <span className="text-xl">{category.icon}</span>
                    <span className="flex-1 text-left">{category.name}</span>
                    {category.services.some((service) =>
                      selectedCategories.includes(service)
                    ) && (
                      <span className="bg-blue-100 text-blue-600 px-2 py-0.5 rounded-full text-sm mr-2">
                        {
                          category.services.filter((service) =>
                            selectedCategories.includes(service)
                          ).length
                        }
                      </span>
                    )}
                  </div>
                </AccordionTrigger>
                <AccordionContent className="!pb-0">
                  <div className="p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 bg-gray-50 rounded-b-lg">
                    {category.services.map((service) => (
                      <label
                        key={service}
                        className="flex items-center gap-3 p-2 rounded-lg bg-white border border-gray-100 hover:border-blue-200 hover:bg-blue-50 transition-colors cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={selectedCategories.includes(service)}
                          onChange={() => toggleSubcategory(service)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                        />
                        <span className="text-sm">{service}</span>
                      </label>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  );
};

export default CraftsmenMapView;
