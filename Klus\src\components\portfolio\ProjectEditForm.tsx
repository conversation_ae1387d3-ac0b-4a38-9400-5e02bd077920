import { useState } from "react";
import { <PERSON><PERSON><PERSON>, Loader2, X } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Project } from "./types";
import { ScrollArea } from "../ui/scroll-area";

interface ProjectEditFormProps {
  project: Project;
  onSubmit: (data: ProjectFormData) => Promise<void>;
  onCancel: () => void;
}

export interface ProjectFormData {
  title: string;
  description: string;
  budget?: number;
  existingPhotos: Photo[];
  newPhotos: File[];
}

interface Photo {
  id: string;
  photo_url: string;
}

const MAX_PHOTOS = 5;

export const ProjectEditForm = ({
  project,
  onSubmit,
  onCancel,
}: ProjectEditFormProps) => {
  const [photos, setPhotos] = useState<Photo[]>(project.photos || []);
  const [newPhotos, setNewPhotos] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const totalPhotos = photos.length + newPhotos.length;

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const remainingSlots = MAX_PHOTOS - totalPhotos;
    const allowedFiles = files.slice(0, remainingSlots);

    if (allowedFiles.length > 0) {
      setNewPhotos((prev) => [...prev, ...allowedFiles]);
    }
  };

  const handleRemovePhoto = (photoId: string) => {
    setPhotos((prev) => prev.filter((photo) => photo.id !== photoId));
  };

  const handleRemoveNewPhoto = (index: number) => {
    setNewPhotos((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (isSubmitting) return;

    setIsSubmitting(true);
    try {
      const formData = new FormData(e.currentTarget);
      await onSubmit({
        title: formData.get("title") as string,
        description: formData.get("description") as string,
        budget: formData.get("budget")
          ? Number(formData.get("budget"))
          : undefined,
        existingPhotos: photos,
        newPhotos: newPhotos,
      });
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="shadow">
      <CardContent className="pt-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Input
              name="title"
              defaultValue={project.title}
              placeholder="Projecttitel"
              required
            />
          </div>
          <div>
            <Input
              name="budget"
              type="number"
              step="0.01"
              min="0"
              defaultValue={project.budget || ""}
              placeholder="Budget (optioneel)"
              onKeyDown={(e) => {
                if (e.key === "-") {
                  e.preventDefault();
                }
              }}
            />
          </div>
          <div>
            <Textarea
              name="description"
              defaultValue={project.description || ""}
              placeholder="Projectbeschrijving"
              required
            />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Foto's</label>
              <span className="text-sm text-muted-foreground">
                {totalPhotos}/{MAX_PHOTOS} foto's
              </span>
            </div>
            <ScrollArea className="h-[200px] w-full rounded-md border p-4">
              <div className="grid grid-cols-3 gap-4">
                {/* Existing photos */}
                {photos.map((photo) => (
                  <div key={photo.id} className="relative group">
                    <img
                      src={photo.photo_url}
                      alt="Project photo"
                      className="rounded-md object-cover w-full h-[120px]"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      className="absolute -top-2 -right-2 hidden group-hover:flex"
                      onClick={() => handleRemovePhoto(photo.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}

                {/* New photos preview */}
                {newPhotos.map((file, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={URL.createObjectURL(file)}
                      alt={`New photo ${index + 1}`}
                      className="rounded-md object-cover w-full h-[120px]"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      className="absolute hidden -top-2 -right-2 group-hover:flex"
                      onClick={() => handleRemoveNewPhoto(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}

                {/* Upload button */}
                {totalPhotos < MAX_PHOTOS && (
                  <label className="cursor-pointer">
                    <div className="border-2 border-dashed rounded-md h-[120px] flex items-center justify-center">
                      <ImagePlus className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      className="hidden"
                      onChange={handlePhotoUpload}
                    />
                  </label>
                )}
              </div>
            </ScrollArea>
          </div>
          <div className="flex gap-2">
            <Button type="submit" className="flex-1" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Opslaan...
                </>
              ) : (
                "Opslaan"
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Annuleren
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
