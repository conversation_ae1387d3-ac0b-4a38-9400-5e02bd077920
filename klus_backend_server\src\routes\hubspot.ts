import { Router } from 'express';
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler';
import { config } from '../config/environment';
import { supabaseClient } from '../config/supabase';

const router = Router();

// Sync profiles to HubSpot
router.post('/sync',
  asyncHandler(async (req, res) => {
    // Get profiles from Supabase
    const { data: profiles, error } = await supabaseClient
      .from('profiles')
      .select('*');
      
    if (error) throw error;

    let successCount = 0;
    let errorCount = 0;

    // For each profile, sync to HubSpot
    for (const profile of profiles) {
      try {
        const hubspotContact = {
          properties: {
            email: profile.email,
            firstname: profile.first_name,
            lastname: profile.last_name,
            phone: profile.phone_number,
            company: profile.company_name,
            user_type: profile.user_type,
          },
        };

        // Send to HubSpot
        const response = await fetch('https://api.hubapi.com/crm/v3/objects/contacts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.hubspot.apiKey}`,
          },
          body: JSON.stringify(hubspotContact),
        });

        if (!response.ok) {
          console.error(`Failed to sync contact ${profile.email} to HubSpot`);
          errorCount++;
          continue;
        }

        console.log(`Successfully synced contact ${profile.email} to HubSpot`);
        successCount++;
      } catch (error) {
        console.error(`Error syncing contact ${profile.email}:`, error);
        errorCount++;
      }
    }

    res.json({ 
      message: 'Sync completed', 
      successCount, 
      errorCount,
      totalProfiles: profiles.length 
    });
  })
);

// Trigger HubSpot sync
router.post('/trigger-sync',
  asyncHandler(async (req, res) => {
    // Trigger the sync-hubspot function by calling our own endpoint
    const baseUrl = process.env.BASE_URL || `http://localhost:${config.port}`;
    
    const response = await fetch(`${baseUrl}/api/hubspot/sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to trigger HubSpot sync');
    }

    const result = await response.json();
    res.json({ message: 'Sync triggered successfully', result });
  })
);

export default router;
