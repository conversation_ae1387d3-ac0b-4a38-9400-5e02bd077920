/**
 * @description This component renders a dedicated section for Klusgebied+ investment packages. It showcases three distinct tiers (Starter, Groei, Elite) for craftsmen to invest in the platform for benefits like higher visibility and guaranteed jobs. The component is designed to be fully responsive, featuring a side-by-side card layout on desktop and a horizontal scroll on mobile. It includes a functional modal form to capture investment interest, which saves data to Supabase and sends an email notification. Key variables include the investmentPackages data array, form state, and submission status management for the modal.
 */

import { CheckCircle, ArrowRight, X } from "lucide-react";
import { useNavigate } from "react-router-dom";

const InvestmentSection = () => {
  const navigate = useNavigate();

  const investmentPackages = [
    {
      id: "klusgebied-plus",
      name: "Klusgebied+",
      price: "€149 p/m",
      color: "yellow",
      bgColor: "bg-yellow-50",
      borderColor: "border-yellow-200",
      buttonColor: "bg-yellow-500 hover:bg-yellow-600",
      route: "/klusgebied-plus",
      features: [
        "Topvermelding in 1 gekozen regio",
        "5 gratis reacties per maand op klussen",
        "Professioneel ingericht profiel",
        "Klusgebied+ badge op je profiel",
      ],
    },
    {
      id: "klusgebied-pro",
      name: "Klusgebied Pro",
      price: "€299 p/m",
      color: "blue",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      buttonColor: "bg-blue-500 hover:bg-blue-600",
      route: "/klusgebied-pro",
      popular: true,
      features: [
        "Topvermelding in 3 regio's",
        "Onbeperkt reageren op klussen",
        "1-op-1 groeigesprek per kwartaal",
        "Professionele profielvideo (1 min)",
      ],
    },
    {
      id: "klusgebied-partner",
      name: "Klusgebied Partner",
      price: "€499 p/m",
      color: "purple",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      buttonColor: "bg-purple-500 hover:bg-purple-600",
      route: "/klusgebied-partner",
      features: [
        "Professionele website op maat",
        "Eigen domeinnaam + professioneel e-mailadres",
        "Professionele fotoshoot op locatie",
        "Regio-exclusiviteit (6 maanden gegarandeerd)",
      ],
    },
  ];

  const handlePackageSelect = (pkg: any) => {
    navigate(pkg.route);
  };

  return (
    <>
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-6">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-teal-100 rounded-full mb-6">
              <span className="text-2xl">📈</span>
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Investeer in Klusgebied+
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Word premium partner van Klusgebied via Klusgebied+. Investeer in
              jouw toekomst als vakman. Kies jouw pakket en profiteer van
              gegarandeerde klussen, hogere zichtbaarheid en vaste klanten via
              ons platform.
            </p>
          </div>

          {/* Investment Packages - Desktop: Side by side, Mobile: Horizontal scroll */}
          <div className="lg:grid lg:grid-cols-3 lg:gap-8 lg:max-w-7xl lg:mx-auto">
            <div className="flex lg:contents gap-6 overflow-x-auto lg:overflow-visible pb-6 lg:pb-0 px-4 lg:px-0">
              {investmentPackages.map((pkg, index) => (
                <div
                  key={pkg.id}
                  className={`
                    relative flex-shrink-0 w-80 lg:w-full ${pkg.bgColor} ${
                    pkg.borderColor
                  } 
                    border-2 rounded-xl p-8 motion-preset-slide-up motion-delay-${
                      index * 100
                    }
                    ${pkg.popular ? "ring-4 ring-teal-200 scale-105" : ""}
                  `}
                >
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-teal-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                        Meest Gekozen
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {pkg.name}
                    </h3>
                    <div className="text-4xl font-bold text-gray-900 mb-1">
                      {pkg.price}
                    </div>
                    <p className="text-gray-600">Maandelijks abonnement</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start">
                        <CheckCircle className="w-5 h-5 text-teal-500 mt-0.5 mr-3 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <button
                    onClick={() => handlePackageSelect(pkg)}
                    className={`
                      w-full ${pkg.buttonColor} text-white font-semibold py-4 px-6 rounded-lg
                      transition-all duration-200 flex items-center justify-center group
                    `}
                  >
                    Meer Informatie
                    <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Detailed Pricing Table */}
          <div className="mt-24">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                Uitgebreide Vergelijking
              </h3>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                Bekijk alle voordelen en services per pakket in detail. Klik op
                een pakket voor meer informatie.
              </p>
            </div>

            {/* Mobile: Horizontal Scroll Layout */}
            <div className="lg:hidden">
              <div className="flex gap-6 overflow-x-auto pb-6 px-4 pt-4">
                {[
                  {
                    name: "Klusgebied+",
                    price: "€149 p/m",
                    color: "yellow",
                    bgColor: "bg-yellow-50",
                    borderColor: "border-yellow-200",
                    features: {
                      Topvermelding: "1 regio",
                      "Aantal reacties op klussen": "5 per maand",
                      Profieloptimalisatie: true,
                      "Social media shout-out": "1x per kwartaal",
                      Nieuwsbriefvermelding: "1x per jaar",
                      "Toegang tot Klusgebied Community": true,
                      Support: "E-mail/chat (48u)",
                      "Groeigesprek (video call)": false,
                      "Professionele profielvideo (1 min)": false,
                      "Professionele fotoshoot op locatie": false,
                      "Bedrijfsvideo (60 sec)": false,
                      "Eigen website op maat": false,
                      "Domeinnaam + professioneel e-mailadres": false,
                      "Hosting & technisch onderhoud (1 jaar inbegrepen)":
                        false,
                      "Google Mijn Bedrijf registratie + SEO-optimalisatie":
                        false,
                      "Maandelijkse advertenties via Klusgebied": false,
                      "Regio-exclusiviteit (6 maanden)": false,
                      '"Founding Partner" badge': true,
                      'Vermelding op "Topvakmannen"-pagina': false,
                      "Looptijd abonnement": "12 maanden",
                      "Voordeel bij jaarbetaling": "1 maand gratis",
                    },
                  },
                  {
                    name: "Klusgebied Pro",
                    price: "€299 p/m",
                    color: "blue",
                    bgColor: "bg-blue-50",
                    borderColor: "border-blue-200",
                    popular: true,
                    features: {
                      Topvermelding: "3 regio's",
                      "Aantal reacties op klussen": "Onbeperkt",
                      Profieloptimalisatie: true,
                      "Social media shout-out": "1x per maand",
                      Nieuwsbriefvermelding: "3x per jaar",
                      "Toegang tot Klusgebied Community": true,
                      Support: "Prioriteitssupport",
                      "Groeigesprek (video call)": "1x per kwartaal",
                      "Professionele profielvideo (1 min)": true,
                      "Professionele fotoshoot op locatie": false,
                      "Bedrijfsvideo (60 sec)": false,
                      "Eigen website op maat": false,
                      "Domeinnaam + professioneel e-mailadres": false,
                      "Hosting & technisch onderhoud (1 jaar inbegrepen)":
                        false,
                      "Google Mijn Bedrijf registratie + SEO-optimalisatie":
                        false,
                      "Maandelijkse advertenties via Klusgebied":
                        "€50 inbegrepen",
                      "Regio-exclusiviteit (6 maanden)": false,
                      '"Founding Partner" badge': true,
                      'Vermelding op "Topvakmannen"-pagina': false,
                      "Looptijd abonnement": "12 maanden",
                      "Voordeel bij jaarbetaling": "Eerste 3 maanden voor €199",
                    },
                  },
                  {
                    name: "Klusgebied Partner",
                    price: "€499 p/m",
                    color: "purple",
                    bgColor: "bg-purple-50",
                    borderColor: "border-purple-200",
                    features: {
                      Topvermelding: "3 regio's",
                      "Aantal reacties op klussen": "Onbeperkt",
                      Profieloptimalisatie: true,
                      "Social media shout-out": "1x per maand",
                      Nieuwsbriefvermelding: "Doorlopend (min. 6x per jaar)",
                      "Toegang tot Klusgebied Community": true,
                      Support: "Persoonlijke accountmanager",
                      "Groeigesprek (video call)":
                        "1x per kwartaal + op aanvraag",
                      "Professionele profielvideo (1 min)": true,
                      "Professionele fotoshoot op locatie": true,
                      "Bedrijfsvideo (60 sec)": true,
                      "Eigen website op maat": true,
                      "Domeinnaam + professioneel e-mailadres": true,
                      "Hosting & technisch onderhoud (1 jaar inbegrepen)": true,
                      "Google Mijn Bedrijf registratie + SEO-optimalisatie":
                        true,
                      "Maandelijkse advertenties via Klusgebied":
                        "€100 inbegrepen",
                      "Regio-exclusiviteit (6 maanden)": true,
                      '"Founding Partner" badge': "✔️ (lifetime)",
                      'Vermelding op "Topvakmannen"-pagina': true,
                      "Looptijd abonnement": "Minimaal 6 maanden",
                      "Voordeel bij jaarbetaling":
                        "2 maanden gratis + gratis brochure",
                    },
                  },
                ].map((plan, index) => (
                  <div
                    key={plan.name}
                    onClick={() => navigate(investmentPackages[index].route)}
                    className={`
                    relative flex-shrink-0 w-80 ${plan.bgColor} ${
                      plan.borderColor
                    }
                    border-2 rounded-xl p-6 motion-preset-slide-up motion-delay-${
                      index * 100
                    }
                    ${
                      plan.popular ? "ring-4 ring-teal-200" : ""
                    } cursor-pointer hover:shadow-lg transition-shadow
                  `}
                  >
                    {plan.popular && (
                      <div className="flex justify-center mb-4">
                        <span className="bg-teal-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                          Meest Gekozen
                        </span>
                      </div>
                    )}

                    <div className="text-center mb-6">
                      <h4 className="text-xl font-bold text-gray-900 mb-2">
                        {plan.name}
                      </h4>
                      <div className="text-2xl font-bold text-gray-900">
                        {plan.price}
                      </div>
                    </div>

                    <div className="space-y-3">
                      {Object.entries(plan.features).map(([feature, value]) => (
                        <div
                          key={feature}
                          className="flex justify-between items-start"
                        >
                          <span className="text-sm font-medium text-gray-700 flex-1 pr-3">
                            {feature}
                          </span>
                          <span className="text-sm text-gray-900 font-semibold text-right">
                            {typeof value === "boolean" ? (
                              value ? (
                                <CheckCircle className="w-4 h-4 text-teal-500" />
                              ) : (
                                <X className="w-4 h-4 text-gray-400" />
                              )
                            ) : (
                              value
                            )}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Desktop: Table Layout */}
            <div className="hidden lg:block overflow-x-auto">
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50 border-b border-gray-200">
                      <th className="text-left py-6 px-6 font-bold text-gray-900 text-lg tracking-wide">
                        Kenmerk
                      </th>
                      <th
                        onClick={() => navigate("/klusgebied-plus")}
                        className="text-center py-6 px-6 font-bold text-yellow-600 text-lg tracking-wide bg-yellow-50 border-l border-r border-yellow-200 cursor-pointer hover:bg-yellow-100 transition-colors"
                      >
                        <div>
                          <div className="text-xl font-bold">Klusgebied+</div>
                          <div className="text-sm font-semibold mt-1">
                            (€149 p/m)
                          </div>
                        </div>
                      </th>
                      <th
                        onClick={() => navigate("/klusgebied-pro")}
                        className="text-center py-6 px-6 font-bold text-blue-600 text-lg tracking-wide bg-blue-50 border-l border-r border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors"
                      >
                        <div className="flex justify-center mb-2">
                          <span className="bg-teal-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                            Meest Gekozen
                          </span>
                        </div>
                        <div>
                          <div className="text-xl font-bold">
                            Klusgebied Pro
                          </div>
                          <div className="text-sm font-semibold mt-1">
                            (€299 p/m)
                          </div>
                        </div>
                      </th>
                      <th
                        onClick={() => navigate("/klusgebied-partner")}
                        className="text-center py-6 px-6 font-bold text-purple-600 text-lg tracking-wide bg-purple-50 border-l border-purple-200 cursor-pointer hover:bg-purple-100 transition-colors"
                      >
                        <div>
                          <div className="text-xl font-bold">
                            Klusgebied Partner
                          </div>
                          <div className="text-sm font-semibold mt-1">
                            (€499 p/m)
                          </div>
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {[
                      {
                        feature: "Topvermelding",
                        values: ["1 regio", "3 regio's", "3 regio's"],
                      },
                      {
                        feature: "Aantal reacties op klussen",
                        values: ["5 per maand", "Onbeperkt", "Onbeperkt"],
                      },
                      {
                        feature: "Profieloptimalisatie",
                        values: [true, true, true],
                      },
                      {
                        feature: "Social media shout-out",
                        values: [
                          "1x per kwartaal",
                          "1x per maand",
                          "1x per maand",
                        ],
                      },
                      {
                        feature: "Nieuwsbriefvermelding",
                        values: [
                          "1x per jaar",
                          "3x per jaar",
                          "Doorlopend (min. 6x per jaar)",
                        ],
                      },
                      {
                        feature: "Toegang tot Klusgebied Community",
                        values: [true, true, true],
                      },
                      {
                        feature: "Support",
                        values: [
                          "E-mail/chat (48u)",
                          "Prioriteitssupport",
                          "Persoonlijke accountmanager",
                        ],
                      },
                      {
                        feature: "Groeigesprek (video call)",
                        values: [
                          false,
                          "1x per kwartaal",
                          "1x per kwartaal + op aanvraag",
                        ],
                      },
                      {
                        feature: "Professionele profielvideo (1 min)",
                        values: [false, true, true],
                      },
                      {
                        feature: "Professionele fotoshoot op locatie",
                        values: [false, false, true],
                      },
                      {
                        feature: "Bedrijfsvideo (60 sec)",
                        values: [false, false, true],
                      },
                      {
                        feature: "Eigen website op maat",
                        values: [false, false, true],
                      },
                      {
                        feature: "Domeinnaam + professioneel e-mailadres",
                        values: [false, false, true],
                      },
                      {
                        feature:
                          "Hosting & technisch onderhoud (1 jaar inbegrepen)",
                        values: [false, false, true],
                      },
                      {
                        feature:
                          "Google Mijn Bedrijf registratie + SEO-optimalisatie",
                        values: [false, false, true],
                      },
                      {
                        feature: "Maandelijkse advertenties via Klusgebied",
                        values: [false, "€50 inbegrepen", "€100 inbegrepen"],
                      },
                      {
                        feature: "Regio-exclusiviteit (6 maanden)",
                        values: [false, false, true],
                      },
                      {
                        feature: '"Founding Partner" badge',
                        values: [true, true, "✔️ (lifetime)"],
                      },
                      {
                        feature: 'Vermelding op "Topvakmannen"-pagina',
                        values: [false, false, true],
                      },
                      {
                        feature: "Looptijd abonnement",
                        values: [
                          "12 maanden",
                          "12 maanden",
                          "Minimaal 6 maanden",
                        ],
                      },
                      {
                        feature: "Voordeel bij jaarbetaling",
                        values: [
                          "1 maand gratis",
                          "Eerste 3 maanden voor €199",
                          "2 maanden gratis + gratis brochure",
                        ],
                      },
                    ].map((row, index) => (
                      <tr
                        key={row.feature}
                        className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                      >
                        <td className="py-4 px-6 font-medium text-gray-900 text-base leading-relaxed">
                          {row.feature}
                        </td>
                        <td className="py-4 px-6 text-center bg-yellow-50 border-l border-r border-yellow-100">
                          {typeof row.values[0] === "boolean" ? (
                            row.values[0] ? (
                              <CheckCircle className="w-5 h-5 text-teal-500 mx-auto" />
                            ) : (
                              <X className="w-5 h-5 text-gray-400 mx-auto" />
                            )
                          ) : (
                            <span className="text-gray-900 font-medium text-base">
                              {row.values[0]}
                            </span>
                          )}
                        </td>
                        <td className="py-4 px-6 text-center bg-blue-50 border-l border-r border-blue-100">
                          {typeof row.values[1] === "boolean" ? (
                            row.values[1] ? (
                              <CheckCircle className="w-5 h-5 text-teal-500 mx-auto" />
                            ) : (
                              <X className="w-5 h-5 text-gray-400 mx-auto" />
                            )
                          ) : (
                            <span className="text-gray-900 font-medium text-base">
                              {row.values[1]}
                            </span>
                          )}
                        </td>
                        <td className="py-4 px-6 text-center bg-purple-50 border-l border-purple-100">
                          {typeof row.values[2] === "boolean" ? (
                            row.values[2] ? (
                              <CheckCircle className="w-5 h-5 text-teal-500 mx-auto" />
                            ) : (
                              <X className="w-5 h-5 text-gray-400 mx-auto" />
                            )
                          ) : (
                            <span className="text-gray-900 font-medium text-base">
                              {row.values[2]}
                            </span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <p className="text-gray-600 mb-6">
              Vragen over Klusgebied+ investeringsmogelijkheden?
            </p>
            <button
              onClick={() => navigate("/contact")}
              className="bg-gray-800 hover:bg-gray-900 text-white font-semibold px-8 py-3 rounded-lg transition-colors"
            >
              Neem Contact Op
            </button>
          </div>
        </div>
      </section>
    </>
  );
};

export default InvestmentSection;
