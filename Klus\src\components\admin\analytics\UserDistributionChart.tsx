import { useQuery } from "@tanstack/react-query";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { DateRange } from "react-day-picker";

import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";

interface UserDistributionChartProps {
  dateRange: DateRange | undefined;
}

export const UserDistributionChart = ({
  dateRange,
}: UserDistributionChartProps) => {
  const { data: userTypeDistribution } = useQuery({
    queryKey: ["userTypeDistribution", dateRange],
    queryFn: async () => {
      let query = supabase.from("profiles").select("user_type");

      if (dateRange?.from) {
        query = query.gte("created_at", dateRange.from.toISOString());
      }
      if (dateRange?.to) {
        query = query.lte("created_at", dateRange.to.toISOString());
      }

      const { data } = await query;
      const distribution = data?.reduce((acc: any, user) => {
        acc[user.user_type] = (acc[user.user_type] || 0) + 1;
        return acc;
      }, {});

      return Object.entries(distribution || {}).map(([name, value]) => ({
        name: name === "vakman" ? "Vakmannen" : "Klusaanvragers",
        value,
      }));
    },
  });

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Gebruikers Verdeling</h2>
      <div className="h-[300px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={userTypeDistribution || []}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="value" fill="#3b82f6" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
};
