/**
 * @description This component renders a comprehensive and SEO-optimized detail page for smart home services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for smart home specialists. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Home,
  Zap,
  SlidersHorizontal,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  User<PERSON>heck,
  CheckCircle,
  MapPin,
  Lightbulb,
  Thermometer,
  ShieldCheck,
} from "lucide-react";

const Service_SmartHomeSpecialistPage = () => {
  usePageTitle(
    "Smart Home Specialist Nodig? | Klusgebied - Domotica Installatie"
  );
  const navigate = useNavigate();

  const smartHomeServices = [
    {
      icon: Lightbulb,
      title: "Slimme Verlichting",
      description:
        "Creëer de perfecte sfeer en bespaar energie met slimme lampen en schakelaars.",
      points: [
        "Pas kleur en helderheid aan via een app.",
        "Stel schema's in voor zonsopgang of filmavond.",
        "Bespaar energie door lampen automatisch uit te schakelen.",
        "Integratie met Philips Hue, IKEA Trådfri en meer.",
      ],
    },
    {
      icon: Thermometer,
      title: "Slimme Thermostaten",
      description:
        "Verwarm uw huis efficiënt en op afstand met een slimme thermostaat.",
      points: [
        "Leert uw leefpatroon en past de verwarming daarop aan.",
        "Bedien de temperatuur op afstand, waar u ook bent.",
        "Bespaar aanzienlijk op uw stookkosten.",
        "Werkt met Google Nest, Tado, en andere merken.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Slimme Beveiliging",
      description:
        "Integreer deurbellen, camera's en sloten in uw smart home systeem.",
      points: [
        "Koppel uw slimme deurbel, camera's en sloten.",
        "Ontvang meldingen bij beweging en kijk wie er voor de deur staat.",
        "Geef op afstand toegang aan familie of de pakketbezorger.",
        'Creëer scenario\'s zoals "alle lichten aan" bij een alarm.',
      ],
    },
    {
      icon: SlidersHorizontal,
      title: "Volledige Domotica Integratie",
      description:
        "Laat al uw slimme apparaten naadloos met elkaar samenwerken.",
      points: [
        "Eén centraal systeem voor al uw apparaten (Homey, Home Assistant).",
        "Laat de gordijnen sluiten als de film start.",
        "Automatiseer uw huis op basis van tijd, locatie of sensoren.",
        "Wij zorgen dat alles perfect en betrouwbaar werkt.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Home className="w-8 h-8 text-white" />,
      title: "Comfort & Gemak",
      description:
        "Automatiseer routinetaken en bedien uw huis met één druk op de knop of uw stem.",
    },
    {
      icon: <Zap className="w-8 h-8 text-white" />,
      title: "Energiebesparing",
      description:
        "Bespaar op uw energierekening door slimme thermostaten en verlichting.",
    },
    {
      icon: <SlidersHorizontal className="w-8 h-8 text-white" />,
      title: "Toekomstbestendig Wonen",
      description:
        "Maak uw huis klaar voor de toekomst met een flexibel en uitbreidbaar domoticasysteem.",
    },
  ];

  const faqs = [
    {
      question: "Wat is domotica of een smart home?",
      answer:
        "Domotica is het automatiseren van verschillende functies in huis, zoals verlichting, verwarming en beveiliging, die u centraal kunt bedienen via een app of spraakassistent.",
    },
    {
      question: "Welke systemen en merken werken goed samen?",
      answer:
        "Er zijn veel verschillende systemen zoals Philips Hue, Google Nest, en Somfy. Onze specialisten adviseren u over de beste combinatie van producten die naadloos samenwerken.",
    },
    {
      question: "Is een smart home wel veilig voor hackers?",
      answer:
        "Veiligheid is cruciaal. Wij zorgen voor een beveiligd netwerk, sterke wachtwoorden en regelmatige updates om uw smart home te beschermen tegen ongewenste toegang.",
    },
    {
      question: "Kan ik mijn bestaande apparaten slim maken?",
      answer:
        "Ja, veel bestaande apparaten en lampen kunnen slim gemaakt worden met behulp van slimme stekkers, schakelaars of modules. Dit is vaak een voordelige eerste stap.",
    },
  ];

  const reviews = [
    {
      name: "Laura van Vliet",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Mijn huis is echt getransformeerd! De specialist van Klusgebied heeft alles perfect geïntegreerd. De lichten die langzaam aangaan als ik wakker word, zijn mijn favoriet. Geweldig!",
      highlighted: true,
    },
    {
      name: "Daan Meijer",
      location: "Groningen",
      rating: 5,
      quote:
        "Ik ben een tech-liefhebber, maar de installatie zelf doen zag ik niet zitten. De monteur was super kundig en had alles snel en netjes werkend. Een aanrader voor iedereen die een smart home overweegt.",
      highlighted: false,
    },
    {
      name: "Familie Singh",
      location: "Den Haag",
      rating: 5,
      quote:
        "De combinatie van slimme verlichting en de thermostaat bespaart ons echt geld. Het is ook heel handig dat we de verwarming alvast aan kunnen zetten als we naar huis rijden.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1633194883650-df448a10d554?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzZWN1cml0eSUyQyUyMHNtYXJ0JTIwaG9tZXxlbnwwfHx8fDE3NTIxODEwNzd8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1596178065887-1198b6148b2b?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1706759755832-47e53579cc0d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzbWFydCUyMGhvbWUlMjBkZXZpY2VzJTJDJTIwaW50ZWdyYXRpb24lMkMlMjB0ZWNobm9sb2d5fGVufDB8fHx8MTc1MTc0MTU5M3ww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1677258523144-a659ca521a07?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwyfHxzZWN1cml0eSUyQyUyMHNtYXJ0JTIwaG9tZXxlbnwwfHx8fDE3NTIxODEwNzd8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Deel uw ideeën",
      description: "Vertel ons wat u wilt automatiseren en wat uw wensen zijn.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Ontvang een plan",
      description:
        "Krijg een advies op maat en een offerte van een domotica specialist.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & Geniet",
      description: "Kies de beste specialist en geniet van uw slimme huis.",
      microcopy: "Vergelijk profielen en beoordelingen",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Domotica Specialisten in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "domotica specialist",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Smart Home Specialist Nodig? | Klusgebied - Domotica Installatie
        </title>
        <meta
          name="description"
          content="Maak uw huis slimmer met een professionele domotica installatie. Vraag gratis offertes aan voor slimme verlichting, thermostaten, beveiliging en meer."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-teal-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-purple-100 border border-purple-200/80 rounded-full px-4 py-2 mb-6">
                    <Home className="w-5 h-5 text-purple-600" />
                    <span className="text-purple-800 font-semibold text-sm">
                      Smart Home Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Smart Home Specialist nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-indigo-500 mt-2">
                      Het huis van de toekomst
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Maak uw leven makkelijker, comfortabeler en energiezuiniger
                    met een professioneel geïnstalleerd domoticasysteem.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus")}
                      className="group inline-flex items-center justify-center bg-purple-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-purple-600 transition-all duration-300 shadow-lg hover:shadow-purple-500/30 transform hover:-translate-y-1"
                    >
                      Vind een domotica specialist
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>
              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1633194883650-df448a10d554?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzZWN1cml0eSUyQyUyMHNtYXJ0JTIwaG9tZXxlbnwwfHx8fDE3NTIxODEwNzd8MA&ixlib=rb-4.1.0?w=1024&h=1024"
                    alt="Hand die een smartphone gebruikt om een smart home te bedienen"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte specialist voor uw smart
                home.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-purple-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-purple-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Smart Home Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Wij integreren de beste slimme technologieën naadloos in uw
                woning.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {smartHomeServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-purple-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-purple-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die hun huis slim lieten maken via
                Klusgebied.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-purple-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-purple-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-purple-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-purple-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een ervaren specialist en geniet van een perfect
                werkend en veilig smart home.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-purple-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(`/stad/${city.toLowerCase().replace(/\s+/g, "-")}`)
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-purple-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-purple-600 to-indigo-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar voor het huis van de toekomst?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvend adviesgesprek aan en ontdek de eindeloze
              mogelijkheden van een smart home.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus")}
              className="bg-white text-purple-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Start met een smart home advies
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus")}
          className="w-full group inline-flex items-center justify-center bg-purple-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-purple-600 transition-all duration-300 shadow-lg hover:shadow-purple-500/30 transform hover:-translate-y-1"
        >
          <Home className="w-5 h-5 mr-2" />
          Vraag offerte aan
        </button>
      </div>
    </div>
  );
};

export default Service_SmartHomeSpecialistPage;
