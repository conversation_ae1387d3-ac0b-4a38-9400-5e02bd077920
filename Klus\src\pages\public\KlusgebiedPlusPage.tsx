/**
 * @description Package detail page for Klusgebied+ (€149 per month) subscription plan.
 * Provides comprehensive information about features, benefits, and pricing for craftsmen
 * who want local visibility and regular job opportunities.
 */
import { useState } from "react";
import { Helmet } from "react-helmet-async";
import {
  CheckCircle,
  ArrowRight,
  Star,
  Users,
  MapPin,
  MessageSquare,
  Mail,
  Phone,
} from "lucide-react";

import Footer from "@/components/landing/Footer";
import ContactFormModal from "@/components/modal/ContactFormModal";
import usePageTitle from "@/hooks/usePageTitle";

const KlusgebiedPlusPage = () => {
  usePageTitle("Klusgebied+ - €149 per maand | Premium Vakman Pakket");
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  const features = [
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "Topvermelding in 1 gekozen regio",
      description:
        "Je verschijnt bovenaan bij relevante klussen in jouw vakgebied.",
    },
    {
      icon: <Star className="w-6 h-6" />,
      title: "Professioneel ingericht profiel",
      description:
        "Wij helpen je profiel overtuigend, compleet en aantrekkelijk te maken.",
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      title: "Klusgebied+ badge op je profiel",
      description:
        "Laat klanten zien dat je actief en betrouwbaar bent binnen Klusgebied.",
    },
    {
      icon: <MessageSquare className="w-6 h-6" />,
      title: "5 gratis reacties per maand op klussen",
      description: "Reageer zonder extra kosten op klussen in jouw regio.",
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "1 social media shout-out per kwartaal",
      description:
        "Jouw profiel wordt gepromoot via onze Facebook en Instagram.",
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Toegang tot de Klusgebied Community",
      description:
        "Netwerk met andere vakmannen en blijf op de hoogte van updates en kansen.",
    },
  ];

  const supportFeatures = [
    {
      icon: <Mail className="w-5 h-5" />,
      title: "E-mail support",
      description: "Binnen 48 uur",
    },
    {
      icon: <MessageSquare className="w-5 h-5" />,
      title: "Chat support",
      description: "Binnen 48 uur",
    },
  ];

  const handleGetStarted = () => {
    setIsContactModalOpen(true);
  };

  const footerProps = {
    logoSrc:
      "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c (1)_LRfmi8Vt.png",
    brandName: "Klusgebied",
    description:
      "Vind snel en eenvoudig een geverifieerde vakman in jouw regio voor elke klus.",
    contact: {
      address: "Slotermeerlaan 58, 1064 HC Amsterdam",
      phone: "+31 123 456 789",
      email: "<EMAIL>",
    },
    links: {
      "Voor Klanten": [
        { label: "Vind een Vakman", href: "/diensten" },
        { label: "Klantenservice", href: "/klantenservice" },
        { label: "Garantie", href: "/garantie" },
        { label: "Veelgestelde Vragen", href: "/faq" },
      ],
      "Voor Vakmensen": [
        { label: "Aanmelden als Vakman", href: "/vakman" },
        { label: "Voordelen", href: "/vakman" },
        { label: "Investeer in Klusgebied+", href: "/investeren" },
        { label: "Support", href: "/support" },
      ],
    },
    socials: [
      { name: "Facebook", href: "#" },
      { name: "Twitter", href: "https://x.com/heybossAI" },
      { name: "Instagram", href: "#" },
      {
        name: "LinkedIn",
        href: "https://www.linkedin.com/company/heyboss-xyz/",
      },
    ],
    legal: {
      privacyPolicyUrl: "https://legal.heyboss.tech/67845a5e6e6bf5ecd4a3ae47/",
      termsAndConditionsUrl:
        "https://legal.heyboss.tech/67845cfe76f9675292514b80/",
      year: 2025,
    },
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      <Helmet>
        <title>Klusgebied+ - €149 per maand | Premium Vakman Pakket</title>
        <meta
          name="description"
          content="Klusgebied+ voor €149 per maand. Voor vakmannen die lokaal zichtbaar willen zijn en regelmatig nieuwe klussen willen binnenhalen. Topvermelding, professioneel profiel en meer."
        />
        <meta
          property="og:title"
          content="Klusgebied+ - €149 per maand | Premium Vakman Pakket"
        />
        <meta
          property="og:description"
          content="Klusgebied+ voor €149 per maand. Voor vakmannen die lokaal zichtbaar willen zijn en regelmatig nieuwe klussen willen binnenhalen."
        />
        <meta
          property="og:image"
          content="https://heyboss.heeyo.ai/user-assets/7265ee1d-7ceb-41ac-a6af-bc9499143883_3xnKAaGW.png"
        />
        <meta name="twitter:card" content="summary_large_image" />
      </Helmet>

      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-yellow-50 to-yellow-100 py-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-yellow-500 rounded-full mb-8">
                <Star className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-5xl font-bold text-gray-900 mb-6 tracking-wide leading-tight">
                Klusgebied+
              </h1>
              <div className="text-4xl font-bold text-yellow-600 mb-4">
                €149 per maand{" "}
                <span className="text-lg text-gray-600 font-normal">
                  (excl. btw)
                </span>
              </div>
              <p className="text-xl text-gray-700 mb-8 leading-relaxed tracking-wide">
                Voor vakmannen die lokaal zichtbaar willen zijn en regelmatig
                nieuwe klussen willen binnenhalen.
              </p>
              <button
                onClick={handleGetStarted}
                className="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold px-8 py-4 rounded-lg text-lg transition-all duration-200 flex items-center justify-center mx-auto group"
              >
                Start Nu
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-16 tracking-wide leading-relaxed">
                Wat je krijgt
              </h2>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className="bg-gray-50 p-8 rounded-xl border border-gray-200 hover:shadow-lg transition-shadow"
                  >
                    <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mb-6">
                      <div className="text-yellow-600">{feature.icon}</div>
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-3 tracking-wide leading-relaxed">
                      {feature.title}
                    </h3>
                    <p className="text-base text-gray-600 leading-relaxed tracking-wide">
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Support Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-12 tracking-wide leading-relaxed">
                Support & Ondersteuning
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {supportFeatures.map((feature, index) => (
                  <div
                    key={index}
                    className="bg-white p-6 rounded-lg border border-gray-200 flex items-center"
                  >
                    <div className="flex items-center justify-center w-10 h-10 bg-yellow-100 rounded-lg mr-4">
                      <div className="text-yellow-600">{feature.icon}</div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 tracking-wide">
                        {feature.title}
                      </h3>
                      <p className="text-base text-gray-600 tracking-wide">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Contract Details */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-12 tracking-wide leading-relaxed">
                Contract Details
              </h2>
              <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-8">
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-4 tracking-wide">
                      Looptijd
                    </h3>
                    <p className="text-base text-gray-700 leading-relaxed tracking-wide">
                      Jaarcontract (12 maanden)
                    </p>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-4 tracking-wide">
                      Bonus
                    </h3>
                    <p className="text-base text-gray-700 leading-relaxed tracking-wide">
                      Betaal je 12 maanden vooruit? Dan krijg je 1 maand gratis.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-br from-yellow-500 to-yellow-600">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-4xl font-bold text-white mb-6 tracking-wide leading-tight">
                Klaar om te starten?
              </h2>
              <p className="text-xl text-yellow-100 mb-8 leading-relaxed tracking-wide">
                Begin vandaag nog met het uitbreiden van je klantenkring en
                verhoog je zichtbaarheid.
              </p>
              <button
                onClick={handleGetStarted}
                className="bg-white text-yellow-600 hover:bg-gray-100 font-semibold px-8 py-4 rounded-lg text-lg transition-all duration-200 flex items-center justify-center mx-auto group"
              >
                Neem Contact Op
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </section>
      </main>

      <Footer {...footerProps} />

      <ContactFormModal
        isOpen={isContactModalOpen}
        setIsOpen={setIsContactModalOpen}
        packageName="Klusgebied+"
        packagePrice="€149 per maand"
      />
    </div>
  );
};

export default KlusgebiedPlusPage;
