import { useGoogleReCaptcha } from "react-google-recaptcha-v3";

import { useToast } from "@/hooks/use-toast";

export const useRecaptcha = () => {
  const { executeRecaptcha } = useGoogleReCaptcha();
  const { toast } = useToast();

  const verifyRecaptcha = async () => {
    if (!executeRecaptcha) {
      console.error("reCAPTCHA not initialized");
      toast({
        variant: "destructive",
        title: "Fout",
        description:
          "Kon de beveiligingscheck niet uitvoeren. Probeer het later opnieuw.",
      });
      return false;
    }

    try {
      const token = await executeRecaptcha("form_submit");

      if (!token) {
        return false;
      }

      // Verify token with backend
      const { backendApi } = await import("@/lib/backendApi");
      const result = await backendApi.verifyRecaptcha(token);

      return result.success === true;
    } catch (error) {
      console.error("reCAPTCHA error:", error);
      toast({
        variant: "destructive",
        title: "Fout",
        description: "Beveiligingscheck mislukt. Probeer het later opnieuw.",
      });
      return false;
    }
  };

  return { verifyRecaptcha };
};
