import { useGoogleReCaptcha } from "react-google-recaptcha-v3";

import { useToast } from "@/hooks/use-toast";

export const useRecaptcha = () => {
  const { executeRecaptcha } = useGoogleReCaptcha();
  const { toast } = useToast();

  const verifyRecaptcha = async () => {
    if (!executeRecaptcha) {
      console.error("reCAPTCHA not initialized");
      toast({
        variant: "destructive",
        title: "Fout",
        description:
          "Kon de beveiligingscheck niet uitvoeren. Probeer het later opnieuw.",
      });
      return false;
    }

    try {
      const token = await executeRecaptcha("form_submit");

      // Hier zou je normaal gesproken het token valideren met je backend
      // Voor nu simuleren we een simpele check
      if (token) {
        return true;
      }

      return false;
    } catch (error) {
      console.error("reCAPTCHA error:", error);
      toast({
        variant: "destructive",
        title: "Fout",
        description: "Beveiligingscheck mislukt. <PERSON>beer het later opnieuw.",
      });
      return false;
    }
  };

  return { verifyRecaptcha };
};
