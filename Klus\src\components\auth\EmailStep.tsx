import { ChangeEvent, useCallback, useEffect, useRef, useState } from "react";
import debounce from "lodash/debounce";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { useTranslation } from "react-i18next"; // <-- 1. Import hook

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { ViewType } from "@/types/auth";

interface EmailStepProps {
  register: UseFormRegister<any>;
  errors: FieldErrors;
  view: ViewType;
  onExistingAccount?: () => void;
  onSubmit: (email: string) => void;
  onNewAccount?: () => void;
}

export const EmailStep = ({
  register,
  errors,
  view,
  onExistingAccount,
  onNewAccount,
}: EmailStepProps) => {
  const { t } = useTranslation(); // <-- 2. Initialize translator
  const [isChecking, setIsChecking] = useState(false);
  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []);

  const debouncedValidateEmail = useCallback(
    debounce(
      async (email: string, resolve: (value: boolean | string) => void) => {
        abortControllerRef.current?.abort();
        const controller = new AbortController();
        abortControllerRef.current = controller;
        setIsChecking(true);

        try {
          const { data: profile, error } = await supabase
            .from("profiles")
            .select("id, status")
            .eq("email", email)
            .abortSignal(controller.signal)
            .maybeSingle();

          if (error) throw error;

          if (!profile) {
            onNewAccount?.();
            resolve(true);
            return;
          }

          if (profile.status === "inactive") {
            toast({
              variant: "destructive",
              title: t("toastAccountBlockedTitle"),
              description: t("toastAccountBlockedDesc"),
            });
            resolve("inactive"); // This is a special keyword, do not translate
            return;
          }

          onExistingAccount?.();
          toast({
            variant: "destructive",
            title: t("toastEmailExistsTitle"),
            description: t("toastEmailExistsDesc"),
          });
          resolve(t("errorEmailExists")); // Return translated message for form error
        } catch (error) {
          if (error instanceof Error) {
            if (error.name === "AbortError") return;
            console.error("Email validation error:", error);
            toast({
              variant: "destructive",
              title: t("toastValidationErrorTitle"),
              description: t("toastValidationErrorDesc"),
            });
            resolve(t("errorValidationGeneric")); // Return translated message
          }
        } finally {
          setIsChecking(false);
          if (abortControllerRef.current === controller) {
            abortControllerRef.current = null;
          }
        }
      },
      500
    ),
    [onExistingAccount, onNewAccount, toast, t] // <-- Add `t` to dependency array
  );

  const validateEmail = async (email: string) => {
    return new Promise<boolean | string>((resolve) => {
      debouncedValidateEmail(email, resolve);
    });
  };

  const handleChangeEmail = async (e: ChangeEvent<HTMLInputElement>) => {
    const email = e.target.value;
    if (email) {
      const emailPattern = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
      if (!emailPattern.test(email)) return;
      
      const result = await validateEmail(email);
      // Special handling for inactive accounts
      if (result === "inactive") {
        e.target.value = ""; // Clear input if the account is inactive
      }
      // Note: Removed redundant toast call here. It's already handled in debouncedValidateEmail.
    }
  };

  return (
    <div className="space-y-2">
      {view === "sign_up" && (
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          {t("emailStepTitle")}
        </h2>
      )}
      <Label htmlFor="email">{t("emailLabel")}</Label>
      <Input
        id="email"
        type="email"
        placeholder={t("emailPlaceholder")}
        {...register("email", {
          required: t("errorEmailRequired"),
          pattern: {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: t("errorEmailInvalid"),
          },
          onChange: handleChangeEmail,
        })}
        isLoading={isChecking}
      />
      {errors.email && (
        <p className="text-sm text-red-500">{errors.email.message as string}</p>
      )}
    </div>
  );
};