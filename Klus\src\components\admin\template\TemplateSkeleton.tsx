import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface TemplateSkeletonProps {
  viewMode: "grid" | "list";
}

export function TemplateSkeleton({ viewMode }: TemplateSkeletonProps) {
  const GridSkeleton = () => (
    <div className="border rounded-lg overflow-hidden">
      <div className="aspect-video relative">
        <Skeleton className="w-full h-full" />
      </div>
      <div className="p-4 space-y-4">
        <div className="space-y-2">
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
        </div>
        <div className="flex items-center gap-2 pt-2 border-t">
          <Skeleton className="h-9 flex-1" />
          <Skeleton className="h-9 flex-1" />
        </div>
      </div>
    </div>
  );

  const ListSkeleton = () => (
    <div className="overflow-hidden border rounded-lg">
      <div className="flex gap-4 p-4">
        <Skeleton className="w-40 h-28 flex-shrink-0 rounded-lg" />
        <div className="flex-1 space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-6 w-2/3" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-28" />
            <Skeleton className="h-9 w-28" />
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile View (Always Grid) */}
      <div className="block sm:hidden">
        <div className="grid gap-4 grid-cols-1">
          {Array.from({ length: 2 }).map((_, i) => (
            <GridSkeleton key={i} />
          ))}
        </div>
      </div>

      {/* Desktop View */}
      <div className="hidden sm:block">
        {viewMode === "grid" ? (
          <div
            className={cn(
              "grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
            )}
          >
            {Array.from({ length: 6 }).map((_, i) => (
              <GridSkeleton key={i} />
            ))}
          </div>
        ) : (
          <div className="flex flex-col gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <ListSkeleton key={i} />
            ))}
          </div>
        )}
      </div>
    </>
  );
}
