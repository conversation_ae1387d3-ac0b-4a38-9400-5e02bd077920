import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Pencil, Star, User } from "lucide-react";

import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ContactInformation } from "./detail/ContactInformation";
import { BusinessInformation } from "./detail/BusinessInformation";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { useAuth } from "../auth/hooks/useAuth";

interface VakmanProfileDialogProps {
  isOpen: boolean;
  onClose: () => void;
  vakmanId: string;
  isAccepted?: boolean;
}

export const VakmanProfileDialog = ({
  isOpen,
  onClose,
  vakmanId,
  isAccepted = false,
}: VakmanProfileDialogProps) => {
  const { userProfile } = useAuth();

  const [reviews, setReviews] = useState<any[]>([]);
  const [averageRating, setAverageRating] = useState<number>(0);
  const [profile, setProfile] = useState<any>(null);
  // Add new state for editing
  const [editingReviewId, setEditingReviewId] = useState<string | null>(null);
  const [editedComment, setEditedComment] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);
  const [respondingToReviewId, setRespondingToReviewId] = useState<
    string | null
  >(null);
  const [reviewResponse, setReviewResponse] = useState("");
  const [isSubmittingResponse, setIsSubmittingResponse] = useState(false);
  const [showAllReviews, setShowAllReviews] = useState(false);

  useEffect(() => {
    const fetchProfileData = async () => {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", vakmanId)
        .single();

      if (error) {
        console.error("Error fetching profile:", error);
        return;
      }
      setProfile(data);
    };

    const fetchReviews = async () => {
      const { data, error } = await supabase
        .from("vakman_reviews")
        .select(
          `
          *,
          reviewer:profiles!vakman_reviews_reviewer_id_fkey (
            first_name,
            last_name
          ),
          job:jobs (
            title
          )
        `
        )
        .eq("vakman_id", vakmanId);

      if (error) {
        console.error("Error fetching reviews:", error);
        return;
      }

      setReviews(data || []);
      const averageRating = data?.length
        ? data.reduce((acc, review) => acc + review.rating, 0) / data.length
        : 0;
      setAverageRating(averageRating);
    };

    const fetchData = async () => {
      await Promise.all([fetchProfileData(), fetchReviews()]);
    };

    if (isOpen && vakmanId) {
      fetchData();
    }

    return () => {
      // Cleanup on unmount or when dependencies change
      setProfile(null);
      setReviews([]);
      setAverageRating(0);
    };
  }, [isOpen, vakmanId]);

  const handleEditClick = (review: any) => {
    setEditingReviewId(review.id);
    setEditedComment(review.comment);
  };

  const handleSaveEdit = async (reviewId: string) => {
    setIsUpdating(true);
    try {
      const { error } = await supabase
        .from("vakman_reviews")
        .update({ comment: editedComment })
        .eq("id", reviewId);

      if (error) throw error;

      // Update local state
      setReviews(
        reviews.map((review) =>
          review.id === reviewId
            ? { ...review, comment: editedComment }
            : review
        )
      );

      setEditingReviewId(null);
    } catch (error) {
      console.error("Error updating review:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingReviewId(null);
    setEditedComment("");
  };

  const handleReviewFeedback = async (reviewId: string) => {
    const review = reviews.find((r) => r.id === reviewId);
    setRespondingToReviewId(reviewId);
    setReviewResponse(review?.feedback || "");
  };

  const handleSaveResponse = async (reviewId: string) => {
    setIsSubmittingResponse(true);
    try {
      const { error } = await supabase
        .from("vakman_reviews")
        .update({
          feedback: reviewResponse,
        })
        .eq("id", reviewId);

      if (error) throw error;

      // Update local state
      setReviews(
        reviews.map((review) =>
          review.id === reviewId
            ? { ...review, feedback: reviewResponse }
            : review
        )
      );

      setRespondingToReviewId(null);
      setReviewResponse("");
    } catch (error) {
      console.error("Error saving response:", error);
    } finally {
      setIsSubmittingResponse(false);
    }
  };

  const handleCancelResponse = () => {
    setRespondingToReviewId(null);
    setReviewResponse("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Vakman Profiel</DialogTitle>
        </DialogHeader>

        {profile && (
          <div className="space-y-6">
            {/* Profile Header */}
            <div className="flex items-start gap-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={profile.profile_photo_url} />
                <AvatarFallback className="bg-primary">
                  <User className="h-10 w-10 text-white" />
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-2xl font-bold">
                  {profile.company_name ||
                    `${profile.first_name} ${profile.last_name}`}
                </h2>
                {profile.company_name && (
                  <p className="font-bold">
                    {profile.first_name} {profile.last_name}
                  </p>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <ContactInformation
              email={profile.email}
              phoneNumber={profile.phone_number}
              isAccepted={isAccepted}
            />

            {/* Business Information */}
            <BusinessInformation
              id={vakmanId}
              kvkNumber={profile.kvk_number}
              btwNumber={profile.btw_number}
              isAccepted={isAccepted}
            />

            {/* Reviews Section */}
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Beoordelingen</h3>

              {reviews.length > 0 ? (
                <>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-semibold">
                      {averageRating.toFixed(1)}
                    </span>
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-5 w-5 ${
                            star <= averageRating
                              ? "text-yellow-400 fill-yellow-400"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-muted-foreground">
                      ({reviews.length}{" "}
                      {reviews.length === 1 ? "beoordeling" : "beoordelingen"})
                    </span>
                  </div>

                  <div className="space-y-4 max-h-[400px] overflow-y-auto">
                    {(showAllReviews ? reviews : reviews.slice(0, 5)).map(
                      (review) => (
                        <div
                          key={review.id}
                          className="border rounded-lg p-4 space-y-2"
                        >
                          <div className="flex items-center justify-between">
                            {/* Rating and reviewer info */}
                            <div className="flex items-center gap-2">
                              <div className="flex">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <Star
                                    key={star}
                                    className={`h-4 w-4 ${
                                      star <= review.rating
                                        ? "text-yellow-400 fill-yellow-400"
                                        : "text-gray-300"
                                    }`}
                                  />
                                ))}
                              </div>
                              <span className="text-sm text-muted-foreground">
                                door {review.reviewer.first_name}{" "}
                                {review.reviewer.last_name}
                              </span>
                            </div>

                            {/* Date and actions */}
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-muted-foreground">
                                {new Date(
                                  review.created_at
                                ).toLocaleDateString()}
                              </span>

                              {/* Action buttons */}
                              <div className="flex gap-1">
                                {userProfile.id === review.reviewer_id && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 hover:bg-muted"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleEditClick(review);
                                    }}
                                  >
                                    <Pencil className="h-4 w-4 text-muted-foreground" />
                                  </Button>
                                )}
                                {userProfile.id === vakmanId && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 hover:bg-muted group relative"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleReviewFeedback(review.id);
                                    }}
                                    title="Reageer op deze beoordeling"
                                  >
                                    <MessageCircle className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                                    {review.feedback?.length > 0 && (
                                      <span className="absolute -top-1 -right-1 h-2 w-2 bg-primary rounded-full" />
                                    )}
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                          {editingReviewId === review.id ? (
                            <div className="space-y-2">
                              <Textarea
                                value={editedComment}
                                onChange={(e) =>
                                  setEditedComment(e.target.value)
                                }
                                className="min-h-[100px]"
                                placeholder="Schrijf je beoordeling..."
                              />
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={handleCancelEdit}
                                  disabled={isUpdating}
                                >
                                  Annuleren
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => handleSaveEdit(review.id)}
                                  disabled={isUpdating}
                                >
                                  {isUpdating ? "Opslaan..." : "Opslaan"}
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <>
                              <p className="text-sm">{review.comment}</p>
                              <p className="text-sm text-muted-foreground">
                                Klus: {review.job.title}
                              </p>
                            </>
                          )}
                          {respondingToReviewId === review.id ? (
                            <div className="space-y-2 mt-4 border-t pt-4">
                              <label className="text-sm font-medium">
                                Je reactie
                              </label>
                              <Textarea
                                value={reviewResponse}
                                onChange={(e) =>
                                  setReviewResponse(e.target.value)
                                }
                                className="min-h-[100px]"
                                placeholder="Schrijf je reactie op deze beoordeling..."
                              />
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={handleCancelResponse}
                                  disabled={isSubmittingResponse}
                                >
                                  Annuleren
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => handleSaveResponse(review.id)}
                                  disabled={isSubmittingResponse}
                                >
                                  {isSubmittingResponse
                                    ? "Opslaan..."
                                    : "Opslaan"}
                                </Button>
                              </div>
                            </div>
                          ) : review.feedback ? (
                            <div className="mt-4 border-t pt-4">
                              <div className="flex items-center gap-2 mb-2">
                                <MessageCircle className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm font-medium">
                                  Reactie van vakman
                                </span>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {review.feedback}
                              </p>
                            </div>
                          ) : null}
                        </div>
                      )
                    )}
                  </div>
                  {reviews.length > 5 && (
                    <div className="flex justify-center pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowAllReviews(!showAllReviews)}
                      >
                        {showAllReviews
                          ? "Minder weergeven"
                          : "Alle beoordelingen"}
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-8 border rounded-lg bg-muted/10">
                  <p className="text-muted-foreground">
                    Deze vakman heeft nog geen beoordelingen ontvangen.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
