import { useState, useEffect } from "react";
import { APIProvider, Map, AdvancedMarker } from "@vis.gl/react-google-maps";
import { X, Phone, Mail, Building2, MapPin, ChevronDown } from "lucide-react";

import { serviceCategories } from "@/components/profile/ProfileServices";
import { supabase } from "@/integrations/supabase/client";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TooltipArrow,
} from "@/components/ui/tooltip";

interface Craftsman {
  id: string;
  street_address: string;
  postal_code: string;
  full_name: string;
  services: string[];
  balance: number;
  email: string;
  phone_number: string;
  kvk_number: string;
  profile_photo_url: string;
}

interface Location {
  id: string;
  lat: number;
  lng: number;
  name: string;
  services: string[];
  balance: number; // Add balance to Location interface
}

// Add CategoryColors type
type CategoryColors = {
  [key: string]: string;
};

// Add the same color mapping from CraftsmenMapView
const categoryColors: CategoryColors = {
  "Algemene klussen & bouw": "#10b981", // emerald-500
  "Installatie & leidingwerk": "#0ea5e9", // sky-500
  "Elektra & domotica": "#f59e0b", // amber-500
  "Woningrenovatie & interieur": "#a855f7", // purple-500
  "Onderhoud & reparatie": "#f43f5e", // rose-500
  "Tuin & buitenwerk": "#6366f1", // indigo-500
  "Schoonmaak & specials": "#64748b", // slate-500
};

interface CraftsmenMapProps {
  craftsmen?: Array<Craftsman>;
}

const CraftsmenMap = ({ craftsmen = [] }: CraftsmenMapProps) => {
  const [locations, setLocations] = useState<Location[]>([]);
  const [selectedTooltip, setSelectedTooltip] = useState<string | null>(null);
  const [openServices, setOpenServices] = useState<string>("");

  const center = { lat: 52.3676, lng: 4.9041 }; // Amsterdam center
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || "";

  useEffect(() => {
    const geocodeAddress = async (address: string) => {
      try {
        const { data, error } = await supabase.functions.invoke(
          "geocode-address",
          {
            body: { address },
          }
        );

        if (error) {
          console.error("Geocoding error:", error);
          return null;
        }

        return data;
      } catch (error) {
        console.error("Request failed:", error);
        return null;
      }
    };

    const getLocations = async () => {
      const geocodedLocations = await Promise.all(
        craftsmen.map(async (craftsman) => {
          const address = `${craftsman.street_address}, ${craftsman.postal_code}, Netherlands`;
          const location = await geocodeAddress(address);

          if (location) {
            return {
              id: craftsman.id,
              lat: location.lat,
              lng: location.lng,
              name: craftsman.full_name,
              services: craftsman.services || [],
              balance: craftsman.balance || 0, // Add balance to location
            };
          }
          return null;
        })
      );

      setLocations(
        geocodedLocations.filter((loc): loc is Location => loc !== null)
      );
    };

    if (craftsmen.length > 0) {
      getLocations();
    } else {
      setLocations([]); // Reset locations if craftsmen is empty
    }
  }, [craftsmen]);

  // Add helper function to get marker color
  const getMarkerColor = (services: string[]) => {
    if (!services.length) return categoryColors["Schoonmaak & specials"]; // default color

    // Find the first service that matches a category
    for (const service of services) {
      const category = Object.keys(categoryColors).find((cat) =>
        serviceCategories
          .find((sc) => sc.name === cat)
          ?.services.includes(service)
      );
      if (category) return categoryColors[category];
    }

    return categoryColors["Schoonmaak & specials"]; // fallback color
  };

  return (
    <APIProvider apiKey={apiKey}>
      <div style={{ width: "100%", height: "600px" }}>
        <Map
          defaultZoom={7}
          defaultCenter={center}
          mapId="DEMO_MAP_ID"
          onClick={() => setSelectedTooltip(null)}
          // gestureHandling="greedy"
        >
          {locations.map((location) => {
            const craftsman = craftsmen.find((c) => c.id === location.id);
            return (
              <AdvancedMarker
                key={location.id}
                position={{ lat: location.lat, lng: location.lng }}
                title={location.name}
                zIndex={selectedTooltip === location.id ? 1 : 0}
              >
                <TooltipProvider>
                  <Tooltip open={selectedTooltip === location.id}>
                    <TooltipTrigger
                      asChild
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div
                        className={`h-5 w-5 cursor-pointer shadow-md transition-transform hover:scale-110 ${
                          location.balance ? "rounded" : "rounded-full"
                        }`}
                        style={{
                          backgroundColor: getMarkerColor(location.services),
                          border: "2px solid white",
                          boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
                        }}
                        onClick={() =>
                          setSelectedTooltip(
                            selectedTooltip === location.id ? null : location.id
                          )
                        }
                      />
                    </TooltipTrigger>
                    <TooltipContent
                      side="top"
                      className="bg-white p-4 rounded-lg shadow-lg border border-gray-200 max-w-[350px] w-full"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <TooltipArrow className="fill-white w-5 h-3" />
                      {/* Tooltip Content */}
                      <button
                        className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedTooltip(null);
                        }}
                      >
                        <X className="h-4 w-4 text-gray-500" />
                      </button>

                      <div className="space-y-4">
                        {/* Header Section */}
                        <div className="flex items-start gap-3">
                          {craftsman?.profile_photo_url ? (
                            <img
                              src={craftsman.profile_photo_url}
                              alt={location.name}
                              className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                            />
                          ) : (
                            <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                              <Building2 className="w-6 h-6 text-gray-400" />
                            </div>
                          )}
                          <div>
                            <h3 className="font-semibold text-lg text-gray-900">
                              {location.name}
                            </h3>
                            <div className="flex items-center text-sm text-gray-600">
                              <MapPin className="w-4 h-4 mr-1" />
                              {craftsman?.street_address},{" "}
                              {craftsman?.postal_code}
                            </div>
                          </div>
                        </div>

                        {/* Contact Section */}
                        <div className="space-y-2">
                          {craftsman?.phone_number && (
                            <div className="flex items-center text-sm">
                              <Phone className="w-4 h-4 mr-2 text-gray-500" />
                              <a
                                href={`tel:${craftsman.phone_number}`}
                                className="text-blue-600 hover:underline"
                              >
                                {craftsman.phone_number}
                              </a>
                            </div>
                          )}
                          {craftsman?.email && (
                            <div className="flex items-center text-sm">
                              <Mail className="w-4 h-4 mr-2 text-gray-500" />
                              <a
                                href={`mailto:${craftsman.email}`}
                                className="text-blue-600 hover:underline"
                              >
                                {craftsman.email}
                              </a>
                            </div>
                          )}
                        </div>

                        {/* Services Section */}
                        {location.services.length > 0 && (
                          <div className="space-y-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenServices(
                                  openServices === location.id
                                    ? null
                                    : location.id
                                );
                              }}
                              className="w-full flex items-center justify-between py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                            >
                              <span className="flex items-center">
                                <span>Services</span>
                                <span className="ml-2 text-xs text-gray-500">
                                  ({location.services.length})
                                </span>
                              </span>
                              <ChevronDown
                                className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${
                                  openServices === location.id
                                    ? "transform rotate-180"
                                    : ""
                                }`}
                              />
                            </button>
                            <div
                              className={`grid transition-all duration-200 ease-in-out ${
                                openServices === location.id
                                  ? "grid-rows-[1fr] opacity-100"
                                  : "grid-rows-[0fr] opacity-0"
                              }`}
                            >
                              <div className="overflow-hidden">
                                <div className="flex flex-wrap gap-1.5 pb-2">
                                  {location.services.map((service, idx) => (
                                    <span
                                      key={idx}
                                      className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200"
                                    >
                                      {service}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Business Info Section */}
                        <div className="flex items-center justify-between text-sm pt-2 border-t border-gray-100">
                          {craftsman?.kvk_number && (
                            <div className="text-gray-600">
                              <span className="font-medium">KVK:</span>{" "}
                              {craftsman.kvk_number}
                            </div>
                          )}
                          {location.balance > 0 && (
                            <div className="text-gray-600">
                              <span className="font-medium">Balance:</span>{" "}
                              <span className="text-emerald-600 font-medium">
                                €{location.balance.toFixed(2)}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </AdvancedMarker>
            );
          })}
        </Map>
      </div>
    </APIProvider>
  );
};

export default CraftsmenMap;
