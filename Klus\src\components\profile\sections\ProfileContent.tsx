import { EditProfileForm } from "@/components/EditProfileForm";
import { ProfileDisplay } from "../ProfileDisplay";
import { LogoutButton } from "./LogoutButton";
import ProfileServices from "../ProfileServices";
import { useAuth } from "@/components/auth/hooks/useAuth";
import ProfileDiploma from "../ProfileDiploma";
import MFAToggle from "@/components/security/MFAToggle";

interface ProfileContentProps {
  profile: any;
  isEditing: boolean;
  onEditSuccess: () => void;
  onEditCancel: () => void;
  onLogout: () => void;
  onEdit?: () => void;
}

export const ProfileContent = ({
  profile,
  isEditing,
  onEditSuccess,
  onEditCancel,
  onLogout,
  onEdit,
}: ProfileContentProps) => {
  const { userProfile } = useAuth();

  const isCraftman = userProfile?.user_type === "vakman";

  return (
    <>
      {isEditing ? (
        <EditProfileForm
          profile={profile}
          onSuccess={onEditSuccess}
          onCancel={onEditCancel}
        />
      ) : (
        <>
          <ProfileDisplay profile={profile} onEdit={onEdit} />
          {isCraftman && (
            <>
              <ProfileServices />
              <ProfileDiploma />
            </>
          )}
          <MFAToggle />
          <LogoutButton onLogout={onLogout} />
        </>
      )}
    </>
  );
};
