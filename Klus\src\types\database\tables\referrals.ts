
export type Referral = {
  Row: {
    id: string;
    referrer_id: string;
    referred_email: string;
    referred_user_id: string | null;
    created_at: string;
    completed_at: string | null;
    status: string;
    bonus_paid: boolean;
    referred_user?: {
      id: string;
      job_responses?: {
        id: string;
        status: string;
      }[];
    } | null;
  };
  Insert: {
    id?: string;
    referrer_id: string;
    referred_email: string;
    referred_user_id?: string | null;
    created_at?: string;
    completed_at?: string | null;
    status?: string;
    bonus_paid?: boolean;
  };
  Update: {
    id?: string;
    referrer_id?: string;
    referred_email?: string;
    referred_user_id?: string | null;
    created_at?: string;
    completed_at?: string | null;
    status?: string;
    bonus_paid?: boolean;
  };
};
