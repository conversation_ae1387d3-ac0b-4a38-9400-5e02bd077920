import { Users } from "lucide-react";

import { Badge } from "@/components/ui/badge";

interface JobCardHeaderProps {
  responseCount: number;
  status: string;
  getStatusColor: (status: string) => string;
  getStatusText: (status: string) => string;
}

export const JobCardHeader = ({
  responseCount,
  status,
  getStatusColor,
  getStatusText,
}: JobCardHeaderProps) => {
  return (
    <div className="flex flex-wrap items-start justify-end gap-2 w-full max-w-[calc(100%-2rem)]">
      {status === "open" && (
        <Badge className="flex items-center gap-1.5 text-white px-3 py-1 whitespace-nowrap rounded shadow-sm">
          <Users className="w-3 h-3 flex-shrink-0" />
          <span className="truncate">
            {responseCount} reactie{responseCount !== 1 ? "s" : ""}
          </span>
        </Badge>
      )}
      <Badge
        className={`text-white px-3 py-1 whitespace-nowrap rounded shadow-sm ${getStatusColor(
          status
        )}`}
      >
        {getStatusText(status)}
      </Badge>
    </div>
  );
};
