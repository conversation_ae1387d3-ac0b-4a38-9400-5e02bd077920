import { useState, useEffect, useRef } from "react";
import { useSet<PERSON>tom } from "jotai";
import { Loader2 } from "lucide-react";

import { JobDetailContainer } from "./job/JobDetailContainer";
import { JobListHeader } from "./jobs/JobListHeader";
import { JobListContent } from "./jobs/JobListContent";
import { useJobs } from "@/hooks/useJobs";
import { Button } from "./ui/button";
import { JobListLoader } from "./jobs/JobListLoader";
import { GradientLayout } from "./layout/GradientLayout";
import { DashboardAlert } from "./dashboard/DashboardAlert";
import { useAuth } from "./auth/hooks/useAuth";
import { currentJobInfoAtom } from "@/states/job";
import { SearchBar } from "./SearchBar";

export const JobList = () => {
  const { userProfile } = useAuth();
  const setCurrentJobInfo = useSetAtom(currentJobInfoAtom);

  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [allJobs, setAllJobs] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");

  const scrollPositionRef = useRef(0);

  const JOBS_PER_PAGE = 10;

  useEffect(() => {
    scrollPositionRef.current = window.scrollY;

    return () => {
      scrollPositionRef.current = window.scrollY;
    };
  }, []);

  // Restore scroll position when component mounts
  useEffect(() => {
    if (scrollPositionRef.current > 0) {
      window.scrollTo(0, scrollPositionRef.current);
    }
  }, []);

  const { jobs, loading } = useJobs(
    userProfile.user_type,
    userProfile.id,
    userProfile.services,
    page,
    JOBS_PER_PAGE
  );

  useEffect(() => {
    if (jobs.length > 0) {
      // Append new jobs to existing jobs
      setAllJobs((prevJobs) => {
        const newJobs = jobs.filter(
          (newJob) =>
            !prevJobs.some((existingJob) => existingJob.id === newJob.id)
        );
        return [...prevJobs, ...newJobs];
      });
    }
    // Check if we have less jobs than the page size
    if (jobs.length < JOBS_PER_PAGE) {
      setHasMore(false);
    } else {
      setHasMore(true);
    }
  }, [jobs]);

  useEffect(() => {
    setCurrentJobInfo(selectedJob);
  }, [selectedJob]);

  // Add this handler function
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleLoadMore = () => {
    // Save current scroll position before loading more
    scrollPositionRef.current = window.scrollY;
    setPage((prevPage) => prevPage + 1);
  };

  const handleJobClick = (job: any) => {
    // Save scroll position before showing job detail
    scrollPositionRef.current = window.scrollY;
    setSelectedJob(job);
  };

  const handleBack = () => {
    setSelectedJob(null);
    // Restore scroll position when returning to list
    setTimeout(() => {
      window.scrollTo(0, scrollPositionRef.current);
    }, 0);
  };

  if (loading && page === 1) {
    return <JobListLoader />;
  }

  if (selectedJob) {
    return (
      <JobDetailContainer
        id={selectedJob.id}
        title={selectedJob.title}
        description={selectedJob.description}
        location={selectedJob.postal_code}
        date={selectedJob.created_at}
        status={selectedJob.status}
        house_number={selectedJob.house_number}
        house_number_addition={selectedJob.house_number_addition}
        photos={selectedJob.photos}
        onBack={handleBack}
        isOwner={selectedJob.user_id === userProfile.id}
        isDirect={selectedJob.direct_request?.includes(userProfile.id)}
        hired_craftman_id={selectedJob.hired_craftman_id}
      />
    );
  }

  return (
    <GradientLayout>
      {userProfile.user_type === "vakman" && (
        <DashboardAlert profile={userProfile} />
      )}
      <JobListHeader userType={userProfile.user_type} />
      {jobs.length > 0 && <SearchBar onSearch={handleSearch} />}
      <JobListContent
        jobs={userProfile.user_type === "vakman" ? allJobs : jobs}
        userType={userProfile.user_type}
        onJobClick={handleJobClick}
        isLoading={loading}
        searchQuery={searchQuery}
      />

      {loading && page > 1 && (
        <div className="flex justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
        </div>
      )}

      {hasMore && userProfile.user_type === "vakman" && !loading && (
        <div className="flex justify-center pt-6">
          <Button
            onClick={handleLoadMore}
            variant="outline"
            className="min-w-[200px] bg-white hover:bg-gray-50 border-gray-200 text-gray-700 hover:text-gray-900 transition-all duration-300 shadow-sm hover:shadow-md"
          >
            Toon meer klussen
          </Button>
        </div>
      )}
    </GradientLayout>
  );
};
