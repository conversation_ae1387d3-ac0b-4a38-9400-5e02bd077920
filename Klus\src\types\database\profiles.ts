import { Json } from './auth';

export interface ProfilesSchema {
  Tables: {
    profiles: {
      Row: {
        id: string;
        email: string | null;
        full_name: string | null;
        user_type: string | null;
        created_at: string;
        updated_at: string;
        balance: number | null;
        first_name: string | null;
        last_name: string | null;
        street_address: string | null;
        house_number: string | null;
        house_number_addition: string | null;
        phone_number: string | null;
        kvk_number: string | null;
        btw_number: string | null;
        profile_photo_url: string | null;
      };
      Insert: {
        id: string;
        email?: string | null;
        full_name?: string | null;
        user_type?: string | null;
        created_at?: string;
        updated_at?: string;
        balance?: number | null;
        first_name?: string | null;
        last_name?: string | null;
        street_address?: string | null;
        house_number?: string | null;
        house_number_addition?: string | null;
        phone_number?: string | null;
        kvk_number?: string | null;
        btw_number?: string | null;
        profile_photo_url?: string | null;
      };
      Update: {
        id?: string;
        email?: string | null;
        full_name?: string | null;
        user_type?: string | null;
        created_at?: string;
        updated_at?: string;
        balance?: number | null;
        first_name?: string | null;
        last_name?: string | null;
        street_address?: string | null;
        house_number?: string | null;
        house_number_addition?: string | null;
        phone_number?: string | null;
        kvk_number?: string | null;
        btw_number?: string | null;
        profile_photo_url?: string | null;
      };
    };
  };
}