import { useState, useEffect } from "react";
import { Plus, Loader2, LayoutGrid, List, Settings } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { AdminPhotoUpload } from "@/components/admin/template/AdminPhotoUpload";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import ConfirmModal from "@/components/modal/ConfirmModal";
import { cn } from "@/lib/utils";
import { EmptyState } from "@/components/admin/template/EmptyState";
import { TemplateCard } from "@/components/admin/template/TemplateCard";
import { TemplateSkeleton } from "@/components/admin/template/TemplateSkeleton";
import { BotConfigModal } from "@/components/modal/BotConfigModal";
import { TemplateServices } from "./TemplateServices";

export interface JobTemplate {
  id: number;
  title: string;
  description: string;
  photos: string[];
  services: string[];
}

export default function JobTemplatePage() {
  const [templates, setTemplates] = useState<JobTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingTemplate, setEditingTemplate] =
    useState<Partial<JobTemplate> | null>(null);
  const [showDialog, setShowDialog] = useState(false);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isBotRunning, setIsBotRunning] = useState(false);
  const [toggleLoading, setToggleLoading] = useState(false);
  const [isBotConfigOpen, setIsBotConfigOpen] = useState(false);
  const [selectedTimes, setSelectedTimes] = useState<string[]>([]);

  useEffect(() => {
    const initializeData = async () => {
      try {
        await Promise.all([fetchTemplates(), getBotRunningStatus()]);
      } catch (error) {
        console.error("Failed to initialize data:", error);
      }
    };

    initializeData();
  }, []);

  const fetchTemplates = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase.from("job_schedules").select("*");

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Fout bij ophalen sjablonen",
        description:
          "De sjablonen konden niet worden opgehaald. Probeer het opnieuw.",
      });
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const getBotRunningStatus = async () => {
    setToggleLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke(
        "get-cron-job-schedule",
        {
          body: {
            job_name: "run-scheduled-job",
          },
        }
      );
      if (error) throw error;

      if (data) {
        setIsBotRunning(!!data.data);

        // Parse cron expression and set selected times
        if (data.data) {
          const cronParts = data.data.split(" ");
          if (cronParts.length === 5) {
            const hours = cronParts[1].split(",");
            const formattedTimes = hours.map(
              (hour: string) => `${hour.padStart(2, "0")}:00`
            );
            setSelectedTimes(formattedTimes);
          }
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setToggleLoading(false);
    }
  };

  const handleSubmit = async (template: Partial<JobTemplate>) => {
    try {
      setIsSubmitting(true);

      if (template.id) {
        const { error, data } = await supabase
          .from("job_schedules")
          .update(template)
          .eq("id", template.id)
          .select()
          .single();

        if (error) throw error;

        // Update existing template in state
        setTemplates((prevTemplates) =>
          prevTemplates.map((t) => (t.id === template.id ? data : t))
        );

        toast({
          title: "Sjabloon bijgewerkt",
          description: "Het sjabloon is succesvol bijgewerkt",
        });
      } else {
        const { error, data } = await supabase
          .from("job_schedules")
          .insert([template])
          .select()
          .single();

        if (error) throw error;

        // Add new template to state
        setTemplates((prevTemplates) => [...prevTemplates, data]);

        toast({
          title: "Sjabloon aangemaakt",
          description: "Het nieuwe sjabloon is succesvol aangemaakt",
        });
      }

      setShowDialog(false);
      setEditingTemplate(null);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Fout bij opslaan",
        description: template.id
          ? "Het sjabloon kon niet worden bijgewerkt"
          : "Het sjabloon kon niet worden aangemaakt",
      });
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleConfirmDelete = async () => {
    if (!deleteId) return;

    setDeleteLoading(true);
    try {
      const { error } = await supabase
        .from("job_schedules")
        .delete()
        .eq("id", deleteId);

      if (error) throw error;

      setTemplates((prevTemplates) =>
        prevTemplates.filter((template) => template.id !== deleteId)
      );
      toast({
        title: "Sjabloon verwijderd",
        description: "Het sjabloon is succesvol verwijderd",
      });

      setIsConfirmOpen(false);
      setDeleteId(null);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Fout bij verwijderen",
        description: "Het sjabloon kon niet worden verwijderd",
      });
      console.error(error);
    } finally {
      setDeleteLoading(false);
    }
  };

  const toggleBot = async (newState: boolean) => {
    setToggleLoading(true);

    try {
      // Optimistic update
      setIsBotRunning(newState);

      const { backendApi } = await import("@/lib/backendApi");

      if (newState) {
        await backendApi.scheduleCronJob({
          job_name: "run-scheduled-job",
          cron_expression: "0 0 * * *",
          command: "SELECT create_random_scheduled_job();",
        });
      } else {
        await backendApi.unscheduleCronJob("run-scheduled-job");
      }

      toast({
        title: `Bot ${newState ? "geactiveerd" : "gedeactiveerd"}`,
        description: newState
          ? "De bot is nu actief en zal taken automatisch uitvoeren"
          : "De bot is gedeactiveerd en zal geen taken meer uitvoeren",
      });
    } catch (error) {
      // Revert optimistic update on error
      setIsBotRunning(!newState);

      toast({
        variant: "destructive",
        title: "Fout bij het schakelen van de bot",
        description: "Er is iets misgegaan. Probeer het opnieuw.",
      });
      console.error("Bot toggle error:", error);
    } finally {
      setToggleLoading(false);
    }
  };

  return (
    <div className="overflow-y-auto h-[calc(100vh-85px)]">
      <div className="max-w-7xl mx-auto py-6 sm:px-2 px-7">
        <div className="flex flex-col gap-6">
          {/* Header Section */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">Werksjablonen</h1>
              <p className="text-muted-foreground mt-1">
                Beheer je werksjablonen voor snelle taakplanning
              </p>
            </div>

            <div className="flex sm:flex-row flex-col sm:items-center gap-4 w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={() => setIsBotConfigOpen(true)}
                className="w-full sm:w-auto"
              >
                <Settings className="h-4 w-4 mr-2" />
                Bot Configuratie
              </Button>

              <div className="flex gap-4">
                <div className="flex items-center bg-muted rounded-lg p-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "px-2",
                      viewMode === "grid" && "bg-background"
                    )}
                    onClick={() => setViewMode("grid")}
                    aria-label="Grid weergave"
                  >
                    <LayoutGrid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "px-2",
                      viewMode === "list" && "bg-background"
                    )}
                    onClick={() => setViewMode("list")}
                    aria-label="Lijst weergave"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
                <Dialog open={showDialog} onOpenChange={setShowDialog}>
                  <DialogTrigger asChild>
                    <Button onClick={() => setEditingTemplate(null)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Nieuw sjabloon
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[725px] max-w-[calc(100vw-20px)]">
                    <DialogHeader>
                      <DialogTitle>
                        {editingTemplate?.id
                          ? "Sjabloon bewerken"
                          : "Sjabloon maken"}
                      </DialogTitle>
                    </DialogHeader>
                    <TemplateForm
                      template={editingTemplate}
                      onSubmit={handleSubmit}
                      isSubmitting={isSubmitting}
                      onClose={() => setShowDialog(false)}
                    />
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </div>

          {/* Content Section */}
          {isLoading ? (
            <TemplateSkeleton viewMode={viewMode} />
          ) : templates.length === 0 ? (
            <EmptyState
              onCreateClick={() => {
                setShowDialog(true);
              }}
            />
          ) : (
            <div
              className={cn(
                viewMode === "grid"
                  ? "grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                  : "flex flex-col gap-4"
              )}
            >
              {templates.map((template) => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  viewMode={viewMode}
                  onEdit={() => {
                    setEditingTemplate(template);
                    setShowDialog(true);
                  }}
                  onDelete={() => {
                    setDeleteId(template.id);
                    setIsConfirmOpen(true);
                  }}
                />
              ))}
            </div>
          )}
        </div>

        <ConfirmModal
          isOpen={isConfirmOpen}
          setIsOpen={setIsConfirmOpen}
          confirmHandler={handleConfirmDelete}
          loading={deleteLoading}
          title="Sjabloon verwijderen"
          content="Weet je zeker dat je dit sjabloon wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt."
          variant="danger"
          yesText="Verwijderen"
        />
      </div>
      <BotConfigModal
        isOpen={isBotConfigOpen}
        onClose={() => setIsBotConfigOpen(false)}
        isRunning={isBotRunning}
        onToggle={toggleBot}
        selectedTimes={selectedTimes}
        onTimesChange={setSelectedTimes}
        isLoading={toggleLoading}
      />
    </div>
  );
}

function TemplateForm({
  template,
  onSubmit,
  isSubmitting,
  onClose,
}: {
  template: Partial<JobTemplate> | null;
  onSubmit: (template: Partial<JobTemplate>) => Promise<void>;
  isSubmitting: boolean;
  onClose: () => void;
}) {
  const [formData, setFormData] = useState(
    template || {
      title: "",
      description: "",
      photos: [],
      services: [],
    }
  );
  const [isUploading, setIsUploading] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsUploading(true);

    try {
      // Process each photo - upload new ones (base64) and keep existing URLs
      const processedPhotos = await Promise.all(
        formData.photos.map(async (photo: string) => {
          // If the photo is already a URL, keep it as is
          if (photo.startsWith("http")) {
            return photo;
          }

          // Otherwise, it's a new base64 image that needs to be uploaded
          const fetchResponse = await fetch(photo);
          const blob = await fetchResponse.blob();

          const fileExt = "jpg";
          const fileName = `${Math.random()}.${fileExt}`;
          const file = new File([blob], fileName, { type: "image/jpeg" });

          const { error } = await supabase.storage
            .from("job-photos")
            .upload(fileName, file);

          if (error) throw error;

          const {
            data: { publicUrl },
          } = supabase.storage.from("job-photos").getPublicUrl(fileName);

          return publicUrl;
        })
      );

      // Submit the form with processed photos
      await onSubmit({
        ...formData,
        photos: processedPhotos,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Fout bij uploaden",
        description: "Er ging iets mis bij het uploaden van de foto's",
      });
      console.error(error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-1 block">Titel</label>
          <Input
            required
            value={formData.title}
            onChange={(e) =>
              setFormData({ ...formData, title: e.target.value })
            }
          />
        </div>
        <div>
          <label className="text-sm font-medium mb-1 block">Beschrijving</label>
          <Textarea
            required
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            rows={8}
          />
        </div>
        <div>
          <label className="text-sm font-medium mb-1 block">Foto's</label>
          <AdminPhotoUpload
            photos={formData.photos || []}
            onPhotosChange={(photos) => setFormData({ ...formData, photos })}
          />
        </div>
        <div>
          <label className="text-sm font-medium mb-1 block">Diensten</label>
          <TemplateServices
            selectedServices={formData.services || []}
            onServicesChange={(services) =>
              setFormData({ ...formData, services })
            }
          />
        </div>
      </div>
      <div className="flex justify-end gap-3">
        <Button type="button" variant="outline" onClick={onClose}>
          Annuleren
        </Button>
        <Button type="submit" disabled={isSubmitting || isUploading}>
          {(isSubmitting || isUploading) && (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          )}
          {template?.id ? "Wijzigingen opslaan" : "Sjabloon maken"}
        </Button>
      </div>
    </form>
  );
}
