{"name": "klus-backend-server", "version": "1.0.0", "description": "Express.js backend server for Klus application", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["express", "typescript", "klus", "backend"], "author": "", "license": "ISC", "dependencies": {"@supabase/supabase-js": "^2.39.3", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "puppeteer": "^21.6.1", "standardwebhooks": "^1.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/multer": "^1.4.11", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "@types/jest": "^29.5.11", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}