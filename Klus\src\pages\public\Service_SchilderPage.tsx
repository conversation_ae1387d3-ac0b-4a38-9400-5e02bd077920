/**
 * @description This component renders a comprehensive and SEO-optimized detail page for painter services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for painters. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Paintbrush,
  ShieldCheck,
  Clock,
  ArrowRight,
  Palette,
  Star,
  Edit3,
  MessageSquare,
  <PERSON>r<PERSON><PERSON><PERSON>,
  CheckCircle,
  MapPin,
  Euro,
  Layers,
  PaintRoller,
  Home,
} from "lucide-react";

const Service_SchilderPage = () => {
  usePageTitle("Schilder Nodig? | Klusgebied - Binnen & Buiten Schilderwerk");
  const navigate = useNavigate();

  const painterServices = [
    {
      icon: Paintbrush,
      title: "Binnen Schilderwerk",
      description: "Muren, plafonds en houtwerk vakkundig schilderen.",
      points: [
        "Strak en dekkend schilderwerk voor muren en plafonds.",
        "Professioneel lakken van deuren, kozijnen en trappen.",
        "Advies over kleur en verfsoorten.",
        "Minimale overlast en een schone oplevering.",
      ],
    },
    {
      icon: Home,
      title: "Buiten Schilderwerk",
      description:
        "Gevels, kozijnen en dakgoten beschermen tegen weer en wind.",
      points: [
        "Duurzame bescherming van uw houtwerk.",
        "Inspectie en reparatie van houtrot.",
        "Gebruik van hoogwaardige, weerbestendige verf.",
        "Verhoogt de waarde en uitstraling van uw woning.",
      ],
    },
    {
      icon: Layers,
      title: "Behang Aanbrengen",
      description: "Professioneel behangen van wanden en plafonds.",
      points: [
        "Ervaring met alle soorten behang (vlies, papier, foto).",
        "Perfect gladde ondergrond voorbereiding.",
        "Naadloze en strakke afwerking.",
        "Ook voor het verwijderen van oud behang.",
      ],
    },
    {
      icon: PaintRoller,
      title: "Houtwerk Lakken",
      description:
        "Bescherming en styling van deuren, kozijnen en lambrisering.",
      points: [
        "Slijtvaste afwerking voor intensief gebruikte onderdelen.",
        "Grondig schuren en gronden voor optimale hechting.",
        "Keuze uit diverse glansgraden (mat, zijdeglans, hoogglans).",
        "Geeft uw interieur een frisse, nieuwe look.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Kwaliteitsverf",
      description:
        "Wij gebruiken alleen de beste verven voor duurzame resultaten.",
    },
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "Snelle Service",
      description: "Van offerte tot oplevering binnen afgesproken termijn.",
    },
    {
      icon: <Palette className="w-8 h-8 text-white" />,
      title: "Kleuradvies",
      description: "Gratis advies over kleurkeuze en afwerking.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een schilder per uur?",
      answer:
        "Het uurtarief van een schilder ligt gemiddeld tussen de €35 en €50. Veel schilders werken echter met een vaste prijs per m² of per project.",
    },
    {
      question: "Wat kost het schilderen van een kamer?",
      answer:
        "De kosten voor een gemiddelde woonkamer (ca. 40m² muren) liggen tussen de €600 en €1200, afhankelijk van de staat van de ondergrond en het aantal lagen.",
    },
    {
      question: "Hoe lang duurt het schilderen van een huis van buiten?",
      answer:
        "Voor een gemiddelde eengezinswoning moet u rekenen op 1 tot 2 weken werk, afhankelijk van de weersomstandigheden en de staat van het houtwerk.",
    },
    {
      question: "Welke verf is het beste voor buiten?",
      answer:
        "Voor buitenschilderwerk gebruiken we hoogwaardige, weerbestendige verf op acryl- of alkydbasis die uw houtwerk jarenlang beschermt tegen zon en regen.",
    },
    {
      question: "Moet ik zelf de verf kopen?",
      answer:
        "Nee, dat is niet nodig. Onze schilders werken met professionele merken en kunnen de verf voor u inkopen, vaak met korting. U kunt uiteraard uw kleurvoorkeuren aangeven.",
    },
    {
      question: "Hoe bereid ik me voor op de komst van de schilder?",
      answer:
        "Het is handig als u kleine meubels en waardevolle spullen alvast uit de te schilderen ruimte verwijdert. Grote meubels kunnen wij voor u afdekken.",
    },
  ];

  const reviews = [
    {
      name: "Linda de Vries",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Fantastisch schilderwerk! Onze woonkamer ziet er weer als nieuw uit. De schilder was professioneel, vriendelijk en werkte super netjes.",
      highlighted: true,
    },
    {
      name: "Kees Bakker",
      location: "Haarlem",
      rating: 5,
      quote:
        "Het buitenschilderwerk is perfect uitgevoerd. Duidelijke afspraken en een prachtig resultaat. Ons huis kan er weer jaren tegenaan!",
      highlighted: false,
    },
    {
      name: "Familie Nguyen",
      location: "Den Haag",
      rating: 5,
      quote:
        "Zeer tevreden over het behangwerk in de kinderkamer. De schilder dacht goed mee en het patroon loopt perfect door. Een echte vakman.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1688372199140-cade7ae820fe?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=600",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1688372199140-cade7ae820fe?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxwYWludGVyJTJDJTIwcGFpbnRpbmclMjBzZXJ2aWNlJTJDJTIwcHJvZmVzc2lvbmFsJTIwd29ya3xlbnwwfHx8fDE3NTE3NDAzMDB8MA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description: "Beschrijf je schilderklus. Geef afmetingen en wensen door.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Vakmannen reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde schilders.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & start de klus",
      description:
        "Vergelijk profielen en kies de beste schilder voor jouw klus. Plan en start!",
      microcopy: "Vergelijk profielen en beoordelingen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = React.useState(0);

  React.useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Schilders in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "schilder",
    color: "purple",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Schilder Nodig? | Klusgebied - Binnen & Buiten Schilderwerk
        </title>
        <meta
          name="description"
          content="Vind snel een betrouwbare schilder voor binnen- en buitenschilderwerk, behangen en meer. Plaats je klus gratis en ontvang offertes van geverifieerde vakmannen in jouw regio."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Schilder",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.9",
              reviewCount: "412",
            },
            review: reviews.map((review) => ({
              "@type": "Review",
              author: { "@type": "Person", name: review.name },
              reviewRating: {
                "@type": "Rating",
                ratingValue: review.rating,
                bestRating: "5",
              },
              reviewBody: review.quote,
            })),
            offers: {
              "@type": "Offer",
              priceCurrency: "EUR",
              priceSpecification: {
                "@type": "PriceSpecification",
                priceCurrency: "EUR",
                minPrice: "35",
                maxPrice: "50",
                valueAddedTaxIncluded: true,
                unitText: "HOUR",
                description:
                  "Standaardtarief per uur. Prijs per m² is ook gebruikelijk.",
              },
            },
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            mainEntity: faqs.map((faq) => ({
              "@type": "Question",
              name: faq.question,
              acceptedAnswer: {
                "@type": "Answer",
                text: faq.answer,
              },
            })),
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-purple-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-purple-100 border border-purple-200/80 rounded-full px-4 py-2 mb-6">
                    <Paintbrush className="w-5 h-5 text-purple-600" />
                    <span className="text-purple-800 font-semibold text-sm">
                      Schilder Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Schilder nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-indigo-500 mt-2">
                      Strak & Snel
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor al uw binnen- en buitenschilderwerk. Vind snel een
                    geverifieerde schilder bij u in de buurt.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate("/plaats-een-klus/stucwerk-binnen")
                      }
                      className="group inline-flex items-center justify-center bg-purple-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-purple-600 transition-all duration-300 shadow-lg hover:shadow-purple-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw schilder
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.9/5</span>
                    <span>gebaseerd op 412 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1688372199140-cade7ae820fe?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Professionele schilder aan het werk"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte schilder voor jouw klus.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-purple-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-purple-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Schilder Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Van een enkele muur tot een complete woning. Lees meer over onze
                expertise.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {painterServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-purple-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-purple-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een schilder via Klusgebied?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                Bij Klusgebied betaal je een eerlijke prijs voor erkende
                vakmannen. Je ontvangt altijd eerst een prijsvoorstel voordat de
                klus start.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-purple-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Uurtarief:{" "}
                    <strong className="text-slate-900">€35–€50</strong> (incl.
                    BTW)
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-purple-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Prijs per m²:{" "}
                    <strong className="text-slate-900">€8–€20</strong>,
                    afhankelijk van de klus
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-purple-500 mr-3 mt-1 flex-shrink-0" />
                  <span>Vaste prijs: Mogelijk voor grotere projecten</span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Geen verborgen kosten. Altijd vooraf een prijsafspraak.
              </div>
              <button
                onClick={() => navigate("/plaats-een-klus/stucwerk-binnen")}
                className="bg-purple-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-purple-600 transition-all duration-300 shadow-lg hover:shadow-purple-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een schilder vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-purple-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-purple-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-purple-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-purple-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center mt-12">
              <a
                href="#"
                className="text-purple-600 font-semibold hover:underline"
              >
                Bekijk alle beoordelingen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                Waarom kiezen voor Klusgebied?
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Ervaren schilders die staan voor kwaliteit en een perfect
                eindresultaat.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-purple-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/over-ons")}
                className="text-purple-300 font-semibold hover:text-white transition-colors"
              >
                Bekijk waarom vakmannen en klanten voor ons kiezen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </button>
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-purple-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-purple-500 to-indigo-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar voor een frisse nieuwe look?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Plaats uw klus en ontvang snel offertes van de beste schilders bij
              u in de buurt.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/stucwerk-binnen")}
              className="bg-white text-purple-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je schilderklus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/stucwerk-binnen")}
          className="w-full group inline-flex items-center justify-center bg-purple-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-purple-600 transition-all duration-300 shadow-lg hover:shadow-purple-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_SchilderPage;
