/**
 * @description This component renders a dedicated landing page for professionals (vakmannen) to join the Klusgebied platform. It provides compelling reasons to sign up, showcases benefits like direct job access and secure payments, and features testimonials from other professionals. The page is designed with a world-class, conversion-focused layout, including prominent calls-to-action for downloading the app, and is fully responsive. Key variables include feature lists, testimonials, and navigation handlers to guide professionals through the sign-up journey.
 */
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
  Briefcase,
  MessageSquare,
  Euro,
  Star,
  CheckCircle,
  Download,
  TrendingUp,
  Users,
  ShieldCheck,
  FileSignature,
  Bell,
} from "lucide-react";

const VakmanLandingPage = () => {
  const navigate = useNavigate();
  usePageTitle("Voor Vakmannen | Sluit je aan bij Klusgebied");
  const [email, setEmail] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    // For now, we can log the email and navigate to the login page as a placeholder action.
    // In a real app, this would trigger the OTP flow.
    console.log("Registering with email:", email);
    navigate("/login");
  };

  const benefits = [
    {
      icon: Briefcase,
      title: "Meer Klussen, Minder Zorgen",
      description:
        "Krijg direct toegang tot duizenden klussen in jouw regio. Zeg vaarwel tegen acquisitie en focus op je vak.",
    },
    {
      icon: MessageSquare,
      title: "Direct Contact met Klanten",
      description:
        "Chat rechtstreeks met klanten, bespreek de details en stuur direct een duidelijke offerte via de app.",
    },
    {
      icon: Euro,
      title: "Gegarandeerde Betaling",
      description:
        "Geen gedoe meer met facturen. Klanten betalen veilig via ons platform, en jij wordt direct na de klus uitbetaald.",
    },
    {
      icon: Star,
      title: "Bouw aan je Reputatie",
      description:
        "Ontvang reviews van tevreden klanten en bouw een ijzersterk profiel op. Goed werk wordt beloond met meer klussen.",
    },
  ];

  const howItWorks = [
    {
      step: 1,
      title: "Ontvang Klusmeldingen",
      description:
        "Krijg real-time notificaties van nieuwe klussen die perfect passen bij jouw specialisme en werkgebied.",
      image:
        "https://images.unsplash.com/photo-1643968704781-df3b260df6a7?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjcmFmdHNtYW4lMkMlMjBwcm9mZXNzaW9uYWx8ZW58MHx8fHwxNzUxNTgxMDgyfDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      step: 2,
      title: "Reageer & Maak een Offerte",
      description:
        "Interesse in een klus? Reageer direct, chat met de klant en stuur een professionele offerte via de app.",
      image:
        "https://images.unsplash.com/photo-1661446600373-125cfeadf275?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwyfHxjcmFmdHNtYW4lMkMlMjBwcm9mZXNzaW9uYWx8ZW58MHx8fHwxNzUxNTgxMDgyfDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      step: 3,
      title: "Klaar de Klus & Krijg Betaald",
      description:
        "Voer de klus uit met vakmanschap. Zodra de klant tevreden is, wordt de betaling direct vrijgegeven.",
      image:
        "https://images.unsplash.com/photo-1726931535415-edbc43d42c28?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwzfHxjcmFmdHNtYW4lMkMlMjBwcm9mZXNzaW9uYWx8ZW58MHx8fHwxNzUxNTgxMDgyfDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const testimonials = [
    {
      name: "Marco V., Loodgieter",
      location: "Amsterdam",
      text: "Sinds ik bij Klusgebied zit, is mijn agenda altijd vol. De app is super makkelijk en ik hoef nooit meer achter mijn geld aan.",
      image:
        "https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.1.0&w=400&h=400",
    },
    {
      name: "Fatima A., Schilder",
      location: "Utrecht",
      text: "Ideaal platform. Ik kan zelf kiezen welke klussen ik aanneem en de communicatie met klanten is heel direct. Een aanrader!",
      image:
        "https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?ixlib=rb-4.1.0&w=400&h=400",
    },
    {
      name: "Jeroen de Boer, Timmerman",
      location: "Rotterdam",
      text: "De gegarandeerde betaling geeft zoveel rust. Ik focus me op mijn werk, Klusgebied regelt de rest. Top!",
      image:
        "https://images.unsplash.com/photo-1557862921-37829c790f19?ixlib=rb-4.1.0&w=400&h=400",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <section className="bg-white pt-24 pb-20 lg:pt-32 lg:pb-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-20 items-center">
            {/* Left Column */}
            <div className="text-left motion-preset-slide-right">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 leading-tight">
                De betrouwbare manier om{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-blue-500">
                  opdrachten
                </span>{" "}
                te vinden die bij je passen
              </h1>
              <p className="text-lg text-slate-600 max-w-xl mt-6">
                Verbind direct met gekwalificeerde vakmensen in jouw buurt.
                Plaats je klus en ontvang snel reacties van betrouwbare
                professionals.
              </p>
              <div className="mt-8 flex flex-col sm:flex-row items-center gap-4">
                <a
                  href="https://apps.apple.com/nl/app/klusgebied/id6747739000"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block hover:-translate-y-1 transition-transform duration-300"
                >
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/3/3c/Download_on_the_App_Store_Badge.svg"
                    alt="Download on the App Store"
                    className="h-14 w-auto"
                    loading="lazy"
                    width={144}
                    height={56}
                  />
                </a>
                <a
                  href="https://play.google.com/store/apps/details?id=nl.klusgebied.android"
                  className="inline-block hover:-translate-y-1 transition-transform duration-300"
                >
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/7/78/Google_Play_Store_badge_EN.svg"
                    alt="Get it on Google Play"
                    className="h-14 w-auto"
                    loading="lazy"
                    width={144}
                    height={56}
                  />
                </a>
              </div>
              <div className="flex items-center gap-4 mt-8">
                <div className="flex items-center">
                  <Star
                    className="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                  />
                  <Star
                    className="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                  />
                  <Star
                    className="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                  />
                  <Star
                    className="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                  />
                  <Star
                    className="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                  />
                </div>
                <p className="text-sm text-slate-600 font-medium">
                  <span className="font-bold text-slate-800">4.8</span> uit 5
                  beoordelingen
                </p>
              </div>
            </div>

            {/* Right Column */}
            <div className="motion-preset-slide-left flex justify-center items-center">
              <div className="bg-white p-8 sm:p-10 md:p-12 rounded-2xl shadow-2xl border border-slate-100 transform scale-100 max-w-md w-full">
                <h2 className="text-xl md:text-2xl font-bold text-slate-800 mb-8 text-center">
                  Registreer je als vakman
                </h2>
                <form onSubmit={handleSubmit}>
                  <div className="mb-4">
                    <label
                      htmlFor="email"
                      className="block text-xs font-medium text-slate-700 mb-2"
                    >
                      E-mailadres
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      required
                      className="w-full px-4 py-2.5 rounded-lg border border-slate-300 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition"
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-teal-500 text-white px-6 py-2.5 rounded-lg font-semibold hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-teal-500/20 hover:-translate-y-0.5"
                  >
                    Maak een account aan
                  </button>
                </form>
                <p className="text-center text-sm text-slate-600 mt-8">
                  Al een account?{" "}
                  <button
                    type="button"
                    onClick={() => navigate("/login")}
                    className="font-medium text-teal-600 hover:underline bg-transparent border-none p-0 cursor-pointer"
                  >
                    Log hier in
                  </button>
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Bar */}
      <section className="py-12 bg-slate-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className="p-4">
              <TrendingUp className="w-10 h-10 mx-auto text-teal-500 mb-2" />
              <div className="text-2xl md:text-3xl font-bold text-slate-800">
                1.500+
              </div>
              <div className="text-slate-600">Nieuwe klussen per week</div>
            </div>
            <div className="p-4">
              <Users className="w-10 h-10 mx-auto text-teal-500 mb-2" />
              <div className="text-2xl md:text-3xl font-bold text-slate-800">
                2.500+
              </div>
              <div className="text-slate-600">Actieve Vakmannen</div>
            </div>
            <div className="p-4">
              <ShieldCheck className="w-10 h-10 mx-auto text-teal-500 mb-2" />
              <div className="text-2xl md:text-3xl font-bold text-slate-800">
                98%
              </div>
              <div className="text-slate-600">
                Uitbetaalde klussen binnen 24u
              </div>
            </div>
            <div className="p-4">
              <Star className="w-10 h-10 mx-auto text-teal-500 mb-2" />
              <div className="text-2xl md:text-3xl font-bold text-slate-800">
                4.8/5
              </div>
              <div className="text-slate-600">Gemiddelde Vakman-rating</div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section (New) */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
              Zo vind je de juiste opdrachten
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
              Sluit je aan bij duizenden vakmensen en laat je bedrijf groeien
              met Klusgebied.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 lg:gap-12 text-center relative">
            {/* Step 1 */}
            <div className="motion-preset-fade-in-up">
              <div className="flex justify-center items-center mb-6">
                <div className="relative group">
                  <div className="w-40 h-40 bg-teal-50 rounded-full transition-transform duration-300 group-hover:scale-110"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <FileSignature className="w-16 h-16 text-teal-500 transition-transform duration-300 group-hover:scale-125" />
                  </div>
                </div>
              </div>
              <h3 className="text-sm font-bold text-slate-500 tracking-wider uppercase">
                STAP 1
              </h3>
              <p className="text-lg md:text-xl font-bold text-slate-800 mt-2">
                Meld je gratis aan
              </p>
              <p className="text-slate-600 mt-2">
                Maak je profiel compleet en laat zien wat je kunt.
              </p>
            </div>
            {/* Step 2 */}
            <div className="motion-preset-fade-in-up motion-delay-200">
              <div className="flex justify-center items-center mb-6">
                <div className="relative group">
                  <div className="w-40 h-40 bg-blue-50 rounded-full transition-transform duration-300 group-hover:scale-110"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Bell className="w-16 h-16 text-blue-500 transition-transform duration-300 group-hover:scale-125" />
                  </div>
                </div>
              </div>
              <h3 className="text-sm font-bold text-slate-500 tracking-wider uppercase">
                STAP 2
              </h3>
              <p className="text-lg md:text-xl font-bold text-slate-800 mt-2">
                Ontvang klusmeldingen
              </p>
              <p className="text-slate-600 mt-2">
                Krijg direct meldingen van klussen die bij jou passen.
              </p>
            </div>
            {/* Step 3 */}
            <div className="motion-preset-fade-in-up motion-delay-400">
              <div className="flex justify-center items-center mb-6">
                <div className="relative group">
                  <div className="w-40 h-40 bg-green-50 rounded-full transition-transform duration-300 group-hover:scale-110"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Euro className="w-16 h-16 text-green-500 transition-transform duration-300 group-hover:scale-125" />
                  </div>
                </div>
              </div>
              <h3 className="text-sm font-bold text-slate-500 tracking-wider uppercase">
                STAP 3
              </h3>
              <p className="text-lg md:text-xl font-bold text-slate-800 mt-2">
                Kies & verdien
              </p>
              <p className="text-slate-600 mt-2">
                Reageer op klussen, maak een offerte en ga aan de slag.
              </p>
            </div>
          </div>
          <div className="text-center mt-16">
            <button
              onClick={() =>
                document
                  .querySelector("header")
                  .scrollIntoView({ behavior: "smooth" })
              }
              className="bg-teal-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-teal-600 transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-teal-500/20 hover:-translate-y-0.5"
            >
              Start met aanmelden
            </button>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
              Jouw partner in groei
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
              Wij zijn meer dan een platform. We zijn jouw partner die je helpt
              je bedrijf te laten groeien, efficiënter te werken en meer te
              verdienen.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <div
                  key={index}
                  className={`bg-slate-50 rounded-2xl p-8 text-center motion-preset-slide-up motion-delay-${
                    index * 100
                  } hover:bg-white hover:shadow-2xl hover:-translate-y-2 transition-all duration-300`}
                >
                  <div className="inline-block p-4 bg-teal-100 text-teal-600 rounded-full mb-4">
                    <Icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-lg md:text-xl font-bold text-slate-800 mb-2">
                    {benefit.title}
                  </h3>
                  <p className="text-slate-600">{benefit.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 lg:py-24 bg-slate-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              Zo simpel werkt het
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
              In drie eenvoudige stappen ben je verbonden met nieuwe klanten en
              verdien je geld met je vakmanschap.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {howItWorks.map((item, index) => (
              <div
                key={item.step}
                className="relative rounded-2xl overflow-hidden group motion-preset-slide-up shadow-2xl shadow-black/30"
                style={
                  {
                    "--motion-delay": `${index * 150}ms`,
                  } as React.CSSProperties
                }
              >
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-96 object-cover transition-transform duration-500 group-hover:scale-110"
                  loading="lazy"
                  width="400"
                  height="384"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"></div>
                <div className="absolute bottom-0 left-0 p-8">
                  <div className="w-14 h-14 border-2 border-teal-400 bg-teal-500/20 text-teal-300 text-2xl font-bold rounded-full flex items-center justify-center mb-4 backdrop-blur-sm">
                    {item.step}
                  </div>
                  <h3
                    className="text-2xl font-bold text-white mb-3"
                    style={{ textShadow: "1px 1px 3px rgba(0,0,0,0.5)" }}
                  >
                    {item.title}
                  </h3>
                  <p className="text-slate-200">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Hoor het van je collega's
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Duizenden vakmensen gingen je voor. Dit is wat zij zeggen over
              Klusgebied.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className={`bg-slate-50 rounded-2xl p-8 shadow-lg motion-preset-slide-up motion-delay-${
                  index * 100
                }`}
              >
                <div className="flex items-center mb-4">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-14 h-14 rounded-full object-cover mr-4"
                    loading="lazy"
                    width="56"
                    height="56"
                  />
                  <div>
                    <div className="font-semibold text-slate-800">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-slate-600">
                      {testimonial.location}
                    </div>
                  </div>
                </div>
                <p className="text-slate-600 leading-relaxed italic">
                  "{testimonial.text}"
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="relative py-20 lg:py-24 overflow-hidden">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute z-0 w-auto min-w-full min-h-full max-w-none object-cover"
          preload="none"
          poster="https://cdn.pixabay.com/video/2016/06/27/3586-172490363_medium.mp4?__cf_chl_tk=y_jZ.y_jZ..._1s"
        >
          <source
            src="https://cdn.pixabay.com/video/2016/06/27/3586-172490363_medium.mp4"
            type="video/mp4"
          />
          Your browser does not support the video tag.
        </video>
        <div className="absolute inset-0 bg-slate-900/60"></div>
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2
            className="text-3xl md:text-4xl font-bold text-white mb-4 motion-preset-fade-down"
            style={{ textShadow: "2px 2px 8px rgba(0,0,0,0.6)" }}
          >
            Klaar om je bedrijf te laten groeien?
          </h2>
          <p
            className="mt-4 max-w-2xl mx-auto text-base md:text-lg text-white/90 motion-preset-fade-down motion-delay-200"
            style={{ textShadow: "1px 1px 4px rgba(0,0,0,0.6)" }}
          >
            Download de app, maak je profiel aan en begin vandaag nog met het
            ontvangen van klussen. De volgende successtory is van jou.
          </p>
          <div className="mt-8 flex flex-col sm:flex-row items-center gap-4 justify-center motion-preset-slide-up motion-delay-300">
            <a
              href="https://apps.apple.com/nl/app/klusgebied/id6747739000"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block hover:-translate-y-1 transition-transform duration-300"
            >
              <img
                src="/download-on-the-app-store.svg"
                alt="Download on the App Store"
                className="h-14 w-auto"
                loading="lazy"
                width={144}
                height={56}
              />
            </a>
            <a
              href="https://play.google.com/store/apps/details?id=nl.klusgebied.android"
              className="inline-block hover:-translate-y-1 transition-transform duration-300"
            >
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/7/78/Google_Play_Store_badge_EN.svg"
                alt="Get it on Google Play"
                className="h-14 w-auto"
                width={144}
                height={56}
                loading="lazy"
              />
            </a>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default VakmanLandingPage;
