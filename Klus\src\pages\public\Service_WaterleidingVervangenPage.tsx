/**
 * @description This component renders a comprehensive and SEO-optimized detail page for water pipe replacement services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for water pipe replacement.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Waves,
  ShieldCheck,
  Droplets,
  ArrowRight,
  Wrench,
  MapPin,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  Euro,
  ChevronLeft,
  ChevronRight,
  Quote,
} from "lucide-react";

const Service_WaterleidingVervangenPage = () => {
  usePageTitle("Waterleiding Vervangen | Klusgebied - Veilig & Professioneel");
  const navigate = useNavigate();

  const waterServices = [
    {
      icon: Wrench,
      title: "Koperen Leidingen",
      description:
        "Duurzaam en betrouwbaar, de traditionele keuze voor waterleidingen.",
      points: [
        "Lange levensduur en bestand tegen hoge druk.",
        "Antibacteriële eigenschappen voor schoon water.",
        "Vakkundig gesoldeerd voor een lekvrije verbinding.",
        "Geschikt voor zowel warm- als koudwaterleidingen.",
      ],
    },
    {
      icon: Waves,
      title: "Kunststof Leidingen (PEX)",
      description:
        "Flexibel, makkelijk te installeren en bestand tegen corrosie en kalkaanslag.",
      points: [
        "Flexibel materiaal, dus minder koppelstukken nodig.",
        "Ongevoelig voor corrosie en kalkaanslag.",
        "Snellere en vaak voordeligere installatie.",
        "Ideaal voor renovaties en moeilijk bereikbare plekken.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Loden Leidingen Vervangen",
      description: "Verwijderen van oude loden leidingen voor uw gezondheid.",
      points: [
        "Essentieel voor de gezondheid van u en uw gezin.",
        "Voorkomt looddeeltjes in uw drinkwater.",
        "Vakkundige en veilige verwijdering en afvoer.",
        "Vervanging door moderne, veilige materialen.",
      ],
    },
    {
      icon: Droplets,
      title: "Complete Renovatie",
      description:
        "Vervangen van alle waterleidingen in uw woning, bijvoorbeeld bij een grote verbouwing.",
      points: [
        "Ideaal bij een badkamer- of keukenrenovatie.",
        "Zekerheid van een compleet nieuw en betrouwbaar systeem.",
        "Voorkomt toekomstige problemen en lekkages.",
        "Aanleg van de watermeter tot aan de kranen.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Voorkom Lekkages",
      description:
        "Vervang verouderde leidingen voordat ze voor kostbare waterschade zorgen.",
    },
    {
      icon: <Droplets className="w-8 h-8 text-white" />,
      title: "Gezond Drinkwater",
      description:
        "Verwijder oude loden leidingen en zorg voor schoon en veilig drinkwater.",
    },
    {
      icon: <Wrench className="w-8 h-8 text-white" />,
      title: "Duurzame Materialen",
      description:
        "Wij werken uitsluitend met moderne, duurzame materialen die decennia meegaan.",
    },
  ];

  const faqs = [
    {
      question: "Wanneer moet ik mijn waterleidingen laten vervangen?",
      answer:
        "Het is raadzaam om waterleidingen te vervangen als ze ouder zijn dan 40 jaar, als u loden leidingen heeft, of als u regelmatig last heeft van lekkages of een lage waterdruk.",
    },
    {
      question: "Wat zijn de kosten voor het vervangen van waterleidingen?",
      answer:
        "De kosten variëren sterk en zijn afhankelijk van de omvang van het project, de bereikbaarheid van de leidingen en de gekozen materialen. Vraag een vrijblijvende offerte aan voor een precieze prijsopgave.",
    },
    {
      question: "Gebruiken jullie moderne, veilige materialen?",
      answer:
        "Absoluut. Wij gebruiken alleen KIWA-gekeurde materialen zoals koper en kunststof (PEX/AluPEX) die voldoen aan alle moderne eisen voor veiligheid en duurzaamheid.",
    },
    {
      question: "Hoe lang duurt het vervangen van de waterleidingen?",
      answer:
        "Voor een gemiddelde woning duurt het vervangen van de belangrijkste leidingen meestal 1 tot 3 dagen, afhankelijk van de complexiteit en de grootte van de woning.",
    },
  ];

  const reviews = [
    {
      name: "Familie de Jong",
      location: "Rotterdam",
      rating: 5,
      quote:
        "Onze loden leidingen zijn vervangen. De loodgieter werkte zeer netjes en we hebben nu weer een gerust gevoel over ons drinkwater.",
      highlighted: true,
    },
    {
      name: "Mark Visser",
      location: "Den Haag",
      rating: 5,
      quote:
        "Tijdens de badkamerrenovatie direct alle leidingen laten vervangen. Goed advies gekregen en vakkundig uitgevoerd.",
      highlighted: false,
    },
    {
      name: "Linda de Boer",
      location: "Leiden",
      rating: 5,
      quote:
        "Snelle service na een leidingbreuk. De oude leiding is direct vervangen door een nieuwe. Top werk!",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1611017050088-345a5cb98e2a?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1581720604719-ee1b1a4e44b1?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx3YXRlciUyMHBpcGUlMkMlMjBwbHVtYmluZyUyQyUyMGhvbWUlMjByZW5vdmF0aW9ufGVufDB8fHx8MTc1MTc0OTE5NXww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1586281380117-5a60ae2050cc?ixlib=rb-4.1.0&w=600&h=600",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1596394516093-501ba68a0ba6?ixlib=rb-4.1.0&w=600&h=600",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Beschrijf uw situatie",
      description:
        "Gaat het om oude leidingen, een lekkage of een complete renovatie? Geef zoveel mogelijk details.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Ontvang offertes",
      description:
        "Gecertificeerde loodgieters sturen u een offerte op maat voor de werkzaamheden.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & plan de klus",
      description:
        "Vergelijk de vakmannen, kies de beste deal en plan de vervanging van uw waterleidingen.",
      microcopy: "Vergelijk profielen en kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Loodgieters in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "loodgieter",
    color: "blue",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Waterleiding Vervangen | Klusgebied - Veilig & Professioneel
        </title>
        <meta
          name="description"
          content="Voorkom lekkages en geniet van schoon drinkwater. Onze loodgieters vervangen oude of beschadigde waterleidingen vakkundig en snel."
        />
      </Helmet>
      <main>
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-blue-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-blue-100 border border-blue-200/80 rounded-full px-4 py-2 mb-6">
                    <Waves className="w-5 h-5 text-blue-600" />
                    <span className="text-blue-800 font-semibold text-sm">
                      Waterleiding Vervangen
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Waterleiding Vervangen?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-cyan-500 mt-2">
                      Veilig & Betrouwbaar
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voorkom lekkages en geniet van schoon drinkwater. Onze
                    loodgieters vervangen oude of beschadigde waterleidingen
                    vakkundig en snel.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate("/plaats-een-klus/waterleiding-vervangen")
                      }
                      className="group inline-flex items-center justify-center bg-blue-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
                    >
                      Vraag direct een offerte aan
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>
              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1611017050088-345a5cb98e2a?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Loodgieter vervangt waterleidingen"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Laat uw waterleidingen vervangen in 3 simpele stappen.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-blue-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Een complete service voor een veilig en betrouwbaar
                waterleidingsysteem.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {waterServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-blue-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-blue-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost het vervangen van waterleidingen?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten zijn afhankelijk van de omvang van het project. Vraag
                een vrijblijvende offerte aan voor een prijs op maat.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Vervangen loden leidingen:{" "}
                    <strong className="text-slate-900">€1.000–€2.500</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Complete renovatie:{" "}
                    <strong className="text-slate-900">Op offertebasis</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Uurtarief loodgieter:{" "}
                    <strong className="text-slate-900">€50–€75</strong>
                  </span>
                </li>
              </ul>
              <button
                onClick={() =>
                  navigate("/plaats-een-klus/waterleiding-vervangen")
                }
                className="bg-blue-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die hun waterleidingen lieten
                vervangen.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-blue-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-blue-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-blue-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-blue-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een erkende loodgieter en wees verzekerd van een
                veilig en betrouwbaar systeem.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-blue-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        <section className="bg-gradient-to-r from-blue-500 to-cyan-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar om uw waterleidingen aan te pakken?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag vandaag nog een vrijblijvende offerte aan en zorg voor een
              veilig en betrouwbaar watersysteem in uw huis.
            </p>
            <button
              onClick={() =>
                navigate("/plaats-een-klus/waterleiding-vervangen")
              }
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu een offerte aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/waterleiding-vervangen")}
          className="w-full group inline-flex items-center justify-center bg-blue-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_WaterleidingVervangenPage;
