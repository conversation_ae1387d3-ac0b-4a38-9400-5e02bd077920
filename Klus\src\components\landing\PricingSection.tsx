

/**
 * @description This component renders a reusable pricing section for various services. It displays the cost structure for a service, including hourly rates and specific task prices, in a clear and visually appealing card. The component is customizable with props for service name, price details, and theme color to match the branding of different service pages. Key variables include serviceName, priceItems, and themeColor, which control the content and appearance of the pricing card.
 */
import React from 'react';
import { Euro, CheckCircle, ArrowRight } from 'lucide-react';

const PricingSection = ({ serviceName, priceItems, themeColor = 'teal' }) => {
  const colorThemes = {
    yellow: {
      bg: 'bg-yellow-100',
      text: 'text-yellow-600',
      buttonBg: 'bg-yellow-500',
      buttonHoverBg: 'hover:bg-yellow-600',
      shadow: 'hover:shadow-yellow-500/30',
      check: 'text-yellow-500',
    },
    blue: {
      bg: 'bg-blue-100',
      text: 'text-blue-600',
      buttonBg: 'bg-blue-500',
      buttonHoverBg: 'hover:bg-blue-600',
      shadow: 'hover:shadow-blue-500/30',
      check: 'text-blue-500',
    },
    gray: {
      bg: 'bg-gray-100',
      text: 'text-gray-600',
      buttonBg: 'bg-slate-800',
      buttonHoverBg: 'hover:bg-slate-900',
      shadow: 'hover:shadow-slate-500/30',
      check: 'text-gray-500',
    },
    cyan: {
      bg: 'bg-cyan-100',
      text: 'text-cyan-600',
      buttonBg: 'bg-cyan-500',
      buttonHoverBg: 'hover:bg-cyan-600',
      shadow: 'hover:shadow-cyan-500/30',
      check: 'text-cyan-500',
    },
    purple: {
      bg: 'bg-purple-100',
      text: 'text-purple-600',
      buttonBg: 'bg-purple-500',
      buttonHoverBg: 'hover:bg-purple-600',
      shadow: 'hover:shadow-purple-500/30',
      check: 'text-purple-500',
    },
    teal: {
      bg: 'bg-teal-100',
      text: 'text-teal-600',
      buttonBg: 'bg-teal-500',
      buttonHoverBg: 'hover:bg-teal-600',
      shadow: 'hover:shadow-teal-500/30',
      check: 'text-teal-500',
    },
  };

  const theme = colorThemes[themeColor] || colorThemes.teal;

  return (
    <section className="py-16 lg:py-24 bg-slate-50">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
          <div className={`inline-flex items-center justify-center h-16 w-16 rounded-full ${theme.bg} ${theme.text} mb-6 shadow-sm`}>
            <Euro className="w-8 h-8" />
          </div>
          <h2 className="text-3xl font-bold text-slate-900 mb-4">Wat kost een {serviceName}?</h2>
          <p className="text-slate-600 leading-relaxed mb-6">
            Bij Klusgebied betaal je een eerlijke prijs. Je ontvangt altijd eerst een prijsvoorstel voordat de klus start.
          </p>
          <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
            {priceItems.map((item, index) => (
              <li key={index} className="flex items-start">
                <CheckCircle className={`w-5 h-5 ${theme.check} mr-3 mt-1 flex-shrink-0`} />
                <span>{item.label} <strong className="text-slate-900">{item.value}</strong> {item.unit}</span>
              </li>
            ))}
          </ul>
          <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
            Geen verborgen kosten. Altijd vooraf een prijsafspraak.
          </div>
          <button 
            onClick={() => window.open("https://klusgebied.nl/plaats-een-klus", "_blank")} 
            className={`${theme.buttonBg} text-white px-8 py-3 rounded-xl font-semibold ${theme.buttonHoverBg} transition-all duration-300 shadow-lg ${theme.shadow} transform hover:-translate-y-1`}
          >
            Vraag gratis prijsvoorstellen aan <ArrowRight className="inline-block ml-2" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
  
