import { Loader2 } from "lucide-react";

import { GradientLayout } from "../layout/GradientLayout";

export const JobListLoader = () => {
  return (
    <GradientLayout>
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4 animate-fade-in">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <p className="text-sm text-muted-foreground">
            Klussen worden geladen...
          </p>
        </div>
      </div>
    </GradientLayout>
  );
};
