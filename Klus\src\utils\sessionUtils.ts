import { supabase } from "@/integrations/supabase/client";
import { ROUTE_PATHS, TOAST_MESSAGES, SESSION_CONFIG } from "@/config/routes";

export interface SessionErrorHandler {
  onSessionExpired?: () => void;
  onError?: (error: any) => void;
  onSuccess?: () => void;
}

export class SessionManager {
  private static instance: SessionManager;
  
  private constructor() {}
  
  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  async validateSession(): Promise<boolean> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error("Session validation error:", error);
        return false;
      }
      
      return !!session;
    } catch (error) {
      console.error("Unexpected error during session validation:", error);
      return false;
    }
  }

  async refreshSession(): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        console.error("Session refresh error:", error);
        return false;
      }
      
      return !!data.session;
    } catch (error) {
      console.error("Unexpected error during session refresh:", error);
      return false;
    }
  }

  async signOut(handlers?: SessionErrorHandler): Promise<void> {
    try {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error("Sign out error:", error);
        handlers?.onError?.(error);
        return;
      }
      
      this.clearLocalData();
      handlers?.onSuccess?.();
    } catch (error) {
      console.error("Unexpected error during sign out:", error);
      handlers?.onError?.(error);
    }
  }

  clearLocalData(): void {
    localStorage.clear();
    // Clear any session cookies if needed
    document.cookie = `${SESSION_CONFIG.COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }

  isSessionExpiringSoon(session: any): boolean {
    if (!session?.expires_at) return false;
    
    const expiresAt = new Date(session.expires_at * 1000);
    const now = new Date();
    const timeUntilExpiry = expiresAt.getTime() - now.getTime();
    
    return timeUntilExpiry < SESSION_CONFIG.REFRESH_THRESHOLD * 1000;
  }

  getReturnUrl(currentPath: string, search: string): string {
    return encodeURIComponent(currentPath + search);
  }

  buildAuthUrl(returnUrl?: string): string {
    if (returnUrl) {
      return `${ROUTE_PATHS.AUTH}?returnUrl=${returnUrl}`;
    }
    return ROUTE_PATHS.AUTH;
  }
}

// Utility functions for common session operations
export const sessionUtils = {
  manager: SessionManager.getInstance(),
  
  async handleSessionError(navigate: (path: string) => void, toast: any) {
    sessionUtils.manager.clearLocalData();
    navigate(ROUTE_PATHS.AUTH);
    toast({
      variant: "destructive",
      ...TOAST_MESSAGES.SESSION_EXPIRED,
    });
  },

  async handleLogout(navigate: (path: string) => void, toast: any) {
    await sessionUtils.manager.signOut({
      onSuccess: () => {
        navigate(ROUTE_PATHS.AUTH);
        toast(TOAST_MESSAGES.LOGOUT_SUCCESS);
      },
      onError: () => {
        toast({
          variant: "destructive",
          ...TOAST_MESSAGES.LOGOUT_ERROR,
        });
      }
    });
  },

  buildRedirectUrl(currentPath: string, search: string): string {
    const returnUrl = sessionUtils.manager.getReturnUrl(currentPath, search);
    return sessionUtils.manager.buildAuthUrl(returnUrl);
  }
};
