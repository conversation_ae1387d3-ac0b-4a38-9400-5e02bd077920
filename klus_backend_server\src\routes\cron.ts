import { Router } from 'express';
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler';
import { validateRequired } from '../middleware/validation';
import { adminAuthMiddleware, AuthenticatedRequest } from '../middleware/auth';

const router = Router();

// Schedule cron job
router.post('/schedule',
  adminAuthMiddleware,
  validateRequired(['job_name', 'cron_expression', 'command']),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { job_name, cron_expression, command } = req.body;

    const { error: cronJobError } = await req.supabase!.rpc('schedule_cron_job', {
      job_name,
      cron_expression,
      command,
    });

    if (cronJobError) {
      throw cronJobError;
    }

    res.json({ success: true });
  })
);

// Unschedule cron job
router.delete('/unschedule',
  adminAuthMiddleware,
  validateRequired(['job_name']),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { job_name } = req.body;

    const { error: cronJobError } = await req.supabase!.rpc('unschedule_cron_job', {
      job_name,
    });

    if (cronJobError) {
      throw cronJobError;
    }

    res.json({ success: true });
  })
);

// Get cron job schedule
router.post('/schedule/get',
  adminAuthMiddleware,
  validateRequired(['job_name']),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { job_name } = req.body;

    const { data, error: cronJobError } = await req.supabase!.rpc('get_cron_job_schedule', {
      job_name,
    });

    if (cronJobError) {
      throw cronJobError;
    }

    res.json({ data, success: true });
  })
);

export default router;
