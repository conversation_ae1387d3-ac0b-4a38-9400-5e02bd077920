import { MapPin, Calendar, User, Clock } from "lucide-react";

import { Badge } from "@/components/ui/badge";

interface JobMetadataProps {
  jobId: string;
  location: string;
  house_number: string;
  house_number_addition?: string;
  date: string;
  isOwner: boolean;
  status: string;
  isDirect: boolean;
}

export const JobMetadata = ({
  location,
  date,
  isOwner,
  status,
  isDirect,
}: JobMetadataProps) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "open":
        return "bg-blue-500 hover:bg-blue-600";
      case "in_behandeling":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "accepted":
        return "bg-green-500 hover:bg-green-600";
      case "completed":
        return "bg-green-700 hover:bg-green-800";
      case "cancelled":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  return (
    <div className="flex flex-wrap gap-4">
      <div className="flex items-center gap-2 text-gray-600">
        <MapPin className="w-4 h-4" />
        <span>{location}</span>
      </div>
      <div className="flex items-center gap-2 text-gray-600">
        <Calendar className="w-4 h-4" />
        <span>{new Date(date).toLocaleDateString()}</span>
      </div>
      <div className="flex items-center gap-2">
        <Clock className="w-4 h-4 text-gray-600" />
        <Badge
          className={`${
            isDirect
              ? "bg-purple-500 hover:bg-purple-600"
              : getStatusColor(status)
          } text-white`}
        >
          {isDirect ? "Directe Aanvraag" : status}
        </Badge>
      </div>
      {isOwner && (
        <div className="flex items-center gap-2 text-gray-600">
          <User className="w-4 h-4" />
          <span>Jouw klus</span>
        </div>
      )}
    </div>
  );
};
