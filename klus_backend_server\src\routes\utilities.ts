import { Router } from "express";
import { as<PERSON><PERSON><PERSON><PERSON> } from "../middleware/errorHandler";
import { validateRequired } from "../middleware/validation";
import { config } from "../config/environment";

const router = Router();

interface GeocodeRequest {
  address: string;
}

interface GeocodeResponse {
  lat: number;
  lng: number;
}

// Verify reCAPTCHA
router.post(
  "/recaptcha/verify",
  validateRequired(["token"]),
  asyncHandler(async (req, res) => {
    const { token } = req.body;

    // Verify the token with Google's reCAPTCHA API
    const response = await fetch(
      `https://www.google.com/recaptcha/api/siteverify?secret=${config.recaptcha.secretKey}&response=${token}`,
      { method: "POST" }
    );

    const data = await response.json();
    res.json(data);
  })
);

// Geocode address
router.post(
  "/geocode",
  validateRequired(["address"]),
  asyncHandler(async (req, res) => {
    const { address }: GeocodeRequest = req.body;

    if (!config.googleMaps.apiKey) {
      throw new Error("Google Maps API key not configured");
    }

    console.log("Geocoding address:", address);

    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
      address
    )}&key=${config.googleMaps.apiKey}`;

    const response = await fetch(url);
    const data = await response.json();

    if (data.status === "OK") {
      const location = data.results[0].geometry.location;
      const result: GeocodeResponse = {
        lat: location.lat,
        lng: location.lng,
      };

      console.log("Geocoding successful:", result);
      res.json(result);
    } else {
      console.error("Geocoding error:", data.status);
      throw new Error(`Geocoding failed: ${data.status}`);
    }
  })
);

export default router;
