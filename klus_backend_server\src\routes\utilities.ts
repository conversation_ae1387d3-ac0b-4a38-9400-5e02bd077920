import { Router } from 'express';
import multer from 'multer';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { validateRequired } from '../middleware/validation';
import { config } from '../config/environment';
import { supabaseAdmin } from '../config/supabase';

const router = Router();
const upload = multer({ storage: multer.memoryStorage() });

interface GeocodeRequest {
  address: string;
}

interface GeocodeResponse {
  lat: number;
  lng: number;
}

// Verify reCAPTCHA
router.post('/recaptcha/verify',
  validateRequired(['token']),
  asyncHandler(async (req, res) => {
    const { token } = req.body;

    // Verify the token with Google's reCAPTCHA API
    const response = await fetch(
      `https://www.google.com/recaptcha/api/siteverify?secret=${config.recaptcha.secretKey}&response=${token}`,
      { method: 'POST' }
    );

    const data = await response.json();
    res.json(data);
  })
);

// Geocode address
router.post('/geocode',
  validateRequired(['address']),
  asyncHandler(async (req, res) => {
    const { address }: GeocodeRequest = req.body;

    if (!config.googleMaps.apiKey) {
      throw new Error('Google Maps API key not configured');
    }

    console.log('Geocoding address:', address);

    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
      address
    )}&key=${config.googleMaps.apiKey}`;

    const response = await fetch(url);
    const data = await response.json();

    if (data.status === 'OK') {
      const location = data.results[0].geometry.location;
      const result: GeocodeResponse = {
        lat: location.lat,
        lng: location.lng,
      };

      console.log('Geocoding successful:', result);
      res.json(result);
    } else {
      console.error('Geocoding error:', data.status);
      throw new Error(`Geocoding failed: ${data.status}`);
    }
  })
);

// Upload chat attachment
router.post('/chat/upload',
  upload.single('file'),
  asyncHandler(async (req, res) => {
    const file = req.file;
    const { jobId, senderId, receiverId, chat_id } = req.body;

    if (!file || !jobId || !senderId || !receiverId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const fileExt = file.originalname.split('.').pop();
    const fileName = `${crypto.randomUUID()}.${fileExt}`;
    const filePath = `${jobId}/${fileName}`;

    // Convert buffer to File-like object for Supabase
    const fileBlob = new Blob([file.buffer], { type: file.mimetype });

    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from('chat_attachments')
      .upload(filePath, fileBlob, {
        contentType: file.mimetype,
        upsert: false,
      });

    if (uploadError) {
      console.error('Upload error:', uploadError);
      throw uploadError;
    }

    const { data: urlData } = await supabaseAdmin.storage
      .from('chat_attachments')
      .getPublicUrl(filePath);

    const fileUrl = urlData.publicUrl;

    // Create a message for the file
    const { error: messageError } = await supabaseAdmin.from('messages').insert({
      job_id: jobId,
      sender_id: senderId,
      receiver_id: receiverId,
      content: `[Bestand gedeeld] ${file.originalname}`,
      attachment_url: fileUrl,
      attachment_type: file.mimetype.startsWith('image/') ? 'image' : 'file',
      chat_id: chat_id,
    });

    if (messageError) {
      console.error('Message error:', messageError);
      throw messageError;
    }

    res.json({
      message: 'File uploaded successfully',
      fileUrl,
    });
  })
);

export default router;
