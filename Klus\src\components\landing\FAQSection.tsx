/**
 * @description This component renders a reusable and accessible FAQ section with an accordion interface. It allows users to expand and collapse answers to frequently asked questions, improving user experience by organizing information cleanly. The component is styled with modern aesthetics and smooth animations. Key variables include the 'faqs' prop, which is an array of question-answer objects, and 'openIndex' state to manage which FAQ item is currently open.
 */
import React, { useState } from "react";
import { ChevronDown } from "lucide-react";

const FAQSection = ({ faqs }) => {
	const [openIndex, setOpenIndex] = useState(null);

	const toggleFAQ = (index) => {
		setOpenIndex(openIndex === index ? null : index);
	};

	return (
		<div className="py-16 lg:py-24 bg-white">
			<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="text-center mb-12">
					<h2 className="text-3xl md:text-4xl font-bold text-slate-900">
						Veelgestelde Vragen
					</h2>
					<p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
						Vind hier antwoorden op de meest voorkomende vragen over onze
						diensten.
					</p>
				</div>
				<div className="space-y-4">
					{faqs.map((faq, index) => (
						<div
							key={index}
							className="border border-slate-200 rounded-2xl overflow-hidden transition-all duration-300"
						>
							<button
								onClick={() => toggleFAQ(index)}
								className="w-full flex justify-between items-center p-6 text-left font-semibold text-lg text-slate-800 hover:bg-slate-50"
							>
								<span>{faq.question}</span>
								<ChevronDown
									className={`w-6 h-6 text-teal-500 transition-transform duration-300 ${
										openIndex === index ? "rotate-180" : ""
									}`}
								/>
							</button>
							<div
								className={`grid transition-all duration-500 ease-in-out ${
									openIndex === index
										? "grid-rows-[1fr] opacity-100"
										: "grid-rows-[0fr] opacity-0"
								}`}
							>
								<div className="overflow-hidden">
									<div className="p-6 pt-0 text-slate-600 leading-relaxed">
										{faq.answer}
									</div>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default FAQSection;
