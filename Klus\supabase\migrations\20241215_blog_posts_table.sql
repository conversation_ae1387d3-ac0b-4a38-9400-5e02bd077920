-- Migration: Create blog posts table
-- Description: Creates the blog posts table migrated from the separate blog project

-- Create the posts table
CREATE TABLE IF NOT EXISTS "posts" (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  summary TEXT NOT NULL,
  content TEXT NOT NULL,
  category TEXT NOT NULL,
  author_id INTEGER NOT NULL,
  cover_image_url TEXT,
  tags TEXT[],
  city TEXT,
  region TEXT,
  seo_title TEXT,
  seo_description TEXT,
  published BOOLEAN DEFAULT true,
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on slug for faster lookups
CREATE INDEX IF NOT EXISTS "idx_posts_slug" ON "posts" (slug);

-- Create index on published status and date for filtering
CREATE INDEX IF NOT EXISTS "idx_posts_published" ON "posts" (published, published_at);

-- Enable row-level security
ALTER TABLE "posts" ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Public can read published posts
CREATE POLICY "Public can view published posts" ON "posts"
FOR SELECT USING (published = true);

-- Only authenticated users with admin role can insert/update/delete
CREATE POLICY "Admins can manage posts" ON "posts"
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.user_type = 'admin'
  )
);

-- Note: Data migration will be handled separately through the application
-- as it requires exporting from the source Supabase instance and importing to this one
