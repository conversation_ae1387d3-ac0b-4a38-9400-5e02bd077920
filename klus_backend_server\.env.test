# Test Environment Variables
NODE_ENV=test
PORT=3001

# Supabase (use test project or mock values)
SUPABASE_URL=https://test.supabase.co
SUPABASE_ANON_KEY=test_anon_key
SUPABASE_SERVICE_ROLE_KEY=test_service_key

# Mock API keys for testing
MOLLIE_API_KEY=test_mollie_key
MOLLIE_TEST_API_KEY=test_mollie_test_key
RESEND_API_KEY=test_resend_key
RECAPTCHA_SECRET_KEY=test_recaptcha_key
HUBSPOT_API_KEY=test_hubspot_key
GOOGLE_MAPS_API_KEY=test_maps_key

# MessageBird test config
MESSAGEBIRD_WORKSPACE_ID=test_workspace
MESSAGEBIRD_CHANNEL_ID=test_channel
MESSAGEBIRD_ACCESS_KEY=test_access_key

# Test URLs
SITE_URL=http://localhost:3000
BASE_URL=http://localhost:3001
