/**
 * @description This component renders a comprehensive and SEO-optimized detail page for lighting specialist services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for lighting specialists. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import PricingSection from "@/components/landing/PricingSection";
import {
  ArrowLeft,
  Lightbulb,
  Clock,
  ArrowRight,
  Sparkles,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Sun,
  Palette,
} from "lucide-react";

const Service_VerlichtingSpecialistPage = () => {
  usePageTitle(
    "Verlichting Specialist Nodig? | Klusgebied - Lichtplan & Installatie"
  );
  const navigate = useNavigate();

  const services = [
    {
      icon: Palette,
      title: "Lichtplan op Maat",
      description:
        "Een professioneel lichtplan voor de perfecte sfeer en functionaliteit.",
      points: [
        "Analyse van de ruimte en uw wensen.",
        "Advies over armaturen, lichtkleur en dimbaarheid.",
        "Creëert een perfect gebalanceerd lichtbeeld.",
        "Voorkomt donkere hoeken en verhoogt het wooncomfort.",
      ],
    },
    {
      icon: Lightbulb,
      title: "LED Verlichting Installatie",
      description:
        "Energiezuinige en duurzame LED-verlichting voor binnen en buiten.",
      points: [
        "Installatie van inbouwspots, opbouwspots en LED-strips.",
        "Bespaar tot 80% op uw energiekosten.",
        "Lange levensduur en verkrijgbaar in vele stijlen.",
        "Direct de juiste sfeer met dimbare LED-verlichting.",
      ],
    },
    {
      icon: Sun,
      title: "Buitenverlichting & Tuinverlichting",
      description:
        "Creëer een sfeervolle en veilige tuin met de juiste verlichting.",
      points: [
        "Functionele verlichting bij oprit en voordeur.",
        "Sfeervolle verlichting om bomen of terras uit te lichten.",
        "Verhoogt de veiligheid en schrikt inbrekers af.",
        "Weerbestendige armaturen voor jarenlang plezier.",
      ],
    },
    {
      icon: Sparkles,
      title: "Slimme Verlichting (Domotica)",
      description: "Bedien uw verlichting met uw smartphone, tablet of stem.",
      points: [
        "Integratie met systemen als Philips Hue en KlikAanKlikUit.",
        "Stel tijdschema's en sfeerscenes in.",
        "Bedien uw verlichting, ook als u niet thuis bent.",
        "Verhoog comfort en bespaar energie.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Sparkles className="w-8 h-8 text-white" />,
      title: "Sfeervol & Functioneel",
      description:
        "Wij creëren de perfecte balans tussen sfeer, design en praktisch gebruik.",
    },
    {
      icon: <Lightbulb className="w-8 h-8 text-white" />,
      title: "Energiebesparende LED",
      description:
        "Bespaar tot 80% op uw verlichtingskosten met moderne LED-technologie.",
    },
    {
      icon: <Sun className="w-8 h-8 text-white" />,
      title: "Professioneel Advies",
      description:
        "Onze specialisten geven advies over de beste lichtoplossingen voor uw woning.",
    },
  ];

  const faqs = [
    {
      question: "Wat is een lichtplan en waarom heb ik dat nodig?",
      answer:
        "Een lichtplan is een gedetailleerd ontwerp dat aangeeft waar welke verlichting komt voor een optimaal resultaat. Het voorkomt donkere hoeken en zorgt voor de juiste sfeer.",
    },
    {
      question: "Waarom zou ik voor LED verlichting kiezen?",
      answer:
        "LED-verlichting is zeer energiezuinig, heeft een veel langere levensduur dan traditionele lampen en is verkrijgbaar in vele kleuren en stijlen.",
    },
    {
      question:
        "Kan ik mijn bestaande lampen vervangen door smart verlichting?",
      answer:
        "Ja, in veel gevallen is dit mogelijk door slimme lampen in uw huidige armaturen te draaien of door slimme schakelaars te installeren. Onze specialist kan u hierover adviseren.",
    },
    {
      question: "Wat kost een verlichting specialist?",
      answer:
        "De kosten zijn afhankelijk van de omvang van het project. Een basis lichtplan start vanaf circa €250. Voor installatie kunt u rekenen op een uurtarief tussen de €45 en €70.",
    },
  ];

  const reviews = [
    {
      name: "Laura de Boer",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Het lichtplan heeft ons huis echt getransformeerd. Zoveel sfeervoller nu!",
      highlighted: true,
    },
    {
      name: "Mark Visser",
      location: "Eindhoven",
      rating: 5,
      quote:
        "De slimme verlichting werkt perfect. Super handig om alles met mijn telefoon te bedienen.",
      highlighted: false,
    },
    {
      name: "Familie Janssen",
      location: "Groningen",
      rating: 5,
      quote:
        "Onze tuin is 's avonds prachtig verlicht. Een hele nieuwe dimensie. Vakkundig geïnstalleerd.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1698864551605-fab9fed03af5?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnRlcmlvciUyMGxpZ2h0aW5nJTJDJTIwc3R5bGlzaCUyMGRlc2lnbiUyQyUyMG1vZGVybiUyMGhvbWV8ZW58MHx8fHwxNzUxNzQxNTYwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1646618696986-76e3e4c7ba5c?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxMRUQlMjBsaWdodGluZyUyMGluc3RhbGxhdGlvbiUyQyUyMGVuZXJneS1lZmZpY2llbnQlMkMlMjBob21lJTIwaW1wcm92ZW1lbnR8ZW58MHx8fHwxNzUxNzQxNTYwfDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1658692051708-519fbdac7e8f?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxvdXRkb29yJTIwbGlnaHRpbmclMkMlMjBnYXJkZW4lMjBpbGx1bWluYXRpb24lMkMlMjBzYWZldHklMjBsaWdodGluZ3xlbnwwfHx8fDE3NTE3NDE1NjB8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1672718985175-be33c65e17f8?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxzbWFydCUyMGxpZ2h0aW5nJTJDJTIwaG9tZSUyMGF1dG9tYXRpb24lMkMlMjBtb2Rlcm4lMjB0ZWNobm9sb2d5fGVufDB8fHx8MTc1MTc0MTU2MHww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description:
        "Beschrijf je wensen voor de verlichting. Voeg foto's toe van de ruimtes.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Specialisten reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde verlichtingsspecialisten.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & start",
      description:
        "Vergelijk profielen en kies de beste specialist voor jouw lichtplan en installatie.",
      microcopy: "Vergelijk profielen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Verlichtingsspecialisten in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "verlichtingsspecialist",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Verlichting Specialist Nodig? | Klusgebied - Lichtplan & Installatie
        </title>
        <meta
          name="description"
          content="Creëer de perfecte sfeer met een professioneel lichtplan en vakkundige installatie. Voor binnen en buiten. Vind een specialist via Klusgebied."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Verlichting Specialist",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            offers: {
              "@type": "Offer",
              priceCurrency: "EUR",
              priceSpecification: {
                "@type": "PriceSpecification",
                priceCurrency: "EUR",
                minPrice: "45",
                maxPrice: "70",
                valueAddedTaxIncluded: true,
                unitText: "HOUR",
                description:
                  "Uurtarief voor installatie. Lichtplan op offertebasis.",
              },
            },
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            mainEntity: faqs.map((faq) => ({
              "@type": "Question",
              name: faq.question,
              acceptedAnswer: {
                "@type": "Answer",
                text: faq.answer,
              },
            })),
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-yellow-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-yellow-100 border border-yellow-200/80 rounded-full px-4 py-2 mb-6">
                    <Lightbulb className="w-5 h-5 text-yellow-600" />
                    <span className="text-yellow-800 font-semibold text-sm">
                      Verlichting Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Verlichting Specialist?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-500 to-orange-500 mt-2">
                      Creëer de Perfecte Sfeer
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Van een professioneel lichtplan tot de installatie van
                    slimme LED-verlichting. Vind een geverifieerde specialist
                    bij u in de buurt.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus")}
                      className="group inline-flex items-center justify-center bg-yellow-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-yellow-600 transition-all duration-300 shadow-lg hover:shadow-yellow-500/30 transform hover:-translate-y-1"
                    >
                      Vind jouw specialist
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.9/5</span>
                    <span>gebaseerd op 180+ klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1698864551605-fab9fed03af5?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxpbnRlcmlvciUyMGxpZ2h0aW5nJTJDJTIwc3R5bGlzaCUyMGRlc2lnbiUyQyUyMG1vZGVybiUyMGhvbWV8ZW58MHx8fHwxNzUxNzQxNTYwfDA&ixlib=rb-4.1.0?w=1024&h=1024"
                    alt="Stijlvolle interieurverlichting"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte specialist voor jouw
                verlichting.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-yellow-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Verlichtingsdiensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Van een compleet lichtplan tot de installatie van slimme
                LED-verlichting.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-yellow-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-yellow-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-yellow-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <PricingSection
          serviceName="verlichtingsspecialist"
          themeColor="yellow"
          priceItems={[
            {
              label: "Installatie:",
              value: "€45–€70",
              unit: "per uur (incl. BTW)",
            },
            {
              label: "Lichtplan:",
              value: "Vanaf €250",
              unit: ", afhankelijk van de grootte",
            },
            { label: "Voorrijkosten:", value: "Vaak inbegrepen", unit: "" },
          ]}
        />

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een verlichtingsspecialist
                vonden via Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-yellow-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-yellow-200"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                        \
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-yellow-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-yellow-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een specialist en transformeer uw huis met de kracht
                van licht.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-yellow-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-yellow-500 to-orange-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar om uw huis te verlichten?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvend advies aan voor een lichtplan op maat en
              ontdek de mogelijkheden.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus")}
              className="bg-white text-yellow-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu een lichtplan aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus")}
          className="w-full group inline-flex items-center justify-center bg-yellow-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-yellow-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_VerlichtingSpecialistPage;
