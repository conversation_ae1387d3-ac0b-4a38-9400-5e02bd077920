import { ChangeEvent, useCallback, useEffect, useRef, useState } from "react";
import debounce from "lodash/debounce";
import { useNavigate } from "react-router-dom";

import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Title,
  <PERSON><PERSON><PERSON>eader,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";

const CreateJobModal = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [isValid, setIsValid] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const abortControllerRef = useRef<AbortController | null>(null);

  const navigate = useNavigate();

  const handleSubmit = async () => {
    navigate(`/banen/new?email=${email}`);
  };

  const validateEmail = (email: string) => {
    const emailPattern = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
    return emailPattern.test(email);
  };

  const checkEmailExists = useCallback(
    debounce(async (email: string) => {
      // Cancel any pending request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      const controller = new AbortController();
      abortControllerRef.current = controller;

      setIsChecking(true);

      try {
        const { data: user, error: supabaseError } = await supabase
          .from("profiles")
          .select("id")
          .match({
            email,
            user_type: "klusaanvrager",
          })
          .abortSignal(controller.signal)
          .maybeSingle();

        if (supabaseError) throw new Error("Database query failed");

        setIsValid(!!user);
        setError(user ? null : "Geen account gevonden met dit email adres");
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") return;

        console.error("[checkEmailExists]:", error);
        setError(
          error instanceof Error && error.message === "Database query failed"
            ? "Er is een fout opgetreden bij het controleren van het email adres"
            : "Er is een fout opgetreden"
        );
        setIsValid(false);
      } finally {
        if (abortControllerRef.current === controller) {
          setIsChecking(false);
          abortControllerRef.current = null;
        }
      }
    }, 500),
    []
  );

  const handleChangeEmail = (e: ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);

    if (!newEmail) {
      setError(null);
      setIsValid(false);
      return;
    }

    if (!validateEmail(newEmail)) {
      setError("Voer een geldig e-mailadres in");
      setIsValid(false);
      return;
    }

    // Only check with API if email format is valid
    checkEmailExists(newEmail);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
      checkEmailExists.cancel();
    };
  }, [checkEmailExists]);

  return (
    <>
      <Button className="text-white" onClick={() => setIsOpen(true)}>
        Een nieuwe baan aanmaken
      </Button>
      <Dialog open={isOpen} onOpenChange={() => setIsOpen(false)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Maak een nieuwe taak met Klusaanvrager</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <p className="text-sm text-muted-foreground">
              Voer het e-mailadres van Klusaanvrager in
            </p>
            <Input
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={handleChangeEmail}
              className={error ? "border-red-500" : ""}
              isLoading={isChecking}
            />
            {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Annuleren
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!isValid}
              className="ml-2 text-white"
            >
              Creëren
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CreateJobModal;
