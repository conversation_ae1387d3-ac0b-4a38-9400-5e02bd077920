import { Settings, Mail, Globe, CreditCard } from "lucide-react";

import { Card } from "@/components/ui/card";

const SettingsPage = () => {
  return (
    <div className="max-w-7xl px-6 sm:px-0 mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Platform Instellingen</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Settings className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-semibold mb-2">Algemene Instellingen</p>
              <p className="text-sm text-muted-foreground">
                Configureer algemene platform instellingen
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Mail className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-semibold mb-2">Email Configuratie</p>
              <p className="text-sm text-muted-foreground">
                Beheer email instellingen en templates
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Globe className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-semibold mb-2">Taal en Regio</p>
              <p className="text-sm text-muted-foreground">
                Pas taal- en regio-instellingen aan
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <CreditCard className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-semibold mb-2">Betalingsinstellingen</p>
              <p className="text-sm text-muted-foreground">
                Configureer betalingsopties en tarieven
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default SettingsPage;
