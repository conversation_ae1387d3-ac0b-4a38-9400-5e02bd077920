import { useAtomValue } from "jotai";
import { Loader2, LockKeyhole } from "lucide-react";

import { Card } from "@/components/ui/card";
import { JobCardImage } from "./job/card/JobCardImage";
import { JobCardContent } from "./job/card/JobCardContent";
import { useUserProfile } from "@/hooks/useUserProfile";
import { useJobResponse } from "./job/hooks/useJobResponse";
import { isProfileCompletedAtom } from "@/states/profile";
import { useAuth } from "./auth/hooks/useAuth";

interface JobCardProps {
  id: string;
  title: string;
  description: string;
  status?: string;
  photos?: string[];
  isDirect: boolean;
  onClick: () => void;
  job: any;
}

export const JobCard = ({
  id,
  title,
  description,
  status = "open",
  photos = [],
  isDirect = false,
  onClick,
  job,
}: JobCardProps) => {
  const { data: profile, isLoading: isProfileLoading } = useUserProfile();
  const isProfileCompleted = useAtomValue(isProfileCompletedAtom);
  const { userProfile } = useAuth();

  const {
    hasResponded,
    responseCount,
    isLoading: isResponseLoading,
    isCheckingResponse,
  } = useJobResponse(id, status, job, userProfile);

  const isLocked =
    userProfile?.user_type === "vakman" &&
    (!userProfile?.balance || !isProfileCompleted);

  if (isProfileLoading || isResponseLoading) {
    return (
      <Card className="p-6 flex items-center justify-center min-h-[320px] bg-card">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
      </Card>
    );
  }

  return (
    <Card
      className={`group overflow-hidden transition-all duration-300 bg-card border-border/30 animate-fade-in relative
        ${isLocked ? "pointer-events-none" : "hover:shadow-lg cursor-pointer"}`}
      onClick={isLocked ? undefined : onClick}
    >
      <div className={`flex flex-col h-full ${isLocked ? "blur-md" : ""}`}>
        <JobCardImage
          photos={photos}
          title={title}
          status={status}
          responseCount={responseCount}
          isDirect={isDirect}
          job={job}
        />
        <JobCardContent
          title={title}
          description={description}
          isVakman={profile?.user_type === "vakman"}
          status={status}
          hasResponded={hasResponded}
          jobId={id}
          onRespond={onClick}
          isCheckingResponse={isCheckingResponse}
        />
      </div>
      {isLocked && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/5">
          <div className="bg-background/95 p-4 rounded-full">
            <LockKeyhole className="h-10 w-10 text-muted-foreground" />
          </div>
        </div>
      )}
    </Card>
  );
};
