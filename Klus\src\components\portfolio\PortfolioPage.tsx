import { useState, useEffect } from "react";
import { Plus, Loader2 } from "lucide-react";

import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { AddProjectForm } from "./AddProjectForm";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ProjectCard } from "./ProjectCard";
import { Project } from "./types";
import { BackToDashboard } from "@/components/BackToDashboard";

export const PortfolioPage = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  const fetchProjects = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from("portfolio_projects")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching projects:", error);
        toast({
          variant: "destructive",
          title: "Fout bij ophalen projecten",
          description:
            "Er is een fout opgetreden bij het ophalen van je portfolio projecten.",
        });
      } else {
        // Transform the photos data to match our Project type
        const projectsWithPhotos = data.map((project) => ({
          ...project,
          photos: Array.isArray(project.photos)
            ? project.photos.map((photo: any) => ({
                id: photo.id?.toString() || "",
                photo_url: photo.photo_url || "",
              }))
            : [],
        }));
        setProjects(projectsWithPhotos);
      }
    } catch (error) {
      console.error("Error in fetchProjects:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const handleUpdateProject = async (
    projectId: string,
    updatedData: Partial<Project>
  ) => {
    try {
      const { error } = await supabase
        .from("portfolio_projects")
        .update({
          title: updatedData.title,
          description: updatedData.description,
          budget: updatedData.budget,
          photos: updatedData.photos,
        })
        .eq("id", projectId);

      if (error) throw error;

      toast({
        title: "Project bijgewerkt",
        description: "Je project is succesvol bijgewerkt.",
      });

      fetchProjects();
    } catch (error) {
      console.error("Error updating project:", error);
      toast({
        variant: "destructive",
        title: "Fout bij bijwerken project",
        description:
          "Er is een fout opgetreden bij het bijwerken van je project.",
      });
    }
  };

  const handleDeleteProject = (projectId: string) => {
    setProjects(projects.filter((project) => project.id !== projectId));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 sm:px-2 px-6">
      <BackToDashboard />
      <div className="flex sm:flex-row flex-col gap-4 justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-accent mb-2">
            Mijn Portfolio
          </h1>
          <p className="text-muted-foreground">
            Beheer je portfolio en laat je beste werk zien aan potentiële
            klanten.
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Nieuw Project
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Nieuw Project Toevoegen</DialogTitle>
            </DialogHeader>
            <AddProjectForm
              onSuccess={() => {
                setIsDialogOpen(false);
                fetchProjects();
              }}
            />
          </DialogContent>
        </Dialog>
      </div>

      {projects.length === 0 ? (
        <Card className="bg-muted/50">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <p className="text-muted-foreground text-center mb-4">
              Je hebt nog geen projecten in je portfolio.
              <br />
              Voeg je eerste project toe om je werk te laten zien aan potentiële
              klanten.
            </p>
            <Button onClick={() => setIsDialogOpen(true)} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Project Toevoegen
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              onUpdate={handleUpdateProject}
              onDelete={handleDeleteProject}
            />
          ))}
        </div>
      )}
    </div>
  );
};
