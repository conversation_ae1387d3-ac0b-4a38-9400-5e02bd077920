import { DateRange } from "react-day-picker";

import { DatePickerWithRange } from "@/components/ui/date-range-picker";

interface AnalyticsHeaderProps {
  dateRange: DateRange | undefined;
  onDateChange: (date: DateRange | undefined) => void;
}

export const AnalyticsHeader = ({
  dateRange,
  onDateChange,
}: AnalyticsHeaderProps) => {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
      <h1 className="text-2xl font-bold">Platform Analytics</h1>
      <DatePickerWithRange
        date={dateRange}
        onDateChange={onDateChange}
        className="w-full sm:w-auto"
      />
    </div>
  );
};
