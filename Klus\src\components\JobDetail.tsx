import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Loader2 } from "lucide-react";

import { supabase } from "@/integrations/supabase/client";
import { JobDetailContainer } from "./job/JobDetailContainer";
import { CreateJobForm } from "./CreateJobForm";
import { useToast } from "@/hooks/use-toast";
import { ROUTE_PATHS } from "@/config/routes";

export const JobDetail = () => {
	const { jobId } = useParams();
	const navigate = useNavigate();
	const [job, setJob] = useState<any>(null);
	const [loading, setLoading] = useState(true);
	const [isOwner, setIsOwner] = useState(false);
	const { toast } = useToast();

	useEffect(() => {
		const fetchJobAndCheckOwnership = async () => {
			try {
				// Don't fetch if we're on the "new" route
				if (jobId === "new") {
					setLoading(false);
					return;
				}

				// Get current user
				const {
					data: { user },
				} = await supabase.auth.getUser();
				if (!user) {
					navigate(ROUTE_PATHS.AUTH);
					return;
				}

				// Fetch job details
				const { data: jobData, error: jobError } = await supabase
					.from("jobs")
					.select(
						`
            *,
            profiles:user_id (
              first_name,
              last_name
            )
          `
					)
					.eq("id", jobId)
					.single();

				if (jobError) {
					console.error("Error fetching job:", jobError);
					toast({
						variant: "destructive",
						title: "Fout bij ophalen klus",
						description: "De klus kon niet worden gevonden.",
					});
					navigate("/banen");
					return;
				}

				if (!jobData) {
					toast({
						variant: "destructive",
						title: "Klus niet gevonden",
						description: "De opgevraagde klus bestaat niet.",
					});
					navigate("/banen");
					return;
				}

				setJob(jobData);

				// Check if current user is the job owner
				setIsOwner(jobData.user_id === user.id);
			} catch (error) {
				console.error("Error in fetchJobAndCheckOwnership:", error);
				toast({
					variant: "destructive",
					title: "Fout",
					description: "Er is een fout opgetreden bij het ophalen van de klus.",
				});
			} finally {
				setLoading(false);
			}
		};

		if (jobId) {
			fetchJobAndCheckOwnership();
		}
	}, [jobId, navigate, toast]);

	if (loading) {
		return (
			<div className="min-h-[calc(100vh-85px)] w-screen flex items-center justify-center">
				<Loader2 className="h-8 w-8 animate-spin text-primary" />
			</div>
		);
	}

	// If we're on the "new" route, show the create form
	if (jobId === "new") {
		return <CreateJobForm />;
	}

	return (
		<JobDetailContainer
			id={jobId}
			title={job?.title}
			description={job?.description}
			job_type={job?.job_type}
			location={job?.postal_code}
			date={job?.created_at}
			status={job?.status}
			house_number={job?.house_number}
			house_number_addition={job?.house_number_addition}
			photos={job?.photos}
			onBack={() => navigate("/banen")}
			isOwner={isOwner}
		/>
	);
};
