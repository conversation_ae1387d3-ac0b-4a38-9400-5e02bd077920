import {
  User,
  Star,
  Briefcase,
  Receipt,
  Building2,
  Award,
  FolderOpen,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";

interface VakmanCardProps {
  vakman: {
    id: string;
    company_name: string | null;
    email: string | null;
    profile_photo_url: string | null;
    average_rating: number | null;
    total_reviews: number;
    first_name?: string | null;
    last_name?: string | null;
    phone_number?: string | null;
    kvk_number?: string | null;
    btw_number?: string | null;
    street_address?: string | null;
    city?: string | null;
    postal_code?: string | null;
    status?: string | null;
    diplomas?: any[] | null;
    portfolioProjectCount?: number;
    qualityScore?: number;
    services?: string[] | null;
  };
  onPortfolioClick: (vakman: any) => void;
  sendRequest: (newId: string) => Promise<void>;
  selectedIds: string[];
}

export const VakmanCard = ({
  vakman,
  onPortfolioClick,
  sendRequest,
  selectedIds,
}: VakmanCardProps) => {
  // Determine if this is a high-quality profile
  const isHighQuality = (vakman.qualityScore || 0) >= 80;
  const isGoodQuality = (vakman.qualityScore || 0) >= 60;

  return (
    <Card
      className={`border-border/30 overflow-hidden hover:shadow-xl transition-all duration-300 bg-card ${
        isHighQuality
          ? "ring-2 ring-green-500/20 border-green-500/30"
          : isGoodQuality
          ? "ring-1 ring-blue-500/20 border-blue-500/30"
          : ""
      }`}
    >
      <CardContent className="p-6 space-y-6 flex flex-col h-full">
        <div className="flex-1">
          {/* Header with Avatar and Rating */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16 border-2 border-border/30">
                {vakman.profile_photo_url ? (
                  <AvatarImage
                    src={vakman.profile_photo_url}
                    alt={vakman.company_name || "Profielfoto"}
                  />
                ) : (
                  <AvatarFallback className="bg-muted">
                    <User className="h-8 w-8 text-muted-foreground" />
                  </AvatarFallback>
                )}
              </Avatar>
              <div className="flex flex-col gap-1">
                {vakman.average_rating !== null && (
                  <div className="flex items-center gap-1 bg-secondary px-2 py-1 rounded-full">
                    <Star className="h-4 w-4 fill-primary text-primary" />
                    <span className="font-medium text-primary-foreground">
                      {vakman.average_rating.toFixed(1)}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      ({vakman.total_reviews})
                    </span>
                  </div>
                )}
                {isHighQuality && (
                  <div className="flex items-center gap-1 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                    <Award className="h-3 w-3" />
                    <span className="font-medium">Top Vakman</span>
                  </div>
                )}
                {isGoodQuality && !isHighQuality && (
                  <div className="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                    <Award className="h-3 w-3" />
                    <span className="font-medium">Kwaliteit</span>
                  </div>
                )}
              </div>
            </div>
            <Badge
              variant="secondary"
              className="bg-secondary text-secondary-foreground"
            >
              Vakman
            </Badge>
          </div>

          <div className="space-y-4">
            {/* Company and Personal Info */}
            <div className="space-y-2">
              <p className="text-xl font-semibold text-foreground">
                {vakman.company_name || "Bedrijfsnaam niet ingesteld"}
              </p>
            </div>

            {/* Business Information */}
            {(vakman.kvk_number ||
              vakman.btw_number ||
              vakman.street_address ||
              (vakman.portfolioProjectCount !== undefined &&
                vakman.portfolioProjectCount > 0)) && (
              <>
                <Separator />
                <div className="space-y-2">
                  {vakman.street_address && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Building2 className="h-4 w-4 flex-shrink-0" />
                      <span>Adres: {vakman.street_address}</span>
                    </div>
                  )}
                  {vakman.kvk_number && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Briefcase className="h-4 w-4" />
                      <span>KvK: {vakman.kvk_number}</span>
                    </div>
                  )}
                  {vakman.btw_number && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Receipt className="h-4 w-4" />
                      <span>BTW: {vakman.btw_number}</span>
                    </div>
                  )}
                  {vakman.portfolioProjectCount !== undefined &&
                    vakman.portfolioProjectCount > 0 && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <FolderOpen className="h-4 w-4" />
                        <span>
                          Portfolio: {vakman.portfolioProjectCount} project
                          {vakman.portfolioProjectCount !== 1 ? "en" : ""}
                        </span>
                      </div>
                    )}
                </div>
              </>
            )}
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            className="bg-primary hover:bg-primary-hover text-primary-foreground w-1/2"
            onClick={(e) => {
              e.stopPropagation(); // Prevent card click when clicking the button
              onPortfolioClick(vakman);
            }}
          >
            Portfolio bekijken
          </Button>
          <Button
            className="bg-primary hover:bg-primary-hover text-primary-foreground w-1/2"
            onClick={() => sendRequest(vakman.id)}
            disabled={
              selectedIds.includes(vakman.id) || selectedIds.length === 10
            }
          >
            {selectedIds.includes(vakman.id)
              ? "Aanvraag verstuurd"
              : "Directe aanvraag"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
