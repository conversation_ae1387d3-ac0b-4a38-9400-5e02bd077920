
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      balance_transactions: {
        Row: {
          id: string;
          user_id: string;
          amount: number;
          type: string;
          description: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          amount: number;
          type: string;
          description?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          amount?: number;
          type?: string;
          description?: string | null;
          created_at?: string;
        };
      };
      jobs: {
        Row: {
          id: string;
          created_at: string;
          user_id: string;
          title: string;
          description: string;
          postal_code: string;
          house_number: string;
          house_number_addition: string | null;
          category: string;
          status: string | null;
          budget: number | null;
          response_cost: number;
          deleted_at: string | null;
          photos: Json | null;
          details: Json | null;
        };
        Insert: {
          id?: string;
          created_at?: string;
          user_id: string;
          title: string;
          description: string;
          postal_code: string;
          house_number: string;
          house_number_addition?: string | null;
          category: string;
          status?: string | null;
          budget?: number | null;
          response_cost?: number;
          deleted_at?: string | null;
          photos?: Json | null;
          details?: Json | null;
        };
        Update: {
          id?: string;
          created_at?: string;
          user_id?: string;
          title?: string;
          description?: string;
          postal_code?: string;
          house_number?: string;
          house_number_addition?: string | null;
          category?: string;
          status?: string | null;
          budget?: number | null;
          response_cost?: number;
          deleted_at?: string | null;
          photos?: Json | null;
          details?: Json | null;
        };
      };
      referrals: {
        Row: {
          id: string;
          referrer_id: string;
          referred_email: string;
          referred_user_id: string | null;
          created_at: string;
          completed_at: string | null;
          status: string;
          bonus_paid: boolean;
        };
        Insert: {
          id?: string;
          referrer_id: string;
          referred_email: string;
          referred_user_id?: string | null;
          created_at?: string;
          completed_at?: string | null;
          status?: string;
          bonus_paid?: boolean;
        };
        Update: {
          id?: string;
          referrer_id?: string;
          referred_email?: string;
          referred_user_id?: string | null;
          created_at?: string;
          completed_at?: string | null;
          status?: string;
          bonus_paid?: boolean;
        };
      };
      [key: string]: any;
    };
    Views: {
      [key: string]: any;
    };
    Functions: {
      [key: string]: any;
    };
    Enums: {
      [key: string]: any;
    };
  };
}

export type * from './json';
export type * from './tables/balance';
export type * from './tables/jobs';
export type * from './tables/messages';
export type * from './tables/profiles';
export type * from './tables/referrals';
