import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "./use-toast";

export const useUserProfile = () => {
  const { toast } = useToast();

  return useQuery({
    queryKey: ["userProfile"],
    queryFn: async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return null;
      }

      const { data: profile, error } = await supabase
        .from("profiles")
        .select("user_type, balance")
        .eq("id", user.id)
        .maybeSingle();

      if (error) {
        console.error("Error fetching profile:", error);
        toast({
          variant: "destructive",
          title: "Fout bij ophalen profiel",
          description:
            "Er is een probleem opgetreden bij het ophalen van je profiel.",
        });
        throw error;
      }

      return profile;
    },
    staleTime: 30000, // Cache voor 30 seconden
    retry: 1,
  });
};
