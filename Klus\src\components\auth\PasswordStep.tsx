import { useState, useCallback, useEffect } from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { ChevronLeft } from "lucide-react";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { ViewType } from "@/types/auth";

interface PasswordStepProps {
  register: UseFormRegister<any>;
  errors: FieldErrors;
  email?: string;
  onBack: () => void;
  view: ViewType;
}

export const PasswordStep = ({
  register,
  errors,
  email,
  onBack,
  view,
}: PasswordStepProps) => {
  const { toast } = useToast();

  const [isResetting, setIsResetting] = useState(false);
  const [resetTimeout, setResetTimeout] = useState<number>(0);

  useEffect(() => {
    return () => {
      if (resetTimeout) {
        clearTimeout(resetTimeout);
      }
    };
  }, [resetTimeout]);

  const RESET_COOLDOWN = 60000; // 60 seconds in milliseconds

  const handleResetPassword = useCallback(async () => {
    if (isResetting) {
      toast({
        variant: "destructive",
        title: "Even geduld",
        description: `Je kunt over ${Math.ceil(
          resetTimeout / 1000
        )} seconden opnieuw een reset aanvragen.`,
      });
      return;
    }

    setIsResetting(true);

    try {
      const { error } = await supabase.functions.invoke("send-password-reset", {
        body: { email },
      });

      if (error) {
        const errorMessages: Record<
          string,
          { title: string; description: string }
        > = {
          "rate limit exceeded": {
            title: "Even geduld",
            description:
              "Je hebt recent al een reset link aangevraagd. Check je email of wacht een minuut voordat je het opnieuw probeert.",
          },
          "User not found": {
            title: "Gebruiker niet gevonden",
            description: "Er bestaat geen account met dit emailadres.",
          },
        };

        const knownError = Object.entries(errorMessages).find(([key]) =>
          error.message.includes(key)
        );

        if (knownError) {
          toast({
            variant: "destructive",
            ...knownError[1],
          });
          return;
        }

        throw error;
      }

      toast({
        title: "Reset link verstuurd",
        description:
          "Check je email voor instructies om je wachtwoord te resetten.",
      });

      const timeout = window.setTimeout(() => {
        setIsResetting(false);
        setResetTimeout(0);
      }, RESET_COOLDOWN);

      setResetTimeout(RESET_COOLDOWN);

      const countdownInterval = setInterval(() => {
        setResetTimeout((prev) => {
          if (prev <= 1000) {
            clearInterval(countdownInterval);
            return 0;
          }
          return prev - 1000;
        });
      }, 1000);

      return () => {
        clearTimeout(timeout);
        clearInterval(countdownInterval);
      };
    } catch (error) {
      console.error("Password reset error:", error);
      toast({
        variant: "destructive",
        title: "Fout",
        description:
          "Er is een fout opgetreden bij het versturen van de reset link.",
      });
      setIsResetting(false);
      setResetTimeout(0);
    }
  }, [email, isResetting, resetTimeout, toast]);

  return (
    <div className="space-y-4">
      {view !== "reset" && (
        <div className="p-3 bg-gray-100 rounded-md flex items-center gap-2">
          <button
            type="button"
            onClick={onBack}
            className="text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          <p className="text-sm text-gray-600">
            <span className="font-medium">{email}</span>
          </p>
        </div>
      )}
      <div className="space-y-2">
        <Label htmlFor="password">Wachtwoord</Label>
        <Input
          id="password"
          type="password"
          {...register("password", {
            required: "Wachtwoord is verplicht",
          })}
          className={errors.password ? "border-red-500" : ""}
        />
        {errors.password && (
          <p className="text-sm text-red-500">
            {errors.password.message as string}
          </p>
        )}

        {(view === "sign_up" || view === "reset") && (
          <div className="mt-4 space-y-2">
            <Label htmlFor="confirmPassword">Bevestig wachtwoord</Label>
            <Input
              id="confirmPassword"
              type="password"
              {...register("confirmPassword", {
                required: "Bevestig je wachtwoord",
              })}
              className={errors.confirmPassword ? "border-red-500" : ""}
            />
            {errors.confirmPassword && (
              <p className="text-sm text-red-500">
                {errors.confirmPassword.message as string}
              </p>
            )}
          </div>
        )}

        {view === "sign_in" && (
          <button
            type="button"
            onClick={handleResetPassword}
            disabled={isResetting}
            className={`text-sm text-gray-600 hover:text-gray-900 transition-colors ${
              isResetting ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {isResetting
              ? `Wacht ${Math.ceil(resetTimeout / 1000)} seconden...`
              : "Wachtwoord vergeten?"}
          </button>
        )}
      </div>
    </div>
  );
};
