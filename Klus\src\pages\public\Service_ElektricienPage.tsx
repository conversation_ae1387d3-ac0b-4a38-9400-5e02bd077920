/**
 * @description This component renders a comprehensive and SEO-optimized detail page for electrician services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for electricians. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Zap,
  ShieldCheck,
  Clock,
  ArrowRight,
  Lightbulb,
  Star,
  Edit3,
  MessageSquare,
  <PERSON>r<PERSON><PERSON><PERSON>,
  CheckCircle,
  MapPin,
  Euro,
  Server,
  Plug,
} from "lucide-react";

const Service_ElektricienPage = () => {
  usePageTitle(
    "Elektricien Nodig? | Klusgebied - Storing, Groepenkast & Verlichting"
  );
  const navigate = useNavigate();

  const electricianServices = [
    {
      icon: Zap,
      title: "Storing Verhelpen",
      description:
        "24/7 storingsdienst voor het snel oplossen van stroomstoringen.",
      points: [
        "Directe hulp bij kortsluiting en stroomuitval.",
        "Opsporen van de oorzaak met professionele meetapparatuur.",
        "Veilige en duurzame reparaties.",
        "24/7 beschikbaar voor noodgevallen.",
      ],
    },
    {
      icon: Server,
      title: "Groepenkast Vervangen",
      description:
        "Vervangen of uitbreiden van uw groepenkast voor meer capaciteit en veiligheid.",
      points: [
        "Installatie van moderne automatenkasten.",
        "Voldoet aan de NEN 1010 veiligheidsnorm.",
        "Extra groepen voor keuken, laadpaal of zonnepanelen.",
        "Verhoogt de veiligheid en voorkomt overbelasting.",
      ],
    },
    {
      icon: Lightbulb,
      title: "Verlichting Installeren",
      description:
        "Aanleggen van binnen- en buitenverlichting, inclusief slimme verlichting.",
      points: [
        "Ontwerp en installatie van complete lichtplannen.",
        "Energiezuinige LED-verlichting en inbouwspots.",
        "Installatie van slimme verlichting (Philips Hue, etc.).",
        "Veilige aanleg van tuin- en buitenverlichting.",
      ],
    },
    {
      icon: Plug,
      title: "Stopcontacten Aanleggen",
      description:
        "Plaatsen van extra stopcontacten en verleggen van bestaande aansluitingen.",
      points: [
        "Extra stopcontacten in elke gewenste ruimte.",
        "Verleggen van lichtpunten en schakelaars.",
        "Aanleg van speciale aansluitingen (bv. Perilex).",
        "Nette afwerking zonder zichtbare kabels.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "24/7 Storingsdienst",
      description:
        "Dag en nacht bereikbaar voor noodgevallen en stroomstoringen.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Veilige Installaties",
      description:
        "Al onze elektriciens zijn gecertificeerd en werken volgens de NEN 1010 norm.",
    },
    {
      icon: <Lightbulb className="w-8 h-8 text-white" />,
      title: "Advies op Maat",
      description:
        "Wij geven advies over energiebesparing en slimme oplossingen.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een elektricien per uur?",
      answer:
        "Het uurtarief van een elektricien ligt gemiddeld tussen de €50 en €80, afhankelijk van de complexiteit van de klus. Voor spoedklussen kan een hoger tarief gelden.",
    },
    {
      question: "Mijn stoppen zijn gesprongen, wat nu?",
      answer:
        "Probeer eerst te achterhalen welk apparaat de kortsluiting veroorzaakt. Lukt dit niet, of springt de stop er direct weer uit, bel dan onze storingsdienst.",
    },
    {
      question: "Is mijn groepenkast nog wel veilig?",
      answer:
        "Als uw groepenkast ouder is dan 20 jaar of nog een oude stoppenkast is, is het verstandig deze te laten controleren en eventueel te vervangen voor een moderne automatenkast met aardlekschakelaars.",
    },
    {
      question: "Kan ik zelf stopcontacten aanleggen?",
      answer:
        "Werken met elektriciteit kan gevaarlijk zijn. Wij raden sterk aan om dit door een erkende elektricien te laten doen om risico's op kortsluiting en brand te voorkomen.",
    },
  ];

  const reviews = [
    {
      name: "Mark de Groot",
      location: "Amsterdam",
      rating: 5,
      quote:
        "De elektricien was er binnen een uur voor een stroomstoring. Super snel en vakkundig opgelost. Een echte aanrader!",
      highlighted: false,
    },
    {
      name: "Chantal Janssen",
      location: "Utrecht",
      rating: 5,
      quote:
        "Mijn nieuwe groepenkast is perfect geïnstalleerd. De vakman dacht goed mee en werkte heel netjes. Zeer tevreden.",
      highlighted: true,
    },
    {
      name: "Familie Yilmaz",
      location: "Rotterdam",
      rating: 5,
      quote:
        "Goed en betrouwbaar. De verlichting in de tuin is prachtig aangelegd en de prijs was eerlijk. Volgende keer weer via Klusgebied.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1562669151-4d4f430a4cfb?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxlbGVjdHJpY2lhbiUyQyUyMHdpcmluZ3xlbnwwfHx8fDE3NTIxMDg1MTh8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1595831708961-1b13c0dd2422?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwyfHxlbGVjdHJpY2lhbiUyQyUyMHdpcmluZ3xlbnwwfHx8fDE3NTIxMDg1MTh8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1597502310092-31cdaa35b46d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwzfHxlbGVjdHJpY2lhbiUyQyUyMHdpcmluZ3xlbnwwfHx8fDE3NTIxMDg1MTh8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description:
        "Beschrijf je klus in detail. Voeg foto's toe voor een duidelijker beeld.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Vakmannen reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde elektriciens.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & start de klus",
      description:
        "Vergelijk profielen en kies de beste vakman voor jouw klus. Plan en start!",
      microcopy: "Vergelijk profielen en beoordelingen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = React.useState(0);

  React.useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Elektriciens in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "elektricien",
    color: "yellow",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Elektricien Nodig? | Klusgebied - Storing, Groepenkast & Verlichting
        </title>
        <meta
          name="description"
          content="Vind snel een betrouwbare elektricien voor storingen, groepenkasten, verlichting en alle elektra. Plaats je klus gratis en ontvang offertes van geverifieerde vakmannen in jouw regio."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Elektricien",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.8",
              reviewCount: "291",
            },
            review: reviews.map((review) => ({
              "@type": "Review",
              author: { "@type": "Person", name: review.name },
              reviewRating: {
                "@type": "Rating",
                ratingValue: review.rating,
                bestRating: "5",
              },
              reviewBody: review.quote,
            })),
            offers: {
              "@type": "Offer",
              priceCurrency: "EUR",
              priceSpecification: {
                "@type": "PriceSpecification",
                priceCurrency: "EUR",
                minPrice: "50",
                maxPrice: "80",
                valueAddedTaxIncluded: true,
                unitText: "HOUR",
                description:
                  "Standaardtarief per uur. Spoedtoeslag kan van toepassing zijn.",
              },
            },
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            mainEntity: faqs.map((faq) => ({
              "@type": "Question",
              name: faq.question,
              acceptedAnswer: {
                "@type": "Answer",
                text: faq.answer,
              },
            })),
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-teal-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-yellow-100 border border-yellow-200/80 rounded-full px-4 py-2 mb-6">
                    <Zap className="w-5 h-5 text-yellow-600" />
                    <span className="text-yellow-800 font-semibold text-sm">
                      Elektricien Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Elektricien nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-500 to-orange-500 mt-2">
                      Snel & Vakkundig
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor alle elektra, van groepenkast tot verlichting. Vind
                    snel een geverifieerde elektricien bij u in de buurt.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate("/plaats-een-klus/elektra-klussen")
                      }
                      className="group inline-flex items-center justify-center bg-yellow-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-yellow-600 transition-all duration-300 shadow-lg hover:shadow-yellow-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw elektricien
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.8/5</span>
                    <span>gebaseerd op 291 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1562669151-4d4f430a4cfb?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxlbGVjdHJpY2lhbiUyQyUyMHdpcmluZ3xlbnwwfHx8fDE3NTIxMDg1MTh8MA&ixlib=rb-4.1.0?w=1024&h=1024"
                    alt="Professionele elektricien aan het werk"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte elektricien voor jouw
                klus.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-yellow-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Elektra Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Van een simpele storing tot een complete nieuwe installatie.
                Lees meer over onze expertise.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {electricianServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-yellow-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-yellow-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-yellow-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een elektricien via Klusgebied?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                Bij Klusgebied betaal je een eerlijke prijs voor erkende
                vakmannen. Je ontvangt altijd eerst een prijsvoorstel voordat de
                klus start.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-yellow-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Standaardtarief:{" "}
                    <strong className="text-slate-900">€50–€80</strong> per uur
                    (incl. BTW)
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-yellow-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Spoedtoeslag:{" "}
                    <strong className="text-slate-900">€25–€50</strong> extra,
                    afhankelijk van tijdstip
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-yellow-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Voorrijkosten: Vaak inbegrepen bij directe beschikbaarheid
                  </span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Geen verborgen kosten. Altijd vooraf een prijsafspraak.
              </div>
              <button
                onClick={() => navigate("/plaats-een-klus/elektra-klussen")}
                className="bg-yellow-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-yellow-600 transition-all duration-300 shadow-lg hover:shadow-yellow-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een elektricien vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-yellow-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-yellow-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-yellow-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-yellow-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center mt-12">
              <a
                href="#"
                className="text-yellow-600 font-semibold hover:underline"
              >
                Bekijk alle beoordelingen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                Waarom kiezen voor Klusgebied?
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Gecertificeerde elektriciens die staan voor veiligheid en
                vakmanschap.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-yellow-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/over-ons")}
                className="text-yellow-300 font-semibold hover:text-white transition-colors"
              >
                Bekijk waarom vakmannen en klanten voor ons kiezen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </button>
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-yellow-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-yellow-500 to-orange-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Storing of een andere elektra klus?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Plaats uw klus en ontvang snel reacties van de beste elektriciens
              bij u in de buurt.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/elektra-klussen")}
              className="bg-white text-yellow-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je elektra klus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/elektra-klussen")}
          className="w-full group inline-flex items-center justify-center bg-yellow-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-yellow-600 transition-all duration-300 shadow-lg hover:shadow-yellow-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_ElektricienPage;
