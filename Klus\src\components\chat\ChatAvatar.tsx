import { cn } from "@/lib/utils";

interface ChatAvatarProps {
  profilePhotoUrl?: string | null;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export const ChatAvatar = ({
  profilePhotoUrl,
  size = "md",
  className,
}: ChatAvatarProps) => {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-10 h-10",
    lg: "w-12 h-12",
  };

  return (
    <img
      src={profilePhotoUrl ?? "/default_avatar.webp"}
      alt="Profile"
      className={cn(
        sizeClasses[size],
        "rounded-full object-cover bg-white",
        className
      )}
    />
  );
};
