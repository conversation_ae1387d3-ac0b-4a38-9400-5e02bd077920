import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const config = {
  // Server
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',

  // Supabase
  supabase: {
    url: process.env.SUPABASE_URL!,
    anonKey: process.env.SUPABASE_ANON_KEY!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  },

  // Mollie
  mollie: {
    apiKey: process.env.MOLLIE_API_KEY!,
    testApiKey: process.env.MOLLIE_TEST_API_KEY!,
  },

  // Email (Resend)
  resend: {
    apiKey: process.env.RESEND_API_KEY!,
  },

  // SMS (MessageBird)
  messageBird: {
    workspaceId: process.env.MESSAGEBIRD_WORKSPACE_ID!,
    channelId: process.env.MESSAGEBIRD_CHANNEL_ID!,
    accessKey: process.env.MESSAGEBIRD_ACCESS_KEY!,
  },

  // reCAPTCHA
  recaptcha: {
    secretKey: process.env.RECAPTCHA_SECRET_KEY!,
  },

  // HubSpot
  hubspot: {
    apiKey: process.env.HUBSPOT_API_KEY!,
  },

  // Google Maps
  googleMaps: {
    apiKey: process.env.GOOGLE_MAPS_API_KEY!,
  },
};

// Validate required environment variables
const requiredEnvVars = [
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}
