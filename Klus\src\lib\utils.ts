import { supabase } from "@/integrations/supabase/client";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const formatDutchPhoneNumber = (phoneNumber: string): string | null => {
  // Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, "");

  // Check if it's a Dutch mobile number
  if (cleaned.startsWith("31")) {
    cleaned = cleaned;
  } else if (cleaned.startsWith("0")) {
    cleaned = "31" + cleaned.substring(1);
  } else if (cleaned.length === 9) {
    cleaned = "31" + cleaned;
  }

  // Validate the final format
  const phoneRegex = /^31[6][0-9]{8}$/;
  if (!phoneRegex.test(cleaned)) {
    return null;
  }

  return "+" + cleaned;
};

export const generateSecurePassword = () => {
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const numbers = "0123456789";
  const symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";

  // Ensure at least one character from each category
  const password = [
    lowercase[Math.floor(Math.random() * lowercase.length)],
    uppercase[Math.floor(Math.random() * uppercase.length)],
    numbers[Math.floor(Math.random() * numbers.length)],
    symbols[Math.floor(Math.random() * symbols.length)],
  ];

  // Add additional random characters to meet minimum length
  const allChars = lowercase + uppercase + numbers + symbols;
  while (password.length < 12) {
    // Using 12 as a secure default length
    password.push(allChars[Math.floor(Math.random() * allChars.length)]);
  }

  // Shuffle the password array
  return password.sort(() => Math.random() - 0.5).join("");
};

export const sendNotificationEmail = async ({
  to,
  subject,
  content,
}: {
  to: string[];
  subject: string;
  content: string;
}) => {
  try {
    const { backendApi } = await import("./backendApi");
    await backendApi.sendEmail({ to, subject, html: content });
  } catch (error) {
    console.error(`Failed to send ${subject} notification:`, error);
    // Don't throw as this is non-critical
  }
};
