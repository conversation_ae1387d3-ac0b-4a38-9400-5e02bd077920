import { Briefcase, Receipt } from "lucide-react";

import { useAuth } from "@/components/auth/hooks/useAuth";

interface BusinessInformationProps {
  id: string;
  kvkNumber?: string | null;
  btwNumber?: string | null;
  isAccepted?: boolean;
}

export const BusinessInformation = ({
  id,
  kvkNumber,
  btwNumber,
  isAccepted = false,
}: BusinessInformationProps) => {
  const { userProfile } = useAuth();

  if (!kvkNumber && !btwNumber) return null;

  return (
    <div className="space-y-2">
      <h3 className="font-semibold text-lg">Bedrijfsinformatie</h3>
      <div
        className={`space-y-1 transition-all duration-200 ${
          !isAccepted && userProfile?.id !== id ? "blur-sm select-none" : ""
        }`}
      >
        {kvkNumber && (
          <p className="flex items-center gap-2 text-muted-foreground">
            <Briefcase className="h-4 w-4" />
            KvK: {kvkNumber}
          </p>
        )}
        {btwNumber && (
          <p className="flex items-center gap-2 text-muted-foreground">
            <Receipt className="h-4 w-4" />
            BTW: {btwNumber}
          </p>
        )}
      </div>
      {!isAccepted && (
        <p className="text-sm text-muted-foreground italic">
          Bedrijfsinformatie wordt zichtbaar na acceptatie
        </p>
      )}
    </div>
  );
};
