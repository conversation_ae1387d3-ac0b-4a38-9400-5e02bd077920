import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY");
const SITE_URL = Deno.env.get("SITE_URL");

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Password reset request received");
    const { email } = await req.json();

    if (!email) {
      console.error("No email provided");
      return new Response(JSON.stringify({ error: "Email is required" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    console.log("Generating reset password link for:", email);

    const { data, error: resetError } = await supabase.auth.admin.generateLink({
      type: "recovery",
      email: email,
      options: {
        redirectTo: `${SITE_URL}/opnieuw_instellen`,
      },
    });

    if (resetError) {
      console.error("Error generating reset link:", resetError);
      throw resetError;
    }

    if (!data?.properties?.action_link) {
      throw new Error("No reset link generated");
    }

    // Send email with Resend
    console.log("Sending reset password email via Resend");
    const res = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${RESEND_API_KEY}`,
      },
      body: JSON.stringify({
        from: "KlusVrij <<EMAIL>>",
        to: [email],
        subject: "Reset je wachtwoord",
        html: `
          <h2>Wachtwoord resetten</h2>
          <p>Je hebt een verzoek gedaan om je wachtwoord te resetten.</p>
          <p>Klik op onderstaande link om een nieuw wachtwoord in te stellen:</p>
          <p><a href="${data.properties.action_link}">Reset wachtwoord</a></p>
          <p>Als je dit verzoek niet hebt gedaan, kun je deze e-mail negeren.</p>
          <p>De link is 24 uur geldig.</p>
        `,
      }),
    });

    if (!res.ok) {
      const error = await res.text();
      console.error("Error sending reset email:", error);
      throw new Error("Error sending reset email");
    }

    console.log("Password reset email sent successfully");
    return new Response(
      JSON.stringify({ message: "Password reset email sent successfully" }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Error in password reset function:", error);
    return new Response(
      JSON.stringify({ error: "Error sending recovery email" }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
