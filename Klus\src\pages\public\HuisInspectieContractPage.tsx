/**
 * @description This component renders a dedicated landing page for the Huis (House) Inspection Contract. It provides detailed information about three selectable contract tiers, allowing users to compare benefits, features, and pricing dynamically. The page is designed with a world-class, conversion-focused layout, is fully responsive, and displays all contract options side-by-side on desktop for easy comparison. Key variables include contractsData for storing contract details and navigation handlers.
 */
import React from "react";
import { useNavigate } from "react-router-dom";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
  ArrowLeft,
  Search,
  Check,
  X,
  Star,
  ShieldCheck,
  TrendingUp,
  Wrench,
} from "lucide-react";

const contractsData = [
  {
    name: "Jaarlijkse Inspectie",
    description:
      "Een grondige jaarlijkse inspectie van uw woning op cruciale punten om de conditie vast te stellen en onverwachte problemen te voorkomen.",
    price: "249",
    billing: "/inspectie",
    included: [
      "Visuele inspectie van dak, geve<PERSON>, kozi<PERSON><PERSON>",
      "Controle op vocht en lekkages",
      "Inspectie van hang- en sluitwerk",
      "Gedetailleerd inspectierapport met foto's",
    ],
    excluded: [
      "Thermografische analyse",
      "Uitvoeren van reparaties",
      "Onderhoudsadvies op lange termijn",
    ],
    isMostChosen: false,
    themeColor: "bg-purple-500 hover:bg-purple-600",
  },
  {
    name: "Inspectie Plus",
    description:
      "De meest gekozen optie met een diepgaande analyse inclusief thermografie en een concreet onderhoudsplan voor de komende jaren.",
    price: "399",
    billing: "/inspectie",
    included: [
      "Alle voordelen van Jaarlijkse Inspectie",
      "Thermografische scan voor warmtelekken",
      "Vochtmetingen op kritieke punten",
      "Gedetailleerd onderhoudsadvies voor 5 jaar",
      "Prioriteit bij het plannen van vervolgklussen",
    ],
    excluded: [
      "Kosten voor reparaties en onderhoud",
      "Coördinatie van werkzaamheden",
    ],
    isMostChosen: true,
    themeColor: "bg-teal-500 hover:bg-teal-600",
  },
  {
    name: "Totaal Woningbeheer",
    description:
      "Volledige ontzorging van uw woningonderhoud. Wij inspecteren, adviseren en coördineren alle benodigde werkzaamheden.",
    price: "79",
    billing: "/maand",
    included: [
      "Alle voordelen van Inspectie Plus",
      "Jaarlijkse inspectie inbegrepen",
      "Coördinatie van alle geadviseerde reparaties",
      "Offertes opvragen en vergelijken",
      "Kwaliteitscontrole op uitgevoerd werk",
    ],
    excluded: ["Uitvoeringskosten van de klussen zelf"],
    isMostChosen: false,
    themeColor: "bg-purple-500 hover:bg-purple-600",
  },
];

const HuisInspectieContractPage = () => {
  usePageTitle("Contract Periodieke Huisinspectie | Klusgebied");
  const navigate = useNavigate();

  const comparisonData = [
    {
      feature: "Frequentie",
      comfort: "1x per jaar",
      zeker: "1x per jaar",
      allin: "1x per jaar + beheer",
    },
    {
      feature: "Inspectiepunten",
      comfort: "Visueel (dak, gevels, etc.)",
      zeker: "Visueel + Thermografie",
      allin: "Visueel + Thermografie",
    },
    {
      feature: "Rapportage",
      comfort: { icon: "Check" },
      zeker: { icon: "Check", text: "uitgebreid" },
      allin: { icon: "Check", text: "uitgebreid" },
    },
    {
      feature: "Onderhoudsadvies",
      comfort: { icon: "X" },
      zeker: { icon: "Check", text: "voor 5 jaar" },
      allin: { icon: "Check", text: "doorlopend" },
    },
    {
      feature: "Coördinatie werkzaamheden",
      comfort: { icon: "X" },
      zeker: { icon: "X" },
      allin: { icon: "Check" },
    },
    {
      feature: "Prijs",
      comfort: "€249 / inspectie",
      zeker: "€399 / inspectie",
      allin: "€79 / maand",
    },
  ];

  const renderCell = (data) => {
    if (typeof data === "string") {
      return <span className="text-slate-700">{data}</span>;
    }
    if (typeof data === "object" && data.icon) {
      return (
        <div className="flex items-center justify-center">
          {data.icon === "Check" ? (
            <Check className="w-6 h-6 text-teal-500" />
          ) : (
            <X className="w-6 h-6 text-red-400" />
          )}
          {data.text && (
            <span className="ml-2 text-slate-600 text-sm">{data.text}</span>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="min-h-screen bg-slate-50">
      <main className="pt-20">
        {/* Hero Section */}
        <section className="relative py-20 lg:py-24 bg-slate-800 text-white overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.1.0&w=1920&h=1080&fit=crop"
              alt="Huis Inspectie"
              className="w-full h-full object-cover opacity-20"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-slate-900 to-slate-800/50"></div>
          </div>
          <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <button
              onClick={() => navigate("/")}
              className="flex items-center space-x-2 text-white/80 hover:text-white mb-8 transition-all duration-300 hover:scale-105 mx-auto"
            >
              <ArrowLeft className="w-5 h-5" />
              <span className="font-medium">Terug naar home</span>
            </button>
            <Search className="w-16 h-16 text-purple-400 mx-auto mb-4" />
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
              Periodieke Huisinspectie
            </h1>
            <p className="text-lg md:text-xl lg:text-2xl text-slate-300 leading-relaxed">
              Voorkom verrassingen en houd uw woning in topconditie met onze
              inspectiediensten.
            </p>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-16 lg:py-24">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
              {contractsData.map((contract, index) => (
                <div
                  key={contract.name}
                  className={`relative bg-white rounded-3xl shadow-lg transition-all duration-500 h-full flex flex-col group motion-preset-slide-up motion-delay-${
                    index * 100
                  } ${
                    contract.isMostChosen
                      ? "border-4 border-teal-500 scale-100 lg:scale-105 shadow-2xl shadow-teal-500/30 z-10"
                      : "border-2 border-slate-100 hover:shadow-2xl hover:scale-102"
                  }`}
                >
                  {contract.isMostChosen && (
                    <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-teal-500 text-white px-4 py-1 rounded-full text-sm font-bold inline-flex items-center space-x-2 shadow-lg">
                      <Star className="w-4 h-4" />
                      <span>Meest Gekozen</span>
                    </div>
                  )}
                  <div className="p-8 flex flex-col flex-grow">
                    <div className="text-center mb-6">
                      <h2 className="text-2xl font-bold text-slate-800 mb-2">
                        {contract.name}
                      </h2>
                      <p className="text-slate-600 text-sm min-h-[6rem]">
                        {contract.description}
                      </p>
                    </div>

                    <div className="text-center my-4">
                      <div className="inline-block bg-purple-100/50 rounded-full p-4">
                        <Search className="w-12 h-12 text-purple-500" />
                      </div>
                    </div>

                    <div className="text-center text-4xl font-bold text-slate-900 mb-2">
                      €{contract.price}
                      <span className="text-lg font-normal text-slate-500">
                        {contract.billing}
                      </span>
                    </div>
                    <p className="text-center text-xs text-slate-400 mb-8">
                      Inclusief BTW.
                    </p>

                    <div className="mt-auto mb-8 pt-6 border-t border-slate-200 flex-grow">
                      <h3 className="text-base font-semibold text-center mb-4 text-slate-700">
                        Wat is inbegrepen?
                      </h3>
                      <ul className="space-y-3">
                        {contract.included.map((item) => (
                          <li key={item} className="flex items-start text-sm">
                            <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-slate-600">{item}</span>
                          </li>
                        ))}
                      </ul>
                      {contract.excluded.length > 0 && (
                        <>
                          <h3 className="text-base font-semibold text-center mb-4 mt-6 text-slate-700">
                            Wat is niet inbegrepen?
                          </h3>
                          <ul className="space-y-3">
                            {contract.excluded.map((item) => (
                              <li
                                key={item}
                                className="flex items-start text-sm"
                              >
                                <X className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
                                <span className="text-slate-500 opacity-90">
                                  {item}
                                </span>
                              </li>
                            ))}
                          </ul>
                        </>
                      )}
                    </div>

                    <button
                      className={`w-full text-white font-bold py-3 rounded-xl text-lg transition-all duration-300 shadow-lg hover:scale-105 ${contract.themeColor}`}
                    >
                      Kies {contract.name.split(" ").shift()}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
                Waarom is een huisinspectie belangrijk?
              </h2>
              <p className="text-lg text-slate-600 leading-relaxed">
                Een periodieke huisinspectie is als een gezondheidscheck voor uw
                woning. Het helpt verborgen gebreken vroegtijdig op te sporen,
                voorkomt onverwachte hoge kosten en geeft u inzicht in het
                toekomstige onderhoud. Zo behoudt uw woning zijn waarde en woont
                u met een gerust hart.
              </p>
            </div>
            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="p-6">
                <ShieldCheck className="w-12 h-12 text-teal-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-slate-800 mb-2">
                  Voorkom Verrassingen
                </h3>
                <p className="text-slate-600">
                  Identificeer problemen voordat ze groot en kostbaar worden.
                </p>
              </div>
              <div className="p-6">
                <TrendingUp className="w-12 h-12 text-teal-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-slate-800 mb-2">
                  Waardebehoud
                </h3>
                <p className="text-slate-600">
                  Houd uw woning in topconditie en behoud de marktwaarde.
                </p>
              </div>
              <div className="p-6">
                <Wrench className="w-12 h-12 text-teal-500 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-slate-800 mb-2">
                  Onderhoudsplanning
                </h3>
                <p className="text-slate-600">
                  Krijg een duidelijk overzicht van het benodigde onderhoud voor
                  de komende jaren.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
                Vergelijk onze Inspectie Pakketten
              </h2>
              <p className="text-lg text-slate-600 max-w-3xl mx-auto">
                Vind het inspectiecontract dat perfect aansluit bij uw wensen en
                woning.
              </p>
            </div>
            <div className="overflow-x-auto bg-white rounded-2xl shadow-xl border border-slate-200/80">
              <table className="w-full min-w-[1000px] text-left border-collapse">
                <thead>
                  <tr className="border-b border-slate-200">
                    <th className="p-6 bg-slate-50 text-lg font-bold text-slate-700 rounded-tl-2xl">
                      Functie
                    </th>
                    <th className="p-6 bg-slate-50 text-lg font-bold text-slate-700 text-center">
                      Jaarlijkse Inspectie
                    </th>
                    <th className="p-6 bg-teal-500/10 text-lg font-bold text-teal-600 text-center border-x-2 border-teal-500">
                      Inspectie Plus
                    </th>
                    <th className="p-6 bg-slate-50 text-lg font-bold text-slate-700 text-center rounded-tr-2xl">
                      Totaal Woningbeheer
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {comparisonData.map((row, index) => (
                    <tr
                      key={index}
                      className="border-b border-slate-200 last:border-b-0 hover:bg-slate-50/50 transition-colors duration-200"
                    >
                      <td className="p-5 font-semibold text-slate-800">
                        {row.feature}
                      </td>
                      <td className="p-5 text-center">
                        {renderCell(row.comfort)}
                      </td>
                      <td className="p-5 text-center bg-teal-500/5 border-x-2 border-teal-500">
                        {renderCell(row.zeker)}
                      </td>
                      <td className="p-5 text-center">
                        {renderCell(row.allin)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default HuisInspectieContractPage;
