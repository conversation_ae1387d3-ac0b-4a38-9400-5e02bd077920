import { useEffect, useState } from "react";
import { Star, Loader2 } from "lucide-react";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { JobReview } from "@/components/job/JobReview";
import { BackToDashboard } from "@/components/BackToDashboard";

interface Review {
  id: string;
  rating: number;
  comment: string | null;
  created_at: string;
  feedback?: string;
  job: {
    title: string;
  } | null;
  reviewer: {
    first_name: string | null;
    last_name: string | null;
  } | null;
  vakman_id: string;
}

const Reviews = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [averageRating, setAverageRating] = useState<number | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchReviews = async () => {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) return;

        const { data: reviewsData, error } = await supabase
          .from("vakman_reviews")
          .select(
            `
            *,
            job:jobs(title),
            reviewer:profiles!vakman_reviews_reviewer_id_fkey(
              first_name,
              last_name
            )
          `
          )
          .eq("vakman_id", user.id)
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error fetching reviews:", error);
          toast({
            variant: "destructive",
            title: "Fout bij ophalen beoordelingen",
            description:
              "Er is een fout opgetreden bij het ophalen van je beoordelingen.",
          });
        } else if (reviewsData) {
          setReviews(reviewsData);

          // Calculate average rating
          const totalRating = reviewsData.reduce(
            (sum, review) => sum + review.rating,
            0
          );
          const avgRating =
            reviewsData.length > 0 ? totalRating / reviewsData.length : null;
          setAverageRating(avgRating);
        }
      } catch (error) {
        console.error("Error in fetchReviews:", error);
        toast({
          variant: "destructive",
          title: "Fout",
          description: "Er is een onverwachte fout opgetreden.",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, [toast]);

  if (loading) {
    return (
      <div className="container mx-auto p-6 max-w-3xl flex justify-center items-center h-[calc(100vh-100px)]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-3xl">
      <BackToDashboard />
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <Star className="h-6 w-6 text-primary" />
          <h1 className="text-3xl font-bold text-accent">Mijn Beoordelingen</h1>
          {averageRating !== null && (
            <div className="flex items-center gap-2 bg-muted rounded-full px-4 py-1">
              <div className="flex items-center">
                {[...Array(5)].map((_, index) => (
                  <Star
                    key={index}
                    className={`h-4 w-4 ${
                      index < Math.round(averageRating)
                        ? "fill-yellow-400 text-yellow-400"
                        : "fill-gray-200 text-gray-200"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm font-medium text-muted-foreground">
                {averageRating.toFixed(1)}
              </span>
            </div>
          )}
        </div>
        <p className="text-muted-foreground">
          Hier vind je een overzicht van alle beoordelingen die je hebt
          ontvangen.
        </p>
      </div>

      <div className="space-y-4">
        {reviews.length > 0 ? (
          reviews.map((review) => (
            <JobReview
              key={review.id}
              review={{
                id: review.id,
                rating: review.rating,
                comment: review.comment,
                reviewer: review.reviewer,
                created_at: review.created_at,
                vakman_id: review.vakman_id,
                feedback: review.feedback,
              }}
            />
          ))
        ) : (
          <p className="text-muted-foreground text-center py-8">
            Je hebt nog geen beoordelingen ontvangen.
          </p>
        )}
      </div>
    </div>
  );
};

export default Reviews;
