import { useState, useEffect } from "react";
import {
  MapPin,
  Calendar,
  Clock,
  Save,
  X,
  Loader2,
  User,
  Mail,
  Phone,
  Building2,
  Receipt,
  MessageSquare,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

interface AdminJobDetailInfoProps {
  job: any;
  status: string;
}

const AdminJobDetailInfo = ({ job, status }: AdminJobDetailInfoProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedJob, setEditedJob] = useState(job);
  const [isSaving, setIsSaving] = useState(false);
  const [messages, setMessages] = useState<any[]>([]);
  const [isMessagesOpen, setIsMessagesOpen] = useState(false);

  useEffect(() => {
    fetchAllMessages();
  }, [job?.id]);

  useEffect(() => {
    setEditedJob(job);
  }, [job]);

  useEffect(() => {
    if (!status) return;
    setEditedJob((editedJob: any) => ({ ...editedJob, status }));
  }, [status]);

  useEffect(() => {
    return () => {
      // Cleanup blob URLs on unmount
      if (editedJob?.photos) {
        editedJob.photos
          .filter((photo: string) => photo.startsWith("blob:"))
          .forEach((url: string) => URL.revokeObjectURL(url));
      }
    };
  }, [editedJob?.photos]);

  const fetchAllMessages = async () => {
    const { data, error } = await supabase
      .from("messages")
      .select("*, profiles:sender_id(*)")
      .eq("job_id", job?.id)
      .order("created_at", { ascending: true });

    if (data) {
      setMessages(data);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "open":
        return "bg-blue-500 hover:bg-blue-600";
      case "in_progress":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "pending":
        return "bg-green-500 hover:bg-green-600";
      case "completed":
        return "bg-green-700 hover:bg-green-800";
      case "cancelled":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Filter out new photos (those with blob: URLs)
      const newPhotos = editedJob.photos.filter((photo: string) =>
        photo.startsWith("blob:")
      );

      // Keep existing photos (those with Supabase URLs)
      const existingPhotos = editedJob.photos.filter(
        (photo: string) => !photo.startsWith("blob:")
      );

      // Upload new photos
      const uploadedUrls = [];
      for (const blobUrl of newPhotos) {
        // Convert blob URL to File object
        const response = await fetch(blobUrl);
        const blob = await response.blob();
        const file = new File([blob], `${Math.random()}.jpg`, {
          type: "image/jpeg",
        });

        const fileExt = file.name.split(".").pop();
        const fileName = `${job?.id}/${Math.random()}.${fileExt}`;
        const { error: uploadError } = await supabase.storage
          .from("job-photos")
          .upload(fileName, file);

        if (uploadError) {
          console.error("Error uploading file:", uploadError);
          continue;
        }

        // Get public URL
        const {
          data: { publicUrl },
        } = supabase.storage.from("job-photos").getPublicUrl(fileName);

        uploadedUrls.push(publicUrl);
      }

      const { title, postal_code, status, description } = editedJob ?? {};
      // Update job with new and existing photos
      const { error: updateError } = await supabase
        .from("jobs")
        .update({
          title,
          postal_code,
          status,
          description,
          photos: [...existingPhotos, ...uploadedUrls],
        })
        .eq("id", job?.id);

      if (updateError) throw updateError;

      setIsEditing(false);
    } catch (error) {
      console.error("Error saving job:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (field: string, value: string | string[]) => {
    setEditedJob((prev: any) => ({ ...prev, [field]: value }));
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedJob(job); // Reset to initial state
  };

  const getChatProfile = (message: any) => {
    // If sender is job placer
    if (message.sender_id === job?.profiles?.id) {
      return job.profiles;
    }
    // If sender is craftman
    return job.job_responses?.find(
      (r: any) => r.profiles?.id === message.sender_id
    )?.profiles;
  };

  return (
    <div className="space-y-8 animate-fade-in mt-4">
      <div className="space-y-4">
        {isEditing ? (
          <Input
            value={editedJob.title}
            onChange={(e) => handleChange("title", e.target.value)}
            className="text-2xl font-bold"
          />
        ) : (
          <h1 className="text-2xl font-bold text-gray-900 group-hover:text-primary transition-colors">
            {editedJob.title}
          </h1>
        )}
        <div className="flex flex-wrap gap-4">
          <div className="flex items-center gap-2 text-gray-600">
            <MapPin className="w-4 h-4" />
            {isEditing ? (
              <Input
                value={editedJob.postal_code}
                onChange={(e) => handleChange("postal_code", e.target.value)}
                className="w-24"
              />
            ) : (
              <span>{editedJob.postal_code}</span>
            )}
          </div>
          <div className="flex items-center gap-2 text-gray-600">
            <Calendar className="w-4 h-4" />
            <span>{new Date(editedJob.created_at).toLocaleDateString()}</span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4 text-gray-600" />
            {isEditing ? (
              <select
                value={editedJob.status}
                onChange={(e) => handleChange("status", e.target.value)}
                className="rounded-md border px-2 py-1"
              >
                <option value="open">Open</option>
                <option value="pending">In behandeling</option>
                <option value="in_progress">In uitvoering</option>
                <option disabled value="completed">
                  Voltooid
                </option>
                <option disabled value="cancelled">
                  Geannuleerd
                </option>
              </select>
            ) : (
              <Badge
                className={`${getStatusColor(editedJob.status)} text-white`}
              >
                {editedJob.status}
              </Badge>
            )}
          </div>
        </div>
      </div>
      {!isEditing && job?.profiles && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-800">
              Klusaanvrager
            </h2>
          </div>

          <div className="bg-card rounded-lg border shadow-sm">
            <div className="flex flex-col md:flex-row items-start p-6">
              <div className="relative w-20 h-20 md:w-24 md:h-24 rounded-full overflow-hidden bg-muted shrink-0 mx-auto md:mx-0 mb-4 md:mb-0">
                {job.profiles.profile_photo_url ? (
                  <img
                    src={job.profiles.profile_photo_url}
                    alt={`${job.profiles.first_name} ${job.profiles.last_name}`}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="h-full w-full flex items-center justify-center bg-primary/10">
                    <User className="h-10 w-10 text-primary/60" />
                  </div>
                )}
              </div>

              <div className="flex-1 md:ml-6 w-full">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-2 mb-4">
                  <div>
                    <h3 className="text-xl font-semibold text-foreground">
                      {job.profiles.company_name ||
                        `${job.profiles.first_name} ${job.profiles.last_name}`}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Account aangemaakt op{" "}
                      {new Date(job.profiles.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    {job.profiles.email && (
                      <div className="flex items-center gap-3 group">
                        <div className="p-2 rounded-md bg-primary/10 text-primary">
                          <Mail className="h-4 w-4" />
                        </div>
                        <a
                          href={`mailto:${job.profiles.email}`}
                          className="text-sm text-foreground hover:text-primary transition-colors"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {job.profiles.email}
                        </a>
                      </div>
                    )}

                    {job.profiles.phone_number && (
                      <div className="flex items-center gap-3 group">
                        <div className="p-2 rounded-md bg-primary/10 text-primary">
                          <Phone className="h-4 w-4" />
                        </div>
                        <a
                          href={`tel:${job.profiles.phone_number}`}
                          className="text-sm text-foreground hover:text-primary transition-colors"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {job.profiles.phone_number}
                        </a>
                      </div>
                    )}
                  </div>

                  {(job.profiles.kvk_number || job.profiles.btw_number) && (
                    <div className="space-y-3">
                      {job.profiles.kvk_number && (
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-md bg-muted">
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                          </div>
                          <span className="text-sm text-muted-foreground">
                            KVK: {job.profiles.kvk_number}
                          </span>
                        </div>
                      )}

                      {job.profiles.btw_number && (
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-md bg-muted">
                            <Receipt className="h-4 w-4 text-muted-foreground" />
                          </div>
                          <span className="text-sm text-muted-foreground">
                            BTW: {job.profiles.btw_number}
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <Separator className="my-6" />
        </div>
      )}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800">Beschrijving</h2>
        {isEditing ? (
          <Textarea
            value={editedJob.description}
            onChange={(e) => handleChange("description", e.target.value)}
            className="min-h-[150px] rounded-none"
          />
        ) : (
          <p className="text-gray-600 whitespace-pre-wrap leading-relaxed">
            {editedJob.description}
          </p>
        )}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800">Foto's</h2>
          {editedJob.photos && editedJob.photos.length > 0 && (
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
              {editedJob.photos.map((photo: string, index: number) => (
                <div
                  key={index}
                  className="relative group overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
                >
                  {isEditing ? (
                    <div className="relative">
                      <img
                        src={photo}
                        alt={`Foto ${index + 1} van de klus`}
                        className="w-full h-48 object-cover"
                      />
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-0 right-0 opacity-90 text-white"
                        onClick={() =>
                          handleChange(
                            "photos",
                            editedJob.photos.filter(
                              (_: string, i: number) => i !== index
                            )
                          )
                        }
                      >
                        <X />
                      </Button>
                    </div>
                  ) : (
                    <>
                      <img
                        src={photo}
                        alt={`Foto ${index + 1} van de klus`}
                        className="w-full h-48 object-cover transform transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-black/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </>
                  )}
                </div>
              ))}
            </div>
          )}
          {isEditing && (
            <Input
              type="file"
              accept="image/*"
              multiple
              onChange={(e) => {
                const files = Array.from(e.target.files || []);
                const newPhotos = files.map((file) =>
                  URL.createObjectURL(file)
                );
                handleChange("photos", [
                  ...(editedJob.photos || []),
                  ...newPhotos,
                ]);
              }}
              className="mt-2"
            />
          )}
        </div>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-800 tracking-wide leading-relaxed">
              Reacties{" "}
              {job.job_responses?.length > 0 && `(${job.job_responses.length})`}
            </h2>
            {job.job_responses?.length > 0 && (
              <div className="text-base text-muted-foreground leading-relaxed">
                {
                  job.job_responses.filter((r: any) => r.status === "pending")
                    .length
                }{" "}
                in afwachting
              </div>
            )}
          </div>

          {job.job_responses?.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {job.job_responses.map((response: any) => (
                <div
                  key={response.id}
                  className="bg-card rounded-lg border shadow-sm hover:shadow-md transition-all duration-300 p-4 space-y-4"
                >
                  {/* Header with profile info */}
                  <div className="flex items-center gap-3">
                    <div className="relative w-12 h-12 rounded-full overflow-hidden bg-muted shrink-0">
                      {response.profiles?.profile_photo_url ? (
                        <img
                          src={response.profiles.profile_photo_url}
                          alt={`${response.profiles.first_name} ${response.profiles.last_name}`}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center bg-primary/10">
                          <User className="h-6 w-6 text-primary/60" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-foreground truncate">
                          {response.profiles?.company_name ||
                            `${response.profiles?.first_name} ${response.profiles?.last_name}`}
                        </h3>
                        <div
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            response.status === "pending"
                              ? "bg-yellow-100 text-yellow-800 border border-yellow-200"
                              : response.status === "accepted"
                              ? "bg-green-100 text-green-800 border border-green-200"
                              : response.status === "rejected"
                              ? "bg-red-100 text-red-800 border border-red-200"
                              : "bg-gray-100 text-gray-800 border border-gray-200"
                          }`}
                        >
                          {response.status === "pending"
                            ? "In afwachting"
                            : response.status === "accepted"
                            ? "Geaccepteerd"
                            : response.status === "rejected"
                            ? "Afgewezen"
                            : response.status}
                        </div>
                      </div>

                      {response.profiles?.company_name && (
                        <p className="text-sm text-muted-foreground truncate">
                          {response.profiles.first_name}{" "}
                          {response.profiles.last_name}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Contact information */}
                  <div className="space-y-3">
                    {response.profiles?.email && (
                      <div className="flex items-center gap-3 text-base">
                        <Mail className="h-4 w-4 text-muted-foreground shrink-0" />
                        <a
                          href={`mailto:${response.profiles.email}`}
                          className="text-foreground hover:text-primary transition-colors truncate leading-relaxed"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {response.profiles.email}
                        </a>
                      </div>
                    )}

                    {response.profiles?.phone_number && (
                      <div className="flex items-center gap-3 text-base">
                        <Phone className="h-4 w-4 text-muted-foreground shrink-0" />
                        <a
                          href={`tel:${response.profiles.phone_number}`}
                          className="text-foreground hover:text-primary transition-colors leading-relaxed"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {response.profiles.phone_number}
                        </a>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between gap-2">
                    <p className="text-base text-foreground line-clamp-2 leading-relaxed tracking-wide flex-1">
                      {response.message}
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="shrink-0 h-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsMessagesOpen(true);
                      }}
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      <span className="hidden sm:inline">Berichten</span>
                    </Button>
                  </div>

                  {/* Footer with date and actions */}
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 pt-2 border-t">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>
                        {new Date(response.created_at).toLocaleDateString(
                          "nl-NL",
                          {
                            day: "numeric",
                            month: "short",
                            year: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          }
                        )}
                      </span>
                    </div>

                    {/* {response.status === "pending" && (
                      <div className="flex gap-2 w-full sm:w-auto">
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 px-3 text-xs flex-1 sm:flex-none"
                          onClick={(e) => {
                            e.stopPropagation();
                            // TODO: Implement reject functionality
                            console.log("Reject response:", response.id);
                          }}
                        >
                          Afwijzen
                        </Button>
                        <Button
                          size="sm"
                          className="h-8 px-3 text-xs flex-1 sm:flex-none"
                          onClick={(e) => {
                            e.stopPropagation();
                            // TODO: Implement accept functionality
                            console.log("Accept response:", response.id);
                          }}
                        >
                          Accepteren
                        </Button>
                      </div>
                    )} */}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-muted/30 rounded-lg border-2 border-dashed border-muted-foreground/20 p-8 text-center">
              <div className="flex flex-col items-center gap-4">
                <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center">
                  <User className="h-8 w-8 text-muted-foreground" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-foreground tracking-wide leading-relaxed">
                    Nog geen reacties
                  </h3>
                  <p className="text-base text-muted-foreground leading-relaxed">
                    Er hebben zich nog geen vakmensen aangemeld voor deze klus.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="flex justify-end space-x-2 ">
          <Button
            className={`${isEditing ? "" : "text-white"}`}
            variant={isEditing ? "outline" : "default"}
            onClick={() => (isEditing ? handleCancel() : setIsEditing(true))}
          >
            {isEditing ? "Annuleren" : "Bewerken"}
          </Button>
          {isEditing && (
            <Button
              onClick={handleSave}
              className="flex items-center gap-2 text-white"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Opslaan...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Opslaan
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      <Dialog open={isMessagesOpen} onOpenChange={setIsMessagesOpen}>
        <DialogContent className="sm:max-w-[600px] h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              Chat Geschiedenis
            </DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-full pr-4">
            <div className="space-y-6 pb-4">
              {messages.map((message) => {
                const isJobPlacer = message.sender_id === job?.profiles?.id;
                const profile = getChatProfile(message);

                return (
                  <div
                    key={message.id}
                    className={`flex gap-3 items-end ${
                      isJobPlacer ? "flex-row" : "flex-row-reverse"
                    }`}
                  >
                    {/* Avatar */}
                    <div className="relative w-8 h-8 rounded-full overflow-hidden bg-muted shrink-0">
                      {profile?.profile_photo_url ? (
                        <img
                          src={profile.profile_photo_url}
                          alt={`${profile.first_name} ${profile.last_name}`}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center bg-primary/10">
                          <User className="h-4 w-4 text-primary/60" />
                        </div>
                      )}
                    </div>

                    {/* Message content */}
                    <div
                      className={`flex flex-col gap-1 max-w-[80%] md:max-w-[70%] ${
                        isJobPlacer ? "items-start" : "items-end"
                      }`}
                    >
                      <div
                        className={`px-4 py-2.5 rounded-2xl ${
                          isJobPlacer
                            ? "bg-muted/50 rounded-tl-none"
                            : "bg-primary text-white rounded-tr-none"
                        }`}
                      >
                        <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                          {message.content}
                        </p>
                      </div>

                      {/* Attachments */}
                      {message.attachment_url && (
                        <div
                          className={`mt-1 ${
                            isJobPlacer ? "rounded-l-lg" : "rounded-r-lg"
                          }`}
                        >
                          {message.attachment_type?.includes("image") ? (
                            <img
                              src={message.attachment_url}
                              alt="Bijlage"
                              className="max-w-[200px] rounded-lg border shadow-sm"
                            />
                          ) : (
                            <a
                              href={message.attachment_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className={`flex items-center gap-2 px-3 py-2 rounded-lg ${
                                isJobPlacer
                                  ? "bg-muted hover:bg-muted/80"
                                  : "bg-primary/90 hover:bg-primary"
                              } transition-colors`}
                            >
                              <Receipt className="h-4 w-4" />
                              <span className="text-sm">Document bekijken</span>
                            </a>
                          )}
                        </div>
                      )}

                      {/* Timestamp */}
                      <span
                        className={`text-xs text-muted-foreground ${
                          isJobPlacer ? "ml-1" : "mr-1"
                        }`}
                      >
                        {new Date(message.created_at).toLocaleTimeString(
                          "nl-NL",
                          {
                            hour: "2-digit",
                            minute: "2-digit",
                          }
                        )}
                      </span>
                    </div>
                  </div>
                );
              })}

              {messages.length === 0 && (
                <div className="h-full flex flex-col items-center justify-center py-12 text-center">
                  <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
                    <MessageSquare className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    Geen berichten
                  </h3>
                  <p className="text-sm text-muted-foreground max-w-sm">
                    Er zijn nog geen berichten uitgewisseld tussen de
                    klusaanvrager en vakman.
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminJobDetailInfo;
