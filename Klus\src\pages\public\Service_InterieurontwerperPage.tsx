/**
 * @description This component renders a comprehensive and SEO-optimized detail page for interior designer services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout, mirroring the structure of other top-tier service pages for consistency. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for interior designers.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Wrench,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
  Palette,
  Home,
  Sparkles,
} from "lucide-react";

const Service_InterieurontwerperPage = () => {
  usePageTitle(
    "Interieurontwerper Nodig? | Klusgebied - Interieuradvies & Styling"
  );
  const navigate = useNavigate();

  const designServices = [
    {
      icon: Palette,
      title: "Interieuradvies",
      description:
        "Persoonlijk advies voor kleur, meubels, verlichting en indeling.",
      points: [
        "Analyse van uw wensen, stijl en budget.",
        "Compleet advies met moodboard en kleurvoorstellen.",
        "Praktisch indelingsplan voor optimale ruimte.",
        "Advies over materialen en meubilair.",
      ],
    },
    {
      icon: Home,
      title: "3D Visualisatie",
      description:
        "Zie uw nieuwe interieur tot leven komen met een realistisch 3D-ontwerp.",
      points: [
        "Fotorealistische weergave van het eindresultaat.",
        "Helpt bij het maken van de juiste keuzes.",
        "Voorkomt teleurstellingen achteraf.",
        "Inclusief lichtinval en sfeerimpressie.",
      ],
    },
    {
      icon: Sparkles,
      title: "Verlichtingsplan",
      description:
        "Een professioneel plan voor functionele en sfeervolle verlichting.",
      points: [
        "Combinatie van basis-, functionele en sfeerverlichting.",
        "Creëert de juiste sfeer in elke ruimte.",
        "Verbetert de functionaliteit van uw interieur.",
        "Advies over armaturen en lichtbronnen.",
      ],
    },
    {
      icon: Wrench,
      title: "Projectbegeleiding",
      description:
        "Van ontwerp tot realisatie, wij begeleiden het hele project.",
      points: [
        "Aansturen van vakmensen en bewaken van planning.",
        "Controle op kwaliteit en budget.",
        "Eén aanspreekpunt voor het hele project.",
        "Zorgeloos genieten van het proces en resultaat.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Palette className="w-8 h-8 text-white" />,
      title: "Persoonlijk & Uniek",
      description:
        "Een interieur dat perfect aansluit bij uw smaak, wensen en levensstijl.",
    },
    {
      icon: <Home className="w-8 h-8 text-white" />,
      title: "Functioneel & Stijlvol",
      description:
        "Wij combineren esthetiek met praktische oplossingen voor een leefbaar huis.",
    },
    {
      icon: <Sparkles className="w-8 h-8 text-white" />,
      title: "Verrassende Ideeën",
      description:
        "Onze ontwerpers komen met creatieve ideeën waar u zelf niet aan denkt.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een interieurontwerper?",
      answer:
        "De kosten variëren sterk, van een paar honderd euro voor een basisadvies tot duizenden euro's voor een compleet ontwerp en projectbegeleiding. Vraag een offerte op maat aan.",
    },
    {
      question: "Werken jullie ook met een klein budget?",
      answer:
        "Absoluut. Een goed ontwerp hoeft niet duur te zijn. Wij denken creatief met u mee om binnen uw budget het maximale resultaat te behalen.",
    },
    {
      question: "Hoe ziet een ontwerpproces eruit?",
      answer:
        "Het proces start met een kennismakingsgesprek, gevolgd door een conceptontwerp, 3D-visualisaties en een definitief plan. Wij kunnen ook de uitvoering begeleiden.",
    },
    {
      question: "Kan ik mijn eigen meubels behouden in het nieuwe ontwerp?",
      answer:
        "Jazeker. Wij integreren graag uw favoriete meubelstukken in het nieuwe ontwerp en creëren een harmonieus geheel met nieuwe elementen.",
    },
  ];

  const reviews = [
    {
      name: "Eva de Groot",
      location: "Utrecht",
      rating: 5,
      quote:
        "De interieurontwerper heeft onze woonkamer omgetoverd tot een droomplek. Precies de sfeer die we wilden, maar waar we zelf niet op kwamen!",
      highlighted: true,
    },
    {
      name: "Tom Bakker",
      location: "Rotterdam",
      rating: 5,
      quote:
        "Fantastisch 3D-ontwerp gekregen. Het was alsof we al in ons nieuwe huis rondliepen. Maakte alle keuzes zoveel makkelijker.",
      highlighted: false,
    },
    {
      name: "Familie Singh",
      location: "Amstelveen",
      rating: 5,
      quote:
        "Professioneel, creatief en heel prettig in de omgang. Heeft ons enorm geholpen met de indeling en kleuren. Een absolute aanrader.",
      highlighted: false,
    },
    {
      name: "Daan Meijer",
      location: "Haarlem",
      rating: 4,
      quote:
        "Goed advies en een mooi lichtplan. Het project duurde iets langer dan verwacht, maar het resultaat is het helemaal waard.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1618220179428-22790b461013?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1616046229478-9901c5536a45?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1615874959474-d609969a20ed?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1538688525198-9b88f6f53126?ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Deel je wensen",
      description:
        "Beschrijf je ruimte, stijl en dromen. Voeg foto's en plattegronden toe.",
      microcopy: "Vrijblijvend en binnen 2 minuten",
    },
    {
      icon: MessageSquare,
      title: "Ontvang voorstellen",
      description:
        "Krijg reacties en portfolio's van geverifieerde interieurontwerpers.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & creëer",
      description:
        "Vergelijk ontwerpers en kies de perfecte match om jouw droominterieur te realiseren.",
      microcopy: "Vergelijk portfolio's en reviews, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Interieurontwerpers in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "interieurontwerper",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Interieurontwerper Nodig? | Klusgebied - Interieuradvies & Styling
        </title>
        <meta
          name="description"
          content="Creëer uw droominterieur met een professionele interieurontwerper. Voor interieuradvies, 3D-ontwerp en projectbegeleiding. Plaats uw aanvraag gratis op Klusgebied."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Interieurontwerper",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.9",
              reviewCount: "152",
            },
            review: reviews.map((review) => ({
              "@type": "Review",
              author: { "@type": "Person", name: review.name },
              reviewRating: {
                "@type": "Rating",
                ratingValue: review.rating,
                bestRating: "5",
              },
              reviewBody: review.quote,
            })),
            offers: {
              "@type": "Offer",
              priceCurrency: "EUR",
              priceSpecification: {
                "@type": "PriceSpecification",
                priceCurrency: "EUR",
                minPrice: "500",
                maxPrice: "5000",
                valueAddedTaxIncluded: true,
                description:
                  "Prijs is afhankelijk van de omvang van het project. Vraag een offerte aan.",
              },
            },
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            mainEntity: faqs.map((faq) => ({
              "@type": "Question",
              name: faq.question,
              acceptedAnswer: {
                "@type": "Answer",
                text: faq.answer,
              },
            })),
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-pink-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-pink-100 border border-pink-200/80 rounded-full px-4 py-2 mb-6 pl-[16px] pr-[14px]">
                    <Palette className="w-5 h-5 text-pink-600" />
                    <span className="text-pink-800 font-semibold text-sm">
                      Interieur Design
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Interieurontwerper nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-500 mt-[10px] !pt-[9px] !pb-[9px]">
                      Creëer uw droomhuis
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-[226px] mt-[12px]">
                    Creëer uw droominterieur met professioneel advies en
                    styling. Van een enkele kamer tot een complete woning.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus")}
                      className="group inline-flex items-center justify-center bg-pink-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-pink-600 transition-all duration-300 shadow-lg hover:shadow-pink-500/30 transform hover:-translate-y-1"
                    >
                      Start met een interieuradvies
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.9/5</span>
                    <span>gebaseerd op 152 projecten</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1618220179428-22790b461013?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Stijlvol en modern interieur"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte designer voor jouw
                project.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-pink-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-pink-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Ontwerpdiensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Wij helpen u bij elke stap om uw droomhuis te realiseren.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {designServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-pink-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-pink-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-pink-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een interieurontwerper?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten zijn afhankelijk van de omvang van het project. Een
                basisadvies is vaak al mogelijk vanaf een paar honderd euro.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-pink-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Kleur- en materiaaladvies:{" "}
                    <strong className="text-slate-900">vanaf €250</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-pink-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Compleet interieurontwerp:{" "}
                    <strong className="text-slate-900">vanaf €1.500</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-pink-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Projectbegeleiding:{" "}
                    <strong className="text-slate-900">
                      Prijs op aanvraag
                    </strong>
                  </span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Vraag een vrijblijvende offerte aan voor een prijs op maat.
              </div>
              <button
                onClick={() => navigate("/plaats-een-klus")}
                className="bg-pink-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-pink-600 transition-all duration-300 shadow-lg hover:shadow-pink-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis voorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een interieurontwerper vonden
                via Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-purple-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      \n{" "}
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-purple-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-purple-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-purple-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center mt-12">
              <a
                href="#"
                className="text-purple-600 font-semibold hover:underline"
              >
                Bekijk alle beoordelingen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een ontwerper via Klusgebied en wees verzekerd van een
                prachtig en functioneel resultaat.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-pink-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/over-ons")}
                className="text-pink-300 font-semibold hover:text-white transition-colors"
              >
                Bekijk waarom vakmannen en klanten voor ons kiezen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </button>
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(`/stad/${city.toLowerCase().replace(/\s+/g, "-")}`)
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-pink-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-pink-500 to-purple-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar om uw interieur te transformeren?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvend gesprek aan en ontdek wat een professionele
              interieurontwerper voor u kan betekenen.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus")}
              className="bg-white text-pink-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Start met een interieuradvies
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus")}
          className="w-full group inline-flex items-center justify-center bg-pink-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-pink-600 transition-all duration-300 shadow-lg hover:shadow-pink-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_InterieurontwerperPage;
