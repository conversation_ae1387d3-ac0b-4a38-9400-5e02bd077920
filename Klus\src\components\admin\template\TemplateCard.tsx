import { <PERSON><PERSON><PERSON>, Trash2, <PERSON><PERSON><PERSON> } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { JobTemplate } from "@/pages/admin/JobTemplatePage";
import { Badge } from "@/components/ui/badge";

interface TemplateCardProps {
  template: JobTemplate;
  viewMode: "grid" | "list";
  onEdit: () => void;
  onDelete: () => void;
}

export function TemplateCard({
  template,
  viewMode,
  onEdit,
  onDelete,
}: TemplateCardProps) {
  const GridView = () => (
    <Card className="group overflow-hidden transition-all duration-200">
      <div className="aspect-video relative">
        {template.photos?.[0] ? (
          <img
            src={template.photos[0]}
            alt={template.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-muted flex items-center justify-center">
            <ImageIcon className="h-12 w-12 text-muted-foreground/50" />
          </div>
        )}
        {template.photos?.length > 1 && (
          <Badge
            variant="secondary"
            className="absolute top-2 right-2 bg-black/50 text-white"
          >
            {template.photos.length} foto's
          </Badge>
        )}
      </div>

      <div className="p-4 space-y-4">
        <div>
          <p className="text-lg font-medium mb-2 line-clamp-1">
            {template.title}
          </p>
          <p className="text-muted-foreground text-sm line-clamp-2">
            {template.description}
          </p>
        </div>

        <div className="flex items-center gap-2 pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={onEdit}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Bewerken
          </Button>
          <Button
            variant="error"
            size="sm"
            className="flex-1"
            onClick={onDelete}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Verwijderen
          </Button>
        </div>
      </div>
    </Card>
  );

  const ListView = () => (
    <div className="hidden sm:block">
      <Card className="overflow-hidden">
        <div className="flex gap-4 p-4">
          <div className="relative w-40 h-28 flex-shrink-0">
            {template.photos?.[0] ? (
              <img
                src={template.photos[0]}
                alt={template.title}
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <div className="w-full h-full bg-muted rounded-lg flex items-center justify-center">
                <ImageIcon className="h-8 w-8 text-muted-foreground/50" />
              </div>
            )}
            {template.photos?.length > 1 && (
              <Badge
                variant="secondary"
                className="absolute bottom-2 right-2 bg-black/50 text-white"
              >
                {template.photos.length} foto's
              </Badge>
            )}
          </div>

          <div className="flex-1 min-w-0">
            <div className="space-y-2">
              <p className="text-lg font-semibold line-clamp-1">
                {template.title}
              </p>
              <p className="text-muted-foreground line-clamp-2 text-sm">
                {template.description}
              </p>
            </div>

            <div className="flex items-center gap-2 mt-4">
              <Button variant="outline" size="sm" onClick={onEdit}>
                <Pencil className="h-4 w-4 mr-2" />
                Bewerken
              </Button>
              <Button variant="error" size="sm" onClick={onDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                Verwijderen
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );

  return (
    <>
      <div className="block sm:hidden">
        <GridView />
      </div>
      <div className="hidden sm:block">
        {viewMode === "grid" ? <GridView /> : <ListView />}
      </div>
    </>
  );
}
