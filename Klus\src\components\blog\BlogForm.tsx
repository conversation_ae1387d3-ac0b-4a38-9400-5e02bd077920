/**
 * Blog post creation and editing form with rich text editor capabilities.
 * Handles all blog data fields including title, description, category, content with WYSIWYG editor,
 * image upload, and SEO settings.
 */
import React, { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  Save,
  Upload,
  X,
  MapPin,
  Tag,
  Calendar,
  Globe,
  Search,
  ArrowLeft,
} from "lucide-react";
// @ts-ignore - react-quill will be installed during setup
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";

interface Author {
  id: number;
  name: string;
  avatar_url?: string;
}

interface FormData {
  title: string;
  slug: string;
  summary: string;
  content: string;
  category: string;
  author_id: number | string;
  cover_image_url: string;
  tags: string[];
  city: string;
  region: string;
  seo_title: string;
  seo_description: string;
  published: boolean;
}

interface BlogFormProps {
  isEdit?: boolean;
}

export const BlogForm: React.FC<BlogFormProps> = ({ isEdit = false }) => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [authors, setAuthors] = useState<Author[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [slugChecking, setSlugChecking] = useState(false);
  const [isRawHtmlMode, setIsRawHtmlMode] = useState(true); // Default to raw HTML mode
  const fileInputRef = useRef<HTMLInputElement>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [formData, setFormData] = useState<FormData>({
    title: "",
    slug: "",
    summary: "",
    content: "",
    category: "SEO",
    author_id: "",
    cover_image_url: "",
    tags: [],
    city: "",
    region: "",
    seo_title: "",
    seo_description: "",
    published: false,
  });

  // Categories for blog posts
  const categories = [
    "SEO",
    "Marketing",
    "Technologie",
    "Zakelijk",
    "Webdesign",
    "E-commerce",
    "Sociale Media",
    "Content Strategie",
  ];

  useEffect(() => {
    fetchAuthors();
    if (isEdit && id) {
      fetchPost(id);
    }
  }, [isEdit, id]);

  const fetchAuthors = async () => {
    try {
      const { data, error } = await supabase
        .from("authors" as any)
        .select("id, name, avatar_url");

      if (error) {
        console.error("Error fetching authors:", error);
        return;
      }

      setAuthors((data as any) || []);

      // Set default author if none selected and authors exist
      if (!formData.author_id && data && data.length > 0) {
        setFormData((prev) => ({ ...prev, author_id: (data as any)[0].id }));
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const fetchPost = async (postId: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("posts" as any)
        .select("*")
        .eq("id", postId)
        .single();

      if (error) {
        console.error("Error fetching post:", error);
        return;
      }

      if (data) {
        setFormData({
          ...(data as any),
          tags: (data as any).tags || [],
        });
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }

    setFormData((prev) => ({ ...prev, [name]: value }));

    // Auto-generate slug from title
    if (name === "title" && !isEdit) {
      const slug = value
        .toLowerCase()
        .replace(/[^\w\s-]/g, "") // Remove special chars
        .replace(/\s+/g, "-") // Replace spaces with hyphens
        .replace(/-+/g, "-"); // Remove consecutive hyphens

      setFormData((prev) => ({ ...prev, slug }));
      // Check slug availability with debouncing
      debouncedCheckSlugAvailability(slug);
    }

    // Check slug availability when slug field is changed directly
    if (name === "slug") {
      debouncedCheckSlugAvailability(value);
    }
  };

  const handleEditorChange = (content: string) => {
    setFormData((prev) => ({ ...prev, content }));
  };

  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tagsString = e.target.value;
    const tagsArray = tagsString
      .split(",")
      .map((tag) => tag.trim())
      .filter(Boolean);
    setFormData((prev) => ({ ...prev, tags: tagsArray }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, published: checked }));
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    const fileExt = file.name.split(".").pop();
    const fileName = `${Math.random()
      .toString(36)
      .substring(2, 15)}.${fileExt}`;
    const filePath = fileName;

    try {
      setUploading(true);

      const { error: uploadError } = await supabase.storage
        .from("blog-images")
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      const {
        data: { publicUrl },
      } = supabase.storage.from("blog-images").getPublicUrl(filePath);

      setFormData((prev) => ({ ...prev, cover_image_url: publicUrl }));
    } catch (error) {
      console.error("Error uploading file:", error);
      toast({
        title: "Upload mislukt",
        description:
          "Er is een fout opgetreden bij het uploaden van de afbeelding.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = "Titel is verplicht";
    if (!formData.slug.trim()) newErrors.slug = "Slug is verplicht";
    if (!formData.summary.trim())
      newErrors.summary = "Samenvatting is verplicht";
    if (!formData.content.trim()) newErrors.content = "Inhoud is verplicht";
    if (!formData.category) newErrors.category = "Categorie is verplicht";
    if (!formData.author_id) newErrors.author_id = "Auteur is verplicht";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        title: "Validatiefout",
        description: "Controleer de gemarkeerde velden en probeer opnieuw.",
        variant: "destructive",
      });
      return;
    }

    try {
      setSaving(true);

      const postData = {
        ...formData,
        published_at: formData.published ? new Date().toISOString() : null,
        updated_at: new Date().toISOString(),
        // Auto-generate SEO fields if not provided
        seo_title:
          formData.seo_title ||
          (formData.title.length > 60
            ? formData.title.substring(0, 57) + "..."
            : formData.title),
        seo_description:
          formData.seo_description ||
          (formData.summary.length > 160
            ? formData.summary.substring(0, 157) + "..."
            : formData.summary),
      };

      let result: any;
      if (isEdit && id) {
        result = await supabase
          .from("posts" as any)
          .update(postData)
          .eq("id", id);
      } else {
        result = await supabase
          .from("posts" as any)
          .insert([postData])
          .select();
      }

      if (result.error) {
        throw result.error;
      }

      toast({
        title: isEdit ? "Artikel bijgewerkt" : "Artikel aangemaakt",
        description: isEdit
          ? "Het artikel is succesvol bijgewerkt."
          : "Het artikel is succesvol aangemaakt.",
      });

      // Navigate back to posts list
      navigate("/beheerder/blog/posts");
    } catch (error: any) {
      console.error("Error saving post:", error);
      toast({
        title: "Fout bij opslaan",
        description:
          error.message ||
          "Er is een fout opgetreden bij het opslaan van het artikel.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const checkSlugAvailability = async (slug: string) => {
    if (!slug) return;

    try {
      setSlugChecking(true);

      const { data, error } = await supabase
        .from("posts" as any)
        .select("id")
        .eq("slug", slug)
        .maybeSingle();

      if (error) {
        throw error;
      }

      // If editing and the slug belongs to the current post, it's available
      if (isEdit && id && (data as any)?.id === parseInt(id)) {
        return;
      }

      // If data exists, slug is taken
      if (data) {
        setErrors((prev) => ({ ...prev, slug: "Deze slug is al in gebruik" }));
      } else {
        // Clear error if slug is available
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors.slug;
          return newErrors;
        });
      }
    } catch (error) {
      console.error("Error checking slug:", error);
    } finally {
      setSlugChecking(false);
    }
  };

  // Debounced version of slug availability check
  const debouncedCheckSlugAvailability = useCallback(
    (slug: string) => {
      // Clear any existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Don't check if slug is empty
      if (!slug.trim()) {
        setSlugChecking(false);
        return;
      }

      // Set timeout for new check
      debounceTimeoutRef.current = setTimeout(() => {
        checkSlugAvailability(slug);
      }, 500); // 500ms delay
    },
    [formData.slug, isEdit, id]
  );

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate("/beheerder/blog/posts")}
          className="mr-4"
        >
          <ArrowLeft size={16} className="mr-2" />
          Terug naar artikelen
        </Button>
        <h1 className="text-2xl font-bold">
          {isEdit ? "Artikel bewerken" : "Nieuw artikel"}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Tabs defaultValue="content" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="content">Inhoud</TabsTrigger>
            <TabsTrigger value="seo">SEO & Metadata</TabsTrigger>
            <TabsTrigger value="publishing">Publicatie</TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label
                    htmlFor="title"
                    className={errors.title ? "text-red-500" : ""}
                  >
                    Titel {errors.title && `(${errors.title})`}
                  </Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    className={errors.title ? "border-red-500" : ""}
                  />
                </div>

                <div>
                  <Label
                    htmlFor="slug"
                    className={errors.slug ? "text-red-500" : ""}
                  >
                    Slug {errors.slug && `(${errors.slug})`}
                  </Label>
                  <div className="flex">
                    <Input
                      id="slug"
                      name="slug"
                      value={formData.slug}
                      onChange={handleInputChange}
                      className={errors.slug ? "border-red-500" : ""}
                    />
                    {slugChecking && (
                      <span className="ml-2 text-gray-500">Controleren...</span>
                    )}
                  </div>
                </div>

                <div>
                  <Label
                    htmlFor="summary"
                    className={errors.summary ? "text-red-500" : ""}
                  >
                    Samenvatting {errors.summary && `(${errors.summary})`}
                  </Label>
                  <Textarea
                    id="summary"
                    name="summary"
                    value={formData.summary}
                    onChange={handleInputChange}
                    className={errors.summary ? "border-red-500" : ""}
                    rows={3}
                  />
                </div>

                <div>
                  <Label
                    htmlFor="category"
                    className={errors.category ? "text-red-500" : ""}
                  >
                    Categorie {errors.category && `(${errors.category})`}
                  </Label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    className={`w-full p-2 border rounded-md ${
                      errors.category ? "border-red-500" : "border-gray-300"
                    }`}
                  >
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label
                    htmlFor="author_id"
                    className={errors.author_id ? "text-red-500" : ""}
                  >
                    Auteur {errors.author_id && `(${errors.author_id})`}
                  </Label>
                  <select
                    id="author_id"
                    name="author_id"
                    value={formData.author_id}
                    onChange={handleInputChange}
                    className={`w-full p-2 border rounded-md ${
                      errors.author_id ? "border-red-500" : "border-gray-300"
                    }`}
                  >
                    <option value="">Selecteer auteur</option>
                    {authors.map((author) => (
                      <option key={author.id} value={author.id}>
                        {author.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <div className="mb-4">
                  <Label htmlFor="cover_image">Omslagafbeelding</Label>
                  <div className="mt-2">
                    {formData.cover_image_url ? (
                      <div className="relative">
                        <img
                          src={formData.cover_image_url}
                          alt="Cover"
                          className="w-full h-48 object-cover rounded-md"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={() =>
                            setFormData((prev) => ({
                              ...prev,
                              cover_image_url: "",
                            }))
                          }
                        >
                          <X size={16} />
                        </Button>
                      </div>
                    ) : (
                      <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          disabled={uploading}
                        >
                          {uploading ? "Uploaden..." : "Upload afbeelding"}
                          <Upload size={16} className="ml-2" />
                        </Button>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleFileUpload}
                          className="hidden"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div>
              <Label
                htmlFor="content"
                className={errors.content ? "text-red-500" : ""}
              >
                Inhoud {errors.content && `(${errors.content})`}
              </Label>
              <div className="mb-2">
                <div className="flex items-center">
                  <Switch
                    checked={isRawHtmlMode}
                    onCheckedChange={setIsRawHtmlMode}
                    id="html-mode"
                  />
                  <Label htmlFor="html-mode" className="ml-2">
                    Raw HTML modus
                  </Label>
                </div>
              </div>

              {isRawHtmlMode ? (
                <Textarea
                  id="content"
                  name="content"
                  value={formData.content}
                  onChange={handleInputChange}
                  className={`min-h-[300px] font-mono ${
                    errors.content ? "border-red-500" : ""
                  }`}
                  rows={15}
                />
              ) : (
                <div
                  className={`border rounded-md ${
                    errors.content ? "border-red-500" : "border-gray-300"
                  }`}
                >
                  <ReactQuill
                    theme="snow"
                    value={formData.content}
                    onChange={handleEditorChange}
                  />
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="seo" className="space-y-4">
            <Card>
              <CardContent className="pt-6 space-y-4">
                <div>
                  <Label htmlFor="seo_title">SEO Titel (max. 60 tekens)</Label>
                  <Input
                    id="seo_title"
                    name="seo_title"
                    value={formData.seo_title}
                    onChange={handleInputChange}
                    maxLength={60}
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {formData.seo_title ? formData.seo_title.length : 0}/60
                    tekens
                  </div>
                </div>

                <div>
                  <Label htmlFor="seo_description">
                    SEO Beschrijving (max. 160 tekens)
                  </Label>
                  <Textarea
                    id="seo_description"
                    name="seo_description"
                    value={formData.seo_description}
                    onChange={handleInputChange}
                    maxLength={160}
                    rows={3}
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {formData.seo_description
                      ? formData.seo_description.length
                      : 0}
                    /160 tekens
                  </div>
                </div>

                <div>
                  <Label htmlFor="tags">Tags (komma-gescheiden)</Label>
                  <div className="flex items-center">
                    <Tag size={16} className="mr-2 text-gray-500" />
                    <Input
                      id="tags"
                      name="tags"
                      value={formData.tags.join(", ")}
                      onChange={handleTagsChange}
                      placeholder="seo, marketing, tips, ..."
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="city">
                      Stad/Plaats (voor geo-targeting)
                    </Label>
                    <div className="flex items-center">
                      <MapPin size={16} className="mr-2 text-gray-500" />
                      <Input
                        id="city"
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        placeholder="Amsterdam"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="region">Regio (voor geo-targeting)</Label>
                    <div className="flex items-center">
                      <Globe size={16} className="mr-2 text-gray-500" />
                      <Input
                        id="region"
                        name="region"
                        value={formData.region}
                        onChange={handleInputChange}
                        placeholder="Noord-Holland"
                      />
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-2 flex items-center">
                    <Search size={16} className="mr-2" />
                    Google Preview
                  </h3>
                  <div className="border border-gray-200 p-3 rounded bg-white">
                    <div className="text-blue-600 text-lg truncate">
                      {formData.seo_title ||
                        formData.title ||
                        "Titel van je artikel"}
                    </div>
                    <div className="text-green-700 text-sm">
                      {window.location.origin}/blog/
                      {formData.slug || "artikel-slug"}
                    </div>
                    <div className="text-gray-600 text-sm line-clamp-2">
                      {formData.seo_description ||
                        formData.summary ||
                        "Beschrijving van je artikel..."}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="publishing" className="space-y-4">
            <Card>
              <CardContent className="pt-6 space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Publicatiestatus</h3>
                    <p className="text-gray-500 text-sm">
                      {formData.published
                        ? "Dit artikel zal zichtbaar zijn voor bezoekers"
                        : "Dit artikel is een concept en niet zichtbaar voor bezoekers"}
                    </p>
                  </div>
                  <Switch
                    checked={formData.published}
                    onCheckedChange={handleSwitchChange}
                    id="published"
                  />
                </div>

                {formData.published && (
                  <div className="flex items-center text-gray-600 mt-2">
                    <Calendar size={16} className="mr-2" />
                    <span>
                      Wordt gepubliceerd op:{" "}
                      {new Date().toLocaleDateString("nl-NL", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate("/beheerder/blog/posts")}
            className="mr-2"
          >
            Annuleren
          </Button>
          <Button type="submit" disabled={saving} className="flex items-center">
            {saving ? "Opslaan..." : "Opslaan"}
            <Save size={16} className="ml-2" />
          </Button>
        </div>
      </form>
    </div>
  );
};

export default BlogForm;
