import { UploadCloud, X } from "lucide-react";

import { Button } from "@/components/ui/button";

interface PhotoUploadProps {
  photos: File[];
  setPhotos: (photos: File[]) => void;
  previewUrls: string[];
  setPreviewUrls: (urls: string[]) => void;
}

export const PhotoUpload = ({
  photos,
  setPhotos,
  previewUrls,
  setPreviewUrls,
}: PhotoUploadProps) => {
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    if (selectedFiles.length > 0) {
      if (photos.length + selectedFiles.length > 5) {
        alert("Je kunt maximaal 5 foto's uploaden");
        return;
      }

      const newPreviewUrls = selectedFiles.map((file) =>
        URL.createObjectURL(file)
      );
      setPreviewUrls([...previewUrls, ...newPreviewUrls]);
      setPhotos([...photos, ...selectedFiles]);
    }
  };

  const removePhoto = (index: number) => {
    URL.revokeObjectURL(previewUrls[index]);
    const newPreviewUrls = previewUrls.filter((_, i) => i !== index);
    const newPhotos = photos.filter((_, i) => i !== index);
    setPreviewUrls(newPreviewUrls);
    setPhotos(newPhotos);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {previewUrls.map((url, index) => (
          <div key={index} className="relative aspect-square">
            <img
              src={url}
              alt={`Preview ${index + 1}`}
              className="w-full h-full object-cover rounded-lg"
            />
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute top-0 right-0"
              onClick={() => removePhoto(index)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ))}
        {photos.length < 5 && (
          <div className="aspect-square border-2 border-dashed rounded-lg flex items-center justify-center relative">
            <input
              type="file"
              accept="image/*"
              multiple
              onChange={handleFileChange}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />
            <div className="text-center p-4">
              <UploadCloud className="mx-auto h-8 w-8 text-muted-foreground" />
              <p className="mt-2 text-sm text-muted-foreground">
                Klik om foto's te uploaden
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
