/**
 * @description This component creates an engaging testimonial carousel showcasing real customer reviews with advanced navigation and animations. It displays customer photos, ratings, quotes, and location information in a beautiful, cinematic slider with swipe support, styled to match the light and modern aesthetic of the website. The component includes navigation arrows, a dots indicator, automatic sliding, and smooth transitions with world-class interactions, and is fully optimized for mobile viewing. Key variables include testimonialsData array with customer information, currentSlide state for managing the active slide, and touch gesture handling for mobile swipe functionality.
 */
import React, { useState, useEffect, useRef } from "react";
import {
  ChevronLeft,
  ChevronRight,
  Star,
  Quote,
  CheckCircle,
} from "lucide-react";

const TestimonialsSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const sliderRef = useRef(null);
  const minSwipeDistance = 50;

  const testimonialsData = [
    {
      id: 1,
      name: "<PERSON>",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Fantastische ervaring! Binnen een dag had ik 5 reacties van loodgieters. De vakman die ik koos was professioneel en betaalbaar. Echt een aanrader!",
      image:
        "https://heyboss.heeyo.ai/1751471803-b35444d0-seniorsathome.jfcs.org-app-uploads-2023-05-iStock-95340089-article-1.jpg",
      service: "Waterleiding Vervangen",
      verified: true,
    },
    {
      id: 2,
      name: "Pieter Janssen",
      location: "Utrecht",
      rating: 5,
      quote:
        "Mijn keuken werd vakkundig geschilderd. Klusgebied maakte het vinden van een betrouwbare schilder zo eenvoudig! Perfecte communicatie.",
      image:
        "https://heyboss.heeyo.ai/1751471803-220af55d-img.freepik.com-free-photo-smiling-craftsman-with-electric-drill-1098-15303.jpg",
      service: "Schilder",
      verified: true,
    },
    {
      id: 3,
      name: "Lisa & Tom",
      location: "Haarlem",
      rating: 5,
      quote:
        "Voor onze badkamerrenovatie vonden we via Klusgebied de perfecte timmerman. Geweldig platform met echte professionals!",
      image:
        "https://images.unsplash.com/photo-1678380326532-89e3ea5c5ce4?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx5b3VuZyUyMGZhbWlseSUyQyUyMHBvcnRyYWl0JTJDJTIwaGFwcHl8ZW58MHx8fHwxNzUxNDcyMDQ0fDA&ixlib=rb-4.1.0?w=1024&h=1024",
      service: "Timmerman",
      verified: true,
    },
    {
      id: 4,
      name: "Ahmed Hassan",
      location: "Rotterdam",
      rating: 5,
      quote:
        "Snel, betrouwbaar en geen gedoe. Precies wat je wilt als ondernemer. Mijn kantoor werd perfect gerenoveerd binnen budget.",
      image:
        "https://heyboss.heeyo.ai/1751471804-90e54a79-media.licdn.com-dms-image-v2-C5612AQG8nM2uJrVBdQ-article-cover-image-shrink-720-1280-article-cover-image-shrink-720-1280-0-1630421131000-e-2147483647-v-beta-t-UNfJ2gpcq6o8R0i-VHuRUVDVeLwADnzg-CVLGxa2Qn8",
      service: "Renovatie",
      verified: true,
    },
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % testimonialsData.length);
  };

  const prevSlide = () => {
    setCurrentSlide(
      (prev) => (prev - 1 + testimonialsData.length) % testimonialsData.length
    );
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(nextSlide, 5000);
    return () => clearInterval(interval);
  }, [isAutoPlaying, currentSlide]);

  // Touch handlers for mobile swipe
  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      nextSlide();
    } else if (isRightSwipe) {
      prevSlide();
    }
  };

  return (
    <section className="bg-gradient-to-br from-white via-slate-50 to-white text-slate-800 py-16 sm:py-20 lg:py-24 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-16 motion-preset-slide-up">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
            Duizenden klanten gingen je voor
          </h2>
          <p className="text-lg sm:text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Lees waarom onze klanten zo tevreden zijn over hun ervaring met
            Klusgebied.
          </p>
        </div>

        <div
          className="relative"
          ref={sliderRef}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onMouseEnter={() => setIsAutoPlaying(false)}
          onMouseLeave={() => setIsAutoPlaying(true)}
        >
          <div
            className="flex transition-transform duration-700 ease-in-out"
            style={{ transform: `translateX(-${currentSlide * 100}%)` }}
          >
            {testimonialsData.map((testimonial) => (
              <div key={testimonial.id} className="w-full flex-shrink-0 px-2">
                <div className="bg-white rounded-3xl shadow-xl border border-slate-200/80 overflow-hidden">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-6 md:gap-0 items-center">
                    {/* Image on the left (2/5 width) */}
                    <div className="md:col-span-2 relative h-full w-full motion-preset-slide-up">
                      <img
                        src={testimonial.image}
                        alt={`${testimonial.name} - Tevreden klant van Klusgebied`}
                        className="w-full h-full object-cover aspect-square md:aspect-[4/5]"
                        loading="lazy"
                        decoding="async"
                        width="400"
                        height="500"
                      />
                      {testimonial.verified && (
                        <div className="absolute top-4 right-4 bg-green-500 text-white rounded-full p-2 flex items-center gap-2 text-sm font-bold shadow-lg">
                          <CheckCircle className="w-5 h-5" />
                          <span>Geverifieerd</span>
                        </div>
                      )}
                    </div>

                    {/* Text on the right (3/5 width) */}
                    <div className="md:col-span-3 motion-preset-slide-up motion-delay-100 p-6 sm:p-8 lg:p-12">
                      <Quote className="w-12 h-12 sm:w-16 sm:h-16 text-teal-400 mb-4 sm:mb-6 opacity-30" />

                      <blockquote className="text-xl sm:text-2xl lg:text-3xl text-slate-800 leading-tight lg:leading-tight font-bold mb-6 sm:mb-8">
                        {testimonial.quote}
                      </blockquote>

                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between border-t border-slate-200 pt-6 gap-4">
                        <div>
                          <div className="font-bold text-slate-800 text-lg sm:text-xl lg:text-2xl">
                            {testimonial.name}
                          </div>
                          <div className="text-slate-600 flex items-center flex-wrap gap-x-3 gap-y-1 mt-1">
                            <span>{testimonial.location}</span>
                            <span className="text-slate-300 hidden sm:inline">
                              •
                            </span>
                            <span className="bg-teal-100 text-teal-800 px-3 py-1 rounded-full text-sm font-medium">
                              {testimonial.service}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-1 shrink-0">
                          {[...Array(testimonial.rating)].map((_, i) => (
                            <Star
                              key={i}
                              className="w-5 h-5 sm:w-6 sm:h-6 text-yellow-400 fill-current"
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-0 md:-left-6 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm rounded-full p-2 md:p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:bg-white border border-slate-200 group"
            aria-label="Previous testimonial"
          >
            <ChevronLeft className="w-6 h-6 md:w-8 md:h-8 text-slate-600 group-hover:text-teal-500 transition-colors duration-300" />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-0 md:-right-6 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm rounded-full p-2 md:p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:bg-white border border-slate-200 group"
            aria-label="Next testimonial"
          >
            <ChevronRight className="w-6 h-6 md:w-8 md:h-8 text-slate-600 group-hover:text-teal-500 transition-colors duration-300" />
          </button>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center space-x-4 mt-12 sm:mt-16">
          {testimonialsData.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`transition-all duration-500 rounded-full flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-teal-400 ${
                index === currentSlide
                  ? "w-12 h-4 bg-teal-500 shadow-lg shadow-teal-500/20"
                  : "w-6 h-4 bg-slate-300 hover:bg-slate-400 hover:scale-110"
              }`}
              aria-label={`Go to testimonial ${index + 1}`}
              style={{ margin: 2 }}
              tabIndex={0}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSlider;
