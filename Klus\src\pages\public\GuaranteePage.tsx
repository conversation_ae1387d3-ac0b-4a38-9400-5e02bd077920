/**
 * @description This component renders the Guarantee page for the Klusgebied platform. It clearly explains the Klusgebied Garantie, how it works, what is covered, and how to make a claim. The page is designed to build trust and confidence with users, featuring a professional layout, clear iconography, world-class animations, and a fully responsive design. Key variables include guarantee features and steps for making a claim.
 */
import React from "react";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
  ShieldCheck,
  CheckCircle,
  FileText,
  MessageSquare,
  ArrowRight,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const GuaranteePage = () => {
  usePageTitle("Garantie | Klusgebied");
  const navigate = useNavigate();

  const guaranteeFeatures = [
    {
      icon: CheckCircle,
      title: "Kwaliteitsgarantie",
      description:
        "Wij staan achter de kwaliteit van onze vak<PERSON>sen. Mocht het werk niet naar wens zijn, dan zoeken we samen naar een oplossing.",
    },
    {
      icon: Shield<PERSON>he<PERSON>,
      title: "Verzekerd Werk",
      description:
        "Alle klussen die via ons platform worden uitgevoerd, zijn verzekerd tegen onvoorziene schade.",
    },
    {
      icon: FileText,
      title: "Duidelijke Voorwaarden",
      description:
        "Onze garantievoorwaarden zijn helder en transparant, zodat u precies weet waar u aan toe bent.",
    },
  ];

  return (
    <div className="bg-white min-h-screen flex flex-col">
      <main className="flex-grow">
        <section className="bg-slate-900 text-white pt-32 pb-20 relative overflow-hidden">
          <div className="absolute inset-0 bg-grid-slate-800 [mask-image:linear-gradient(to_bottom,white,transparent)]"></div>
          <div className="container mx-auto px-4 text-center relative">
            <ShieldCheck className="mx-auto h-16 w-16 text-teal-400 mb-4" />
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              Klusgebied Garantie
            </h1>
            <p className="text-lg md:text-xl text-slate-300 max-w-3xl mx-auto">
              Uw klus, ons woord. Wij zorgen ervoor dat elke klus naar volle
              tevredenheid wordt uitgevoerd.
            </p>
          </div>
        </section>

        <section className="py-20 bg-slate-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-slate-800">
                Hoe onze garantie u beschermt
              </h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {guaranteeFeatures.map((feature, index) => (
                <div
                  key={index}
                  className="bg-white p-8 rounded-lg shadow-sm text-center"
                >
                  <feature.icon className="h-12 w-12 text-teal-500 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-slate-800 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-slate-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-20 bg-white">
          <div className="container mx-auto px-4 max-w-4xl">
            <h2 className="text-3xl font-bold text-center text-slate-800 mb-12">
              Een beroep doen op de garantie
            </h2>
            <div className="space-y-8">
              <div className="flex items-start">
                <div className="flex-shrink-0 h-12 w-12 rounded-full bg-teal-500 text-white flex items-center justify-center font-bold text-xl">
                  1
                </div>
                <div className="ml-6">
                  <h3 className="text-xl font-bold text-slate-800">
                    Neem contact op
                  </h3>
                  <p className="text-slate-600 mt-1">
                    Bent u niet tevreden over het uitgevoerde werk? Neem dan
                    binnen 14 dagen na afronding van de klus contact op met onze
                    klantenservice.
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0 h-12 w-12 rounded-full bg-teal-500 text-white flex items-center justify-center font-bold text-xl">
                  2
                </div>
                <div className="ml-6">
                  <h3 className="text-xl font-bold text-slate-800">
                    Documentatie aanleveren
                  </h3>
                  <p className="text-slate-600 mt-1">
                    Wij vragen u om de situatie te beschrijven en eventueel
                    foto's aan te leveren. Dit helpt ons om de situatie goed te
                    beoordelen.
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0 h-12 w-12 rounded-full bg-teal-500 text-white flex items-center justify-center font-bold text-xl">
                  3
                </div>
                <div className="ml-6">
                  <h3 className="text-xl font-bold text-slate-800">
                    Bemiddeling en oplossing
                  </h3>
                  <p className="text-slate-600 mt-1">
                    Ons team bemiddelt tussen u en de vakman om tot een passende
                    oplossing te komen. Indien nodig wordt het werk kosteloos
                    hersteld.
                  </p>
                </div>
              </div>
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/contact")}
                className="bg-teal-500 text-white font-bold py-3 px-8 rounded-lg hover:bg-teal-600 transition-colors duration-300 flex items-center mx-auto"
              >
                Start een garantieclaim <ArrowRight className="ml-2 h-5 w-5" />
              </button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default GuaranteePage;
