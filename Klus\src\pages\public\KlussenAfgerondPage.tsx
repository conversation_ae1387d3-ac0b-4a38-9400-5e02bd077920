/**
 * @description This component renders a dedicated page to showcase successfully completed jobs on the Klusgebied platform. It features a hero section, a filterable gallery of completed projects, and key statistics to build trust and credibility. The page is designed to be visually engaging, fully responsive, and provides a clear call-to-action for users to post their own jobs. Key variables include completedJobs data, filter states for job categories, and navigation handlers for exploring project details.
 */
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Footer from "../../components/landing/Footer";
import usePageTitle from "../../hooks/usePageTitle";
import {
  ArrowRight,
  CheckCircle,
  Star,
  Tag,
  Calendar,
  MapPin,
} from "lucide-react";

const completedJobs = [
  {
    id: 1,
    title: "Complete Badkamerrenovatie",
    category: "Loodgieter",
    location: "Amsterdam",
    date: "Juni 2025",
    imageUrl:
      "https://images.unsplash.com/photo-1721825172145-e87cfce84c88?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBiYXRocm9vbSUyQyUyMGx1eHVyeSUyMHJlbm92YXRpb24lMkMlMjB3YWxrLWluJTIwc2hvd2VyfGVufDB8fHx8MTc1MTUwODA4Mnww&ixlib=rb-4.1.0?w=1024&h=1024",
    description:
      "Een verouderde badkamer getransformeerd tot een moderne, luxe oase met inloopdouche en dubbele wastafel.",
    rating: 5,
  },
  {
    id: 2,
    title: "Buitenschilderwerk Herenhuis",
    category: "Schilder",
    location: "Utrecht",
    date: "Mei 2025",
    imageUrl:
      "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.0.3&amp;q=85&amp;fm=jpg&amp;crop=entropy&amp;cs=srgb&amp;w=600",
    description:
      "Het volledige exterieur van een klassiek herenhuis voorzien van een nieuwe, duurzame verflaag die jaren meegaat.",
    rating: 5,
  },
  {
    id: 3,
    title: "Installatie Nieuwe Groepenkast",
    category: "Elektricien",
    location: "Rotterdam",
    date: "Juli 2025",
    imageUrl:
      "https://images.unsplash.com/photo-1621905251189-08b45d6a269e?ixlib=rb-4.0.3&amp;q=85&amp;fm=jpg&amp;crop=entropy&amp;cs=srgb&amp;w=600",
    description:
      "Een verouderde stoppenkast vervangen door een moderne groepenkast met aardlekschakelaars voor extra veiligheid.",
    rating: 5,
  },
  {
    id: 4,
    title: "Maatwerk Inbouwkasten Slaapkamer",
    category: "Timmerman",
    location: "Den Haag",
    date: "Juni 2025",
    imageUrl:
      "https://images.unsplash.com/photo-1618220179428-22790b461013?ixlib=rb-4.0.3&amp;q=85&amp;fm=jpg&amp;crop=entropy&amp;cs=srgb&amp;w=600",
    description:
      "Perfect passende inbouwkasten ontworpen en gebouwd voor een slaapkamer, waardoor de opbergruimte is gemaximaliseerd.",
    rating: 4,
  },
  {
    id: 5,
    title: "Tuinontwerp en Aanleg",
    category: "Tuinman",
    location: "Eindhoven",
    date: "April 2025",
    imageUrl:
      "https://images.unsplash.com/photo-1750762286053-28632f48e717?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxnYXJkZW4lMjBkZXNpZ24lMkMlMjBsYW5kc2NhcGluZyUyQyUyMG91dGRvb3IlMjBzcGFjZXxlbnwwfHx8fDE3NTE1MDgwODJ8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    description:
      "Een complete tuin make-over, inclusief nieuw terras, beplanting en een sfeervol verlichtingsplan.",
    rating: 5,
  },
  {
    id: 6,
    title: "Daklekkage Verholpen",
    category: "Dakdekker",
    location: "Groningen",
    date: "Mei 2025",
    imageUrl:
      "https://images.unsplash.com/photo-1721493707262-0fc9e5794c27?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyb29mJTIwcmVwYWlyJTJDJTIwbGVhayUyMGRldGVjdGlvbiUyQyUyMGhvbWUlMjBtYWludGVuYW5jZXxlbnwwfHx8fDE3NTE1MDgwODJ8MA&ixlib=rb-4.1.0?w=1024&h=1024",
    description:
      "Een hardnekkige daklekkage opgespoord en vakkundig gerepareerd, inclusief vervanging van beschadigde dakpannen.",
    rating: 5,
  },
];

const categories = [
  "Alle",
  ...new Set(completedJobs.map((job) => job.category)),
];

const KlussenAfgerondPage = () => {
  usePageTitle("Klusgebied | Bekijk Onze Afgeronde Klussen");
  const navigate = useNavigate();
  const [filter, setFilter] = useState("Alle");

  const filteredJobs =
    filter === "Alle"
      ? completedJobs
      : completedJobs.filter((job) => job.category === filter);

  return (
    <div className="bg-slate-50 min-h-screen">
      <main>
        {/* Hero Section */}
        <section className="relative bg-gradient-to-b from-teal-600 to-teal-700 text-white py-24 sm:py-32 lg:py-40">
          <div className="absolute inset-0 bg-black opacity-20"></div>
          <div
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}
          ></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <div className="motion-preset-fade-down">
              <CheckCircle className="mx-auto h-16 w-16 text-white opacity-80 mb-4" />
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight">
                Afgeronde Klussen
              </h1>
              <p className="mt-6 max-w-3xl mx-auto text-lg sm:text-xl text-teal-100">
                Bekijk een selectie van de duizenden klussen die met trots zijn
                afgerond door onze geverifieerde vakmannen.
              </p>
            </div>
          </div>
        </section>

        {/* Filters and Gallery */}
        <section className="py-16 sm:py-24 lg:py-28 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Filter Buttons */}
            <div className="flex justify-center flex-wrap gap-2 sm:gap-3 mb-12 motion-preset-fade-up">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setFilter(category)}
                  className={`px-4 py-2 text-sm sm:text-base font-semibold rounded-full transition-all duration-300 ease-in-out transform hover:scale-105 ${
                    filter === category
                      ? "bg-teal-600 text-white shadow-lg"
                      : "bg-white text-slate-700 hover:bg-teal-50 shadow"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>

            {/* Gallery Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredJobs.map((job, index) => (
                <div
                  key={job.id}
                  className="bg-white rounded-2xl shadow-lg overflow-hidden group motion-preset-slide-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="relative">
                    <img
                      src={job.imageUrl}
                      alt={job.title}
                      className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-500"
                      loading="lazy"
                      width="400"
                      height="224"
                    />
                    <div className="absolute top-4 right-4 bg-teal-600 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-1">
                      <Star className="w-4 h-4" />
                      <span>{job.rating}.0</span>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center gap-2 text-sm text-slate-500 mb-2">
                      <Tag className="w-4 h-4 text-teal-500" />
                      <span>{job.category}</span>
                      <span className="text-slate-300">|</span>
                      <Calendar className="w-4 h-4 text-teal-500" />
                      <span>{job.date}</span>
                    </div>
                    <h3 className="text-xl font-bold text-slate-800 mb-2 group-hover:text-teal-600 transition-colors duration-300">
                      {job.title}
                    </h3>
                    <p className="text-slate-600 text-base leading-relaxed mb-4">
                      {job.description}
                    </p>
                    <div className="flex items-center text-sm text-slate-500">
                      <MapPin className="w-4 h-4 mr-2 text-teal-500" />
                      <span>{job.location}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-white">
          <div className="max-w-4xl mx-auto py-16 sm:py-24 px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl sm:text-4xl font-extrabold text-slate-800 tracking-tight">
              Heeft u zelf een klus?
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
              Plaats uw klus gratis en vrijblijvend op Klusgebied en ontvang
              reacties van de beste vakmannen in uw regio.
            </p>
            <div className="mt-8">
              <a
                href="https://klusgebied.nl/plaats-een-klus"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-full text-white bg-teal-600 hover:bg-teal-700 transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg"
              >
                Plaats uw klus <ArrowRight className="ml-2 -mr-1 h-5 w-5" />
              </a>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default KlussenAfgerondPage;
