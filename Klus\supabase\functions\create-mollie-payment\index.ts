const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const {
      amount,
      userId,
      transactionId,
      description,
      redirectUrl,
      webhookUrl,
    } = await req.json();

    const response = await fetch("https://api.mollie.com/v2/payments", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${Deno.env.get("MOLLIE_API_KEY")}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        amount: {
          value: amount.toFixed(2),
          currency: "EUR",
        },
        description,
        redirectUrl,
        webhookUrl,
        metadata: {
          userId,
          transactionId,
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Mollie API error:", errorData);
      throw new Error(
        `Mollie API error: ${errorData.detail || "Unknown error"}`
      );
    }

    const data = await response.json();
    console.log("Payment created successfully:", data.id);

    return new Response(
      JSON.stringify({
        paymentUrl: data._links.checkout.href,
        paymentId: data.id,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Error creating payment:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 400,
    });
  }
});
