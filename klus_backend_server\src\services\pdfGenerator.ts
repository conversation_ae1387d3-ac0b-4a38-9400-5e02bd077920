import puppeteer from 'puppeteer';

// Company constants
const COMPANY_INFO = {
  name: "Klusgebied B.V.",
  address: "Slotermeerlaan 58",
  postalCode: "1064 HC Amsterdam",
  kvk: "97419982",
  vestigingsnr: "000062663151",
  btw: "NL123456789B01",
  email: "<EMAIL>",
  website: "www.klusgebied.nl",
};

export const generateTransactionPDF = async (
  transaction: any,
  profile: any
): Promise<Buffer> => {
  console.log("Starting PDF generation for transaction:", transaction.id);

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    const html = generateTransactionHTML(transaction, profile);
    
    await page.setContent(html, { waitUntil: 'networkidle0' });
    
    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20mm',
        right: '20mm',
        bottom: '20mm',
        left: '20mm'
      }
    });

    console.log("PDF generation completed");
    return pdf;
  } finally {
    await browser.close();
  }
};

const generateTransactionHTML = (transaction: any, profile: any): string => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('nl-NL');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('nl-NL', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Betalingsbewijs</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          font-size: 12px;
          line-height: 1.4;
          color: #333;
          margin: 0;
          padding: 0;
        }
        .header {
          text-align: center;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 30px;
        }
        .company-info {
          margin-bottom: 30px;
        }
        .company-info h3 {
          margin: 0 0 10px 0;
          font-size: 14px;
        }
        .transaction-info {
          margin-bottom: 30px;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .payment-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        .payment-table th,
        .payment-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        .payment-table th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        .total-row {
          font-weight: bold;
          background-color: #f9f9f9;
        }
        .footer {
          margin-top: 40px;
          font-size: 10px;
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="header">
        BETALINGSBEWIJS / KWITANTIE
      </div>

      <div class="company-info">
        <h3>${COMPANY_INFO.name}</h3>
        <div>${COMPANY_INFO.address}</div>
        <div>${COMPANY_INFO.postalCode}</div>
        <div>KvK: ${COMPANY_INFO.kvk}</div>
        <div>Vestigingsnr: ${COMPANY_INFO.vestigingsnr}</div>
        <div>BTW: ${COMPANY_INFO.btw}</div>
        <div>E-mail: ${COMPANY_INFO.email}</div>
        <div>Website: ${COMPANY_INFO.website}</div>
      </div>

      <div class="transaction-info">
        <h3>Transactiegegevens</h3>
        <div class="info-row">
          <span>Transactie ID:</span>
          <span>${transaction.id}</span>
        </div>
        <div class="info-row">
          <span>Datum:</span>
          <span>${formatDate(transaction.created_at)}</span>
        </div>
        <div class="info-row">
          <span>Klant:</span>
          <span>${profile.first_name} ${profile.last_name}</span>
        </div>
        ${profile.company_name ? `
        <div class="info-row">
          <span>Bedrijf:</span>
          <span>${profile.company_name}</span>
        </div>
        ` : ''}
        <div class="info-row">
          <span>E-mail:</span>
          <span>${profile.email}</span>
        </div>
        <div class="info-row">
          <span>Status:</span>
          <span>${transaction.status}</span>
        </div>
      </div>

      <table class="payment-table">
        <thead>
          <tr>
            <th>Omschrijving</th>
            <th>Bedrag</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>${transaction.description || 'Saldo opwaardering'}</td>
            <td>${formatCurrency(transaction.amount)}</td>
          </tr>
          <tr class="total-row">
            <td><strong>Totaal</strong></td>
            <td><strong>${formatCurrency(transaction.amount)}</strong></td>
          </tr>
        </tbody>
      </table>

      <div class="footer">
        <p>Dit is een automatisch gegenereerd betalingsbewijs.</p>
        <p>Voor vragen kunt u contact opnemen via ${COMPANY_INFO.email}</p>
        <p>Gegenereerd op: ${new Date().toLocaleString('nl-NL')}</p>
      </div>
    </body>
    </html>
  `;
};
