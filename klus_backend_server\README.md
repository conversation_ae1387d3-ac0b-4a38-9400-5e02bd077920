# Klus Backend Server

Express.js backend server for the Klus application, converted from Supabase Edge Functions.

## Features

- **Payment Processing**: Mollie payment integration with webhooks
- **Communication**: Email (Resend) and SMS (MessageBird) services
- **User Management**: Admin user creation, deletion, and password reset
- **Utilities**: reCAPTCHA verification, geocoding, file uploads
- **Cron Jobs**: Schedule and manage cron jobs
- **HubSpot Integration**: Sync user profiles to HubSpot
- **PDF Generation**: Generate transaction PDFs

## Setup

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase project
- Required API keys (see Environment Variables)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

4. Configure your environment variables (see below)

5. Build the project:
   ```bash
   npm run build
   ```

6. Start the server:
   ```bash
   npm start
   ```

For development:
```bash
npm run dev
```

## Environment Variables

Copy `.env.example` to `.env` and configure the following:

### Required
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_ANON_KEY`: Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key

### Optional
- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment (development/production)
- `MOLLIE_API_KEY`: Mollie live API key
- `MOLLIE_TEST_API_KEY`: Mollie test API key
- `RESEND_API_KEY`: Resend email service API key
- `MESSAGEBIRD_WORKSPACE_ID`: MessageBird workspace ID
- `MESSAGEBIRD_CHANNEL_ID`: MessageBird channel ID
- `MESSAGEBIRD_ACCESS_KEY`: MessageBird access key
- `RECAPTCHA_SECRET_KEY`: Google reCAPTCHA secret key
- `HUBSPOT_API_KEY`: HubSpot API key
- `GOOGLE_MAPS_API_KEY`: Google Maps API key
- `SEND_SMS_HOOK_SECRET`: SMS webhook secret
- `SITE_URL`: Frontend site URL
- `BASE_URL`: Backend base URL

## API Endpoints

### Health Check
- `GET /health` - Server health status

### Payments
- `POST /api/payments/mollie/create` - Create Mollie payment
- `POST /api/payments/mollie/create-test` - Create test payment
- `POST /api/payments/mollie/webhook` - Mollie webhook handler
- `POST /api/payments/mollie/test-webhook` - Test webhook handler

### Communications
- `POST /api/communications/email/send` - Send email
- `POST /api/communications/sms/send` - Send SMS
- `POST /api/communications/sms/hook` - SMS webhook for 2FA

### Users
- `POST /api/users/admin/create` - Create admin user (admin only)
- `DELETE /api/users/admin/:id` - Delete admin user (admin only)
- `DELETE /api/users/:id` - Delete user (admin only)
- `POST /api/users/password-reset` - Send password reset email

### Utilities
- `POST /api/utilities/recaptcha/verify` - Verify reCAPTCHA token
- `POST /api/utilities/geocode` - Geocode address
- `POST /api/utilities/chat/upload` - Upload chat attachment

### Cron Jobs
- `POST /api/cron/schedule` - Schedule cron job (admin only)
- `DELETE /api/cron/unschedule` - Unschedule cron job (admin only)
- `POST /api/cron/schedule/get` - Get cron job schedule (admin only)

### HubSpot
- `POST /api/hubspot/sync` - Sync profiles to HubSpot
- `POST /api/hubspot/trigger-sync` - Trigger sync process

### PDF
- `POST /api/pdf/transaction` - Generate transaction PDF

## Authentication

Most endpoints require authentication via Bearer token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

Admin-only endpoints require the user to have `user_type: 'admin'` in their profile.

## Error Handling

The API returns consistent error responses:
```json
{
  "error": "Error message"
}
```

HTTP status codes:
- `200`: Success
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

## Development

### Scripts
- `npm run dev`: Start development server with hot reload
- `npm run build`: Build TypeScript to JavaScript
- `npm start`: Start production server
- `npm test`: Run tests
- `npm run lint`: Run ESLint
- `npm run lint:fix`: Fix ESLint issues

### Project Structure
```
src/
├── config/          # Configuration files
├── middleware/      # Express middleware
├── routes/          # API route handlers
├── services/        # Business logic services
└── server.ts        # Main server file
```

## Testing

Run tests with:
```bash
npm test
```

## Deployment

1. Build the project: `npm run build`
2. Set production environment variables
3. Start the server: `npm start`

For production deployment, consider using PM2 or Docker.

## Migration from Supabase Edge Functions

This server replaces the following Supabase Edge Functions:
- create-mollie-payment → `/api/payments/mollie/create`
- mollie-webhook → `/api/payments/mollie/webhook`
- send-email → `/api/communications/email/send`
- verify-recaptcha → `/api/utilities/recaptcha/verify`
- And many more...

Update your frontend to call these new endpoints instead of the Supabase functions.
