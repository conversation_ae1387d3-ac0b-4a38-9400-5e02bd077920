import { useEffect, useState } from "react";
import { Shield, AlertCircle } from "lucide-react";
import { useSet<PERSON>tom } from "jotai";

import { supabase } from "@/integrations/supabase/client";
import { Header } from "../layout/Header";
import { showMF<PERSON><PERSON>en<PERSON>tom } from "@/states/admin";
import { useAuth } from "@/components/auth/hooks/useAuth";
import { USER_TYPES } from "@/config/routes";

export default function AuthMFA() {
  const setShowMFAScreen = useSetAtom(showMFAScreenAtom);
  const { userProfile } = useAuth();

  const [verifyCode, setVerifyCode] = useState("");
  const [error, setError] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [factorId, setFactorId] = useState("");
  const [challengeId, setChallengeId] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");

  const isAdmin = userProfile?.user_type === USER_TYPES.ADMIN;

  useEffect(() => {
    startChallenge();
  }, []);

  const startChallenge = async () => {
    setError("");
    try {
      const factors = await supabase.auth.mfa.listFactors();
      if (factors.error) {
        throw factors.error;
      }

      if (isAdmin) {
        // For admin users, use TOTP authenticator
        const totpFactor = factors.data.totp[0];
        if (!totpFactor) {
          throw new Error("No TOTP factors found!");
        }
        const factorId = totpFactor.id;
        setFactorId(factorId);

        const challenge = await supabase.auth.mfa.challenge({ factorId });
        if (challenge.error) {
          setError(challenge.error.message);
          throw challenge.error;
        }
        setChallengeId(challenge.data.id);
      } else {
        // For non-admin users, use phone authenticator
        const phoneFactor = factors.data.phone[0];
        if (!phoneFactor) {
          throw new Error("No phone factors found!");
        }
        const factorId = phoneFactor.id;
        setFactorId(factorId);
        setPhoneNumber(`+${(phoneFactor as any).phone}`);

        const challenge = await supabase.auth.mfa.challenge({ factorId });
        if (challenge.error) {
          setError(challenge.error.message);
          throw challenge.error;
        }
        setChallengeId(challenge.data.id);
      }
    } catch (error) {
      setError(error.message);
    }
  };

  const handleVerifyCode = async () => {
    setError("");
    setIsVerifying(true);
    try {
      const verify = await supabase.auth.mfa.verify({
        factorId,
        challengeId,
        code: verifyCode,
      });
      if (verify.error) {
        setError(verify.error.message);
        throw verify.error;
      }

      setShowMFAScreen(false);
    } catch (error) {
      setError(error.message);
    } finally {
      setIsVerifying(false);
    }
  };

  const maskPhoneNumber = (phone: string) => {
    if (!phone) return "";
    // Keep the country code and last 2 digits visible
    const countryCode = phone.substring(0, 3); // Keep +XX
    const lastTwoDigits = phone.slice(-2);
    const maskedPart = "•".repeat(phone.length - 5); // Mask middle digits
    return `${countryCode} ${maskedPart} ${lastTwoDigits}`;
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header onLogout={async () => await supabase.auth.signOut()} />

      <main className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md space-y-6 p-6 bg-white rounded-xl shadow-lg">
          <div className="text-center space-y-2">
            <Shield className="w-12 h-12 text-primary mx-auto" />
            <h1 className="text-2xl font-bold text-gray-900">
              Two-Factor Authentication
            </h1>
            {isVerifying ? (
              <p className="text-gray-500">Loading verification details...</p>
            ) : (
              <>
                {isAdmin ? (
                  <p className="text-gray-500">
                    Voer de 6-cijferige code in van je authenticator-app
                  </p>
                ) : (
                  <>
                    <p className="text-gray-500">
                      Please enter the verification code sent to your phone
                    </p>
                    {phoneNumber && (
                      <p className="text-sm text-gray-600 font-medium">
                        {maskPhoneNumber(phoneNumber)}
                      </p>
                    )}
                  </>
                )}
              </>
            )}
          </div>

          {error && (
            <div className="flex items-center gap-2 p-3 text-sm text-red-500 bg-red-50 rounded-lg">
              <AlertCircle className="w-4 h-4" />
              <span>{error}</span>
            </div>
          )}

          <div className="space-y-4">
            <div className="space-y-2">
              <input
                type="text"
                maxLength={6}
                placeholder={isAdmin ? "000000" : "Enter 6-digit code"}
                inputMode="numeric"
                pattern="\d*"
                autoFocus
                className={`w-full px-4 py-3 text-center border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none transition ${
                  isAdmin
                    ? "text-3xl tracking-[0.5em] font-mono bg-gray-50"
                    : "text-lg tracking-widest font-mono"
                }`}
                value={verifyCode}
                onChange={(e) => {
                  const value = isAdmin
                    ? e.target.value.replace(/\D/g, "")
                    : e.target.value.trim();
                  setVerifyCode(value);
                }}
                disabled={isVerifying}
              />
              {isAdmin && (
                <p className="text-sm text-gray-500 text-center">
                  Geen code ontvangen? Controleer of de tijd op je apparaat
                  correct is
                </p>
              )}
            </div>

            <button
              onClick={handleVerifyCode}
              disabled={verifyCode.length !== 6 || isVerifying}
              className="w-full py-3 px-4 bg-primary text-white rounded-lg font-medium hover:bg-primary/90 focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition inline-flex items-center justify-center gap-2"
            >
              {isVerifying ? (
                <>
                  <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  {isAdmin ? "Verifiëren..." : "Verifying..."}
                </>
              ) : (
                <>
                  <Shield className="w-4 h-4" />
                  {isAdmin ? "Verifiëren" : "Verify Code"}
                </>
              )}
            </button>
          </div>

          {!isAdmin && (
            <p className="text-center text-sm text-gray-500">
              Didn't receive the code?{" "}
              <button
                onClick={startChallenge}
                className="text-primary hover:underline font-medium"
                disabled={isVerifying}
              >
                Send again
              </button>
            </p>
          )}
        </div>
      </main>
    </div>
  );
}
