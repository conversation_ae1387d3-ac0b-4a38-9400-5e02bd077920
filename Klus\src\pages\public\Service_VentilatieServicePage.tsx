/**
 * @description This component renders a comprehensive and SEO-optimized detail page for ventilation services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for ventilation services. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import PricingSection from "@/components/landing/PricingSection";
import {
  <PERSON><PERSON><PERSON>t,
  Wind,
  Droplets,
  EyeOff,
  ArrowRight,
  Edit3,
  <PERSON><PERSON><PERSON>re,
  <PERSON>r<PERSON><PERSON><PERSON>,
  CheckCircle,
  MapPin,
} from "lucide-react";

const Service_VentilatieServicePage = () => {
  usePageTitle(
    "Ventilatie Service | Klusgebied - Voor een Gezond Binnenklimaat"
  );
  const navigate = useNavigate();

  const services = [
    {
      icon: Wind,
      title: "Mechanische Ventilatie Installatie",
      description:
        "Installatie van een MV-box voor een constante afvoer van vervuilde lucht.",
      points: [
        "Voert continu vervuilde en vochtige lucht af.",
        "Voorkomt schimmel en verbetert luchtkwaliteit.",
        "Installatie van energiezuinige en stille MV-boxen.",
        "Ideaal voor keuken, badkamer en toilet.",
      ],
    },
    {
      icon: Droplets,
      title: "WTW-unit Onderhoud",
      description: "Onderhoud en reiniging van uw Warmte-Terug-Win-systeem.",
      points: [
        "Hergebruikt warmte voor maximale energie-efficiëntie.",
        "Jaarlijks onderhoud en filterreiniging is essentieel.",
        "Zorgt voor een optimale en gezonde werking.",
        "Verlengt de levensduur van uw WTW-unit.",
      ],
    },
    {
      icon: EyeOff,
      title: "Ventilatiekanalen Reinigen",
      description:
        "Professionele reiniging van de kanalen voor een gezonde luchtstroom.",
      points: [
        "Verwijdert stof, vuil en bacteriën uit de kanalen.",
        "Verbetert de luchtkwaliteit aanzienlijk.",
        "Vermindert allergieën en gezondheidsklachten.",
        "Aanbevolen om de 4-6 jaar.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <EyeOff className="w-8 h-8 text-white" />,
      title: "Gezond Binnenklimaat",
      description:
        "Continue aanvoer van frisse lucht en afvoer van vocht, CO2 en schadelijke stoffen.",
    },
    {
      icon: <Droplets className="w-8 h-8 text-white" />,
      title: "Voorkomt Vocht & Schimmel",
      description:
        "Goede ventilatie is essentieel om vochtproblemen en schimmelvorming te voorkomen.",
    },
    {
      icon: <Wind className="w-8 h-8 text-white" />,
      title: "Energiezuinig",
      description:
        "Moderne WTW-systemen ventileren zonder significant warmteverlies.",
    },
  ];

  const faqs = [
    {
      question: "Waarom is ventileren zo belangrijk?",
      answer:
        "In moderne, goed geïsoleerde huizen kan vervuilde lucht niet ontsnappen. Ventileren is cruciaal voor de afvoer van vocht, CO2 en schadelijke stoffen, en de aanvoer van verse, zuurstofrijke lucht. Dit is essentieel voor uw gezondheid.",
    },
    {
      question:
        "Wat is het verschil tussen mechanische ventilatie en een WTW-unit?",
      answer:
        "Een mechanisch ventilatiesysteem voert alleen lucht af; verse lucht komt binnen via roosters. Een WTW-unit voert lucht af én brengt verse lucht actief naar binnen, waarbij de warmte van de binnenlucht wordt overgedragen aan de koude buitenlucht.",
    },
    {
      question: "Hoe vaak moet ik mijn ventilatiesysteem laten onderhouden?",
      answer:
        "Het wordt aangeraden om de filters van een WTW-unit elke 3-6 maanden te vervangen. Een grote onderhoudsbeurt en reiniging van de kanalen wordt geadviseerd om de 4-6 jaar.",
    },
    {
      question: "Maakt een ventilatiesysteem veel geluid?",
      answer:
        "Moderne ventilatiesystemen zijn zeer stil. Als uw systeem veel lawaai maakt, kan dit duiden op vervuiling of een defect. Laat dit dan controleren door een specialist.",
    },
  ];

  const reviews = [
    {
      name: "Familie de Wit",
      location: "Amsterdam",
      rating: 5,
      quote:
        "De lucht in huis is zoveel frisser sinds de installatie van het nieuwe ventilatiesysteem. Geen last meer van condens op de ramen.",
      highlighted: true,
    },
    {
      name: "Jeroen Bakker",
      location: "Utrecht",
      rating: 5,
      quote:
        "De ventilatiekanalen zijn professioneel gereinigd. Het verschil is echt merkbaar. Veel minder stof in huis.",
      highlighted: false,
    },
    {
      name: "Linda Verhoeven",
      location: "Eindhoven",
      rating: 4,
      quote:
        "Goed advies gekregen over de beste oplossing voor ons huis. De installatie was netjes en snel.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1581602989918-8d61dcb04417?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx2ZW50aWxhdGlvbiUyMHN5c3RlbSUyQyUyMG1vZGVybiUyMGFpciUyMHZlbnQlMkMlMjBpbmRvb3IlMjBhaXIlMjBxdWFsaXR5fGVufDB8fHx8MTc1MTc0MjQ5Nnww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1617861648989-76a572012089?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx2ZW50aWxhdGlvbiUyMGluc3RhbGxhdGlvbiUyQyUyMGFpciUyMHF1YWxpdHklMjBpbXByb3ZlbWVudCUyQyUyMEhWQUMlMjBzeXN0ZW18ZW58MHx8fHwxNzUxNzQyNDk2fDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1600585152220-90363fe7e115?ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description: "Beschrijf uw ventilatiebehoefte of probleem.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Specialisten reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde ventilatiespecialisten.",
      microcopy: "Binnen 24 uur reacties",
    },
    {
      icon: UserCheck,
      title: "Kies & start",
      description:
        "Vergelijk profielen en kies de beste specialist voor uw klus.",
      microcopy: "Vergelijk profielen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Ventilatiespecialisten in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "ventilatiespecialist",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Ventilatie Service | Klusgebied - Voor een Gezond Binnenklimaat
        </title>
        <meta
          name="description"
          content="Zorg voor een fris en gezond binnenklimaat met een professioneel geïnstalleerd en onderhouden ventilatiesysteem. Vind een specialist via Klusgebied."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-cyan-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-cyan-100 border border-cyan-200/80 rounded-full px-4 py-2 mb-6">
                    <Wind className="w-5 h-5 text-cyan-600" />
                    <span className="text-cyan-800 font-semibold text-sm">
                      Ventilatie Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Ventilatie Service?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-500 to-blue-600 mt-2">
                      Voor een Gezond Huis
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Zorg voor een fris en gezond binnenklimaat met een
                    professioneel geïnstalleerd en onderhouden
                    ventilatiesysteem.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate(
                          "/plaats-een-klus/ventilatie-vervangen-of-reinigen"
                        )
                      }
                      className="group inline-flex items-center justify-center bg-cyan-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-cyan-600 transition-all duration-300 shadow-lg hover:shadow-cyan-500/30 transform hover:-translate-y-1"
                    >
                      Vind jouw ventilatiespecialist
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1581602989918-8d61dcb04417?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx2ZW50aWxhdGlvbiUyMHN5c3RlbSUyQyUyMG1vZGVybiUyMGFpciUyMHZlbnQlMkMlMjBpbmRvb3IlMjBhaXIlMjBxdWFsaXR5fGVufDB8fHx8MTc1MTc0MjQ5Nnww&ixlib=rb-4.1.0?w=1024&h=1024"
                    alt="Een modern ventilatierooster in een plafond"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte specialist voor uw
                ventilatie.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-cyan-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-cyan-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Ventilatie Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Complete oplossingen voor een gezonde leefomgeving.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-cyan-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-cyan-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-cyan-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <PricingSection
          serviceName="ventilatiespecialist"
          themeColor="cyan"
          priceItems={[
            { label: "Onderhoudsbeurt:", value: "€120–€180", unit: "" },
            { label: "Kanalen reinigen:", value: "Vanaf €250", unit: "" },
            {
              label: "Nieuwe MV-box:",
              value: "€350–€500",
              unit: "(incl. montage)",
            },
          ]}
        />

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Voordelen van Goede Ventilatie
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Investeer in uw gezondheid en comfort met een professioneel
                systeem.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-cyan-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-cyan-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-cyan-500 to-blue-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Adem frisse, gezonde lucht in huis.
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Vraag een vrijblijvend adviesgesprek aan en ontdek de beste
              ventilatieoplossing voor uw woning.
            </p>
            <button
              onClick={() =>
                navigate("/plaats-een-klus/ventilatie-vervangen-of-reinigen")
              }
              className="bg-white text-cyan-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu advies aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() =>
            navigate("/plaats-een-klus/ventilatie-vervangen-of-reinigen")
          }
          className="w-full group inline-flex items-center justify-center bg-cyan-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-cyan-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_VentilatieServicePage;
