#!/usr/bin/env node

/**
 * SSL Certificate Testing and Validation Script
 * Tests SSL implementation for klusgebied.nl and admin.klusgebied.nl
 * Validates security headers, redirects, and SEO optimization
 */

import https from "https";
import http from "http";
import { URL } from "url";
import { fileURLToPath } from "url";

const domains = ["klusgebied.nl", "admin.klusgebied.nl", "www.klusgebied.nl"];

const testUrls = [
  "https://klusgebied.nl/",
  "https://admin.klusgebied.nl/",
  "https://klusgebied.nl/ventilatie",
  "https://klusgebied.nl/badkamer",
  "https://klusgebied.nl/inloggen",
];

const requiredSecurityHeaders = [
  "strict-transport-security",
  "x-content-type-options",
  "x-frame-options",
  "x-xss-protection",
  "referrer-policy",
];

const seoHeaders = [
  "x-robots-tag",
  "link", // for canonical URLs
];

// Colors for console output
const colors = {
  green: "\x1b[32m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  reset: "\x1b[0m",
  bold: "\x1b[1m",
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function testHttpsRedirect(domain) {
  return new Promise((resolve) => {
    const options = {
      hostname: domain,
      port: 80,
      path: "/",
      method: "GET",
      timeout: 5000,
    };

    const req = http.request(options, (res) => {
      const isRedirect = res.statusCode >= 300 && res.statusCode < 400;
      const location = res.headers.location;
      const isHttpsRedirect = location && location.startsWith("https://");

      resolve({
        success: isRedirect && isHttpsRedirect,
        statusCode: res.statusCode,
        location: location,
        message: isHttpsRedirect
          ? "HTTP to HTTPS redirect working"
          : "HTTP redirect not properly configured",
      });
    });

    req.on("error", (err) => {
      resolve({
        success: false,
        error: err.message,
        message: "HTTP request failed",
      });
    });

    req.on("timeout", () => {
      req.destroy();
      resolve({
        success: false,
        message: "HTTP request timeout",
      });
    });

    req.end();
  });
}

function testHttpsConnection(url) {
  return new Promise((resolve) => {
    const urlObj = new URL(url);

    const options = {
      hostname: urlObj.hostname,
      port: 443,
      path: urlObj.pathname,
      method: "GET",
      timeout: 10000,
      rejectUnauthorized: true, // This will fail if SSL certificate is invalid
    };

    const req = https.request(options, (res) => {
      const headers = res.headers;
      const securityHeaders = {};
      const seoHeadersFound = {};

      // Check security headers
      requiredSecurityHeaders.forEach((header) => {
        securityHeaders[header] = headers[header] || null;
      });

      // Check SEO headers
      seoHeaders.forEach((header) => {
        seoHeadersFound[header] = headers[header] || null;
      });

      let body = "";
      res.on("data", (chunk) => {
        body += chunk;
      });

      res.on("end", () => {
        resolve({
          success: true,
          statusCode: res.statusCode,
          headers: headers,
          securityHeaders: securityHeaders,
          seoHeaders: seoHeadersFound,
          hasCanonical: body.includes('rel="canonical"'),
          hasHSTS: !!headers["strict-transport-security"],
          certificate: res.socket ? res.socket.getPeerCertificate() : null,
          message: "HTTPS connection successful",
        });
      });
    });

    req.on("error", (err) => {
      resolve({
        success: false,
        error: err.message,
        message: "HTTPS connection failed",
      });
    });

    req.on("timeout", () => {
      req.destroy();
      resolve({
        success: false,
        message: "HTTPS request timeout",
      });
    });

    req.end();
  });
}

function validateSSLCertificate(cert) {
  if (!cert || !cert.subject) {
    return { valid: false, message: "No certificate information available" };
  }

  const now = new Date();
  const validFrom = new Date(cert.valid_from);
  const validTo = new Date(cert.valid_to);

  const isValid = now >= validFrom && now <= validTo;
  const daysUntilExpiry = Math.ceil((validTo - now) / (1000 * 60 * 60 * 24));

  return {
    valid: isValid,
    subject: cert.subject.CN,
    issuer: cert.issuer.CN,
    validFrom: validFrom.toISOString(),
    validTo: validTo.toISOString(),
    daysUntilExpiry: daysUntilExpiry,
    message: isValid
      ? `Certificate valid for ${daysUntilExpiry} more days`
      : "Certificate expired or not yet valid",
  };
}

async function runSSLTests() {
  log(
    `${colors.bold}🔒 SSL Certificate and Security Testing for Klusgebied${colors.reset}\n`
  );

  let allTestsPassed = true;

  // Test HTTPS redirects
  log(`${colors.blue}Testing HTTP to HTTPS redirects...${colors.reset}`);
  for (const domain of domains) {
    const result = await testHttpsRedirect(domain);
    if (result.success) {
      log(`✅ ${domain}: ${result.message}`, colors.green);
    } else {
      log(`❌ ${domain}: ${result.message}`, colors.red);
      allTestsPassed = false;
    }
  }

  log(""); // Empty line

  // Test HTTPS connections and security headers
  log(
    `${colors.blue}Testing HTTPS connections and security headers...${colors.reset}`
  );
  for (const url of testUrls) {
    log(`\n${colors.yellow}Testing: ${url}${colors.reset}`);

    const result = await testHttpsConnection(url);

    if (result.success) {
      log(
        `✅ HTTPS connection successful (${result.statusCode})`,
        colors.green
      );

      // Validate SSL certificate
      if (result.certificate) {
        const certValidation = validateSSLCertificate(result.certificate);
        if (certValidation.valid) {
          log(`✅ SSL Certificate: ${certValidation.message}`, colors.green);
          log(`   Subject: ${certValidation.subject}`);
          log(`   Issuer: ${certValidation.issuer}`);
        } else {
          log(`❌ SSL Certificate: ${certValidation.message}`, colors.red);
          allTestsPassed = false;
        }
      }

      // Check security headers
      log(`${colors.blue}Security Headers:${colors.reset}`);
      requiredSecurityHeaders.forEach((header) => {
        const value = result.securityHeaders[header];
        if (value) {
          log(`✅ ${header}: ${value}`, colors.green);
        } else {
          log(`❌ ${header}: Missing`, colors.red);
          allTestsPassed = false;
        }
      });

      // Check SEO headers
      log(`${colors.blue}SEO Headers:${colors.reset}`);
      if (result.hasCanonical) {
        log(`✅ Canonical URL found in HTML`, colors.green);
      } else {
        log(`⚠️  Canonical URL not found in HTML`, colors.yellow);
      }

      if (result.seoHeaders["x-robots-tag"]) {
        log(
          `✅ X-Robots-Tag: ${result.seoHeaders["x-robots-tag"]}`,
          colors.green
        );
      } else {
        log(`⚠️  X-Robots-Tag: Not set`, colors.yellow);
      }
    } else {
      log(`❌ HTTPS connection failed: ${result.message}`, colors.red);
      if (result.error) {
        log(`   Error: ${result.error}`, colors.red);
      }
      allTestsPassed = false;
    }
  }

  // Final summary
  log(`\n${colors.bold}=== SSL Test Summary ===${colors.reset}`);
  if (allTestsPassed) {
    log(
      `🎉 All SSL tests passed! Your site is properly secured for SEO.`,
      colors.green
    );
  } else {
    log(
      `⚠️  Some SSL tests failed. Please review the issues above.`,
      colors.yellow
    );
  }

  // SEO recommendations
  log(`\n${colors.bold}SEO Recommendations:${colors.reset}`);
  log(`✅ Ensure all URLs use HTTPS`);
  log(`✅ Implement HSTS headers`);
  log(`✅ Add canonical URLs to all pages`);
  log(`✅ Submit updated sitemap to Google Search Console`);
  log(`✅ Update any hardcoded HTTP links to HTTPS`);
  log(`✅ Test site speed with SSL enabled`);

  return allTestsPassed;
}

// Run the tests
const __filename = fileURLToPath(import.meta.url);

// Check if this script is being run directly
if (process.argv[1] === __filename) {
  runSSLTests()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((err) => {
      console.error("Test runner error:", err);
      process.exit(1);
    });
}

export { runSSLTests };
