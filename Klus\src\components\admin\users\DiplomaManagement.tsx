import React, { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { FileText, Check, X, Edit2, Trash2, Award } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

import { supabase } from "@/integrations/supabase/client";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";

export interface Diploma {
  id: string;
  url: string;
  name: string;
  status: "pending" | "approved" | "rejected";
  type: "image" | "doc";
  uploadDate: string;
}

interface DiplomaManagementProps {
  userInfo: {
    id: string;
    first_name: string;
    last_name: string;
    diplomas?: Diploma[];
  };
  onDiplomasUpdate?: () => void;
}

const DiplomaManagement: React.FC<DiplomaManagementProps> = ({
  userInfo,
  onDiplomasUpdate,
}) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newName, setNewName] = useState("");

  const updateDiplomaStatus = async (
    diplomaId: string,
    status: "approved" | "rejected"
  ) => {
    if (!userInfo.diplomas) throw new Error("No diplomas found");

    const updatedDiplomas = userInfo.diplomas.map((diploma) =>
      diploma.id === diplomaId ? { ...diploma, status } : diploma
    );

    const { error } = await supabase
      .from("profiles")
      .update({ diplomas: updatedDiplomas as any })
      .eq("id", userInfo.id);

    if (error) throw error;
  };

  const updateDiplomaName = async (diplomaId: string, name: string) => {
    if (!userInfo.diplomas) throw new Error("No diplomas found");

    const updatedDiplomas = userInfo.diplomas.map((diploma) =>
      diploma.id === diplomaId ? { ...diploma, name } : diploma
    );

    const { error } = await supabase
      .from("profiles")
      .update({ diplomas: updatedDiplomas as any })
      .eq("id", userInfo.id);

    if (error) throw error;
  };

  const deleteDiploma = async (diplomaId: string) => {
    if (!userInfo.diplomas) throw new Error("No diplomas found");

    const updatedDiplomas = userInfo.diplomas.filter(
      (diploma) => diploma.id !== diplomaId
    );

    const { error } = await supabase
      .from("profiles")
      .update({ diplomas: updatedDiplomas as any })
      .eq("id", userInfo.id);

    if (error) throw error;
  };

  const { mutate: updateStatus, isPending: isUpdatingStatus } = useMutation({
    mutationFn: ({
      diplomaId,
      status,
    }: {
      diplomaId: string;
      status: "approved" | "rejected";
    }) => updateDiplomaStatus(diplomaId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profiles"] });
      onDiplomasUpdate?.();
      toast({
        title: "Status bijgewerkt",
        description: "De diploma status is succesvol bijgewerkt.",
      });
    },
    onError: (error) => {
      toast({
        title: "Fout",
        description:
          "Er is een fout opgetreden bij het bijwerken van de status.",
        variant: "destructive",
      });
      console.error("Error updating diploma status:", error);
    },
  });

  const { mutate: updateName, isPending: isUpdatingName } = useMutation({
    mutationFn: ({ diplomaId, name }: { diplomaId: string; name: string }) =>
      updateDiplomaName(diplomaId, name),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profiles"] });
      onDiplomasUpdate?.();
      setEditingId(null);
      setNewName("");
      toast({
        title: "Naam bijgewerkt",
        description: "De diploma naam is succesvol bijgewerkt.",
      });
    },
    onError: (error) => {
      toast({
        title: "Fout",
        description: "Er is een fout opgetreden bij het bijwerken van de naam.",
        variant: "destructive",
      });
      console.error("Error updating diploma name:", error);
    },
  });

  const { mutate: deleteDiplomaItem, isPending: isDeleting } = useMutation({
    mutationFn: ({ diplomaId }: { diplomaId: string }) =>
      deleteDiploma(diplomaId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profiles"] });
      onDiplomasUpdate?.();
      toast({
        title: "Diploma verwijderd",
        description: "Het diploma is succesvol verwijderd.",
      });
    },
    onError: (error) => {
      toast({
        title: "Fout",
        description:
          "Er is een fout opgetreden bij het verwijderen van het diploma.",
        variant: "destructive",
      });
      console.error("Error deleting diploma:", error);
    },
  });

  const handleStatusUpdate = (
    diplomaId: string,
    status: "approved" | "rejected"
  ) => {
    updateStatus({ diplomaId, status });
  };

  const handleNameUpdate = (diplomaId: string) => {
    if (!newName.trim()) {
      toast({
        title: "Fout",
        description: "Voer een geldige naam in.",
        variant: "destructive",
      });
      return;
    }
    updateName({ diplomaId, name: newName.trim() });
  };

  const handleDelete = (diplomaId: string) => {
    if (window.confirm("Weet je zeker dat je dit diploma wilt verwijderen?")) {
      deleteDiplomaItem({ diplomaId });
    }
  };

  const startEditing = (diploma: Diploma) => {
    setEditingId(diploma.id);
    setNewName(diploma.name);
  };

  const cancelEditing = () => {
    setEditingId(null);
    setNewName("");
  };

  if (!userInfo.diplomas || userInfo.diplomas.length === 0) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Award className="h-5 w-5 text-muted-foreground" />
            <CardTitle className="text-base">
              Diploma's & Certificaten
            </CardTitle>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Geen documenten beschikbaar
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-sm text-muted-foreground">
              Deze vakman heeft nog geen diploma's of certificaten geüpload
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Award className="h-5 w-5 text-primary" />
            <CardTitle className="text-base">
              Diploma's & Certificaten
            </CardTitle>
          </div>
          <p className="text-sm text-muted-foreground">
            {userInfo.diplomas.length} document
            {userInfo.diplomas.length === 1 ? "" : "en"}
          </p>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
          <AnimatePresence>
            {userInfo.diplomas.map((diploma) => (
              <motion.div
                key={diploma.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="group border rounded-lg overflow-hidden hover:shadow-md transition-all duration-200 bg-card"
              >
                {/* Document Preview */}
                <div className="relative">
                  {diploma.type === "image" ? (
                    <div className="aspect-video relative overflow-hidden">
                      <img
                        src={diploma.url}
                        alt={diploma.name}
                        className="object-cover w-full h-full transition-transform group-hover:scale-105"
                      />
                    </div>
                  ) : (
                    <div className="aspect-video bg-muted flex items-center justify-center">
                      <FileText size={48} className="text-muted-foreground" />
                    </div>
                  )}
                  <div className="absolute top-2 right-2">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium shadow-sm
                        ${
                          diploma.status === "approved"
                            ? "bg-green-100 text-green-700 border border-green-200"
                            : diploma.status === "rejected"
                            ? "bg-red-100 text-red-700 border border-red-200"
                            : "bg-yellow-100 text-yellow-700 border border-yellow-200"
                        }`}
                    >
                      {diploma.status === "approved"
                        ? "Goedgekeurd"
                        : diploma.status === "rejected"
                        ? "Afgewezen"
                        : "In behandeling"}
                    </span>
                  </div>
                </div>

                {/* Document Info and Actions */}
                <div className="p-4 space-y-3">
                  {/* Document Name */}
                  <div className="space-y-2">
                    {editingId === diploma.id ? (
                      <div className="flex items-center gap-2">
                        <Input
                          value={newName}
                          onChange={(e) => setNewName(e.target.value)}
                          className="flex-1 text-sm"
                          autoFocus
                          disabled={isUpdatingName}
                        />
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={cancelEditing}
                            disabled={isUpdatingName}
                            className="px-2"
                          >
                            <X size={14} />
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleNameUpdate(diploma.id)}
                            disabled={isUpdatingName || !newName.trim()}
                            className="px-2"
                          >
                            <Check size={14} />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <a
                          href={diploma.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm font-medium hover:text-primary hover:underline truncate flex-1 mr-2"
                        >
                          {diploma.name}
                        </a>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => startEditing(diploma)}
                          disabled={isUpdatingName}
                          className="px-2 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <Edit2 size={14} />
                        </Button>
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Geüpload op{" "}
                      {new Date(diploma.uploadDate).toLocaleDateString("nl-NL")}
                    </p>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                    <div className="flex gap-2 sm:flex-1">
                      <Button
                        size="sm"
                        variant={
                          diploma.status === "approved" ? "default" : "outline"
                        }
                        className="flex-1 text-xs"
                        onClick={() =>
                          handleStatusUpdate(diploma.id, "approved")
                        }
                        disabled={
                          isUpdatingStatus || diploma.status === "approved"
                        }
                      >
                        <Check size={14} className="mr-1" />
                        Goedkeuren
                      </Button>
                      <Button
                        size="sm"
                        variant={
                          diploma.status === "rejected"
                            ? "destructive"
                            : "outline"
                        }
                        className="flex-1 text-xs"
                        onClick={() =>
                          handleStatusUpdate(diploma.id, "rejected")
                        }
                        disabled={
                          isUpdatingStatus || diploma.status === "rejected"
                        }
                      >
                        <X size={14} className="mr-1" />
                        Afwijzen
                      </Button>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(diploma.id)}
                      disabled={isDeleting}
                      className="sm:px-2 text-destructive hover:text-destructive sm:w-auto w-full"
                    >
                      <Trash2 size={14} className="sm:mr-0 mr-2" />
                      <span className="sm:hidden">Verwijderen</span>
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </CardContent>
    </Card>
  );
};

export default DiplomaManagement;
