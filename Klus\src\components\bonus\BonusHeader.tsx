import { Coins } from "lucide-react";

import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { BackToDashboard } from "@/components/BackToDashboard";

interface BonusHeaderProps {
  profile: any;
  isLoading: boolean;
}

export const BonusHeader = ({ profile, isLoading }: BonusHeaderProps) => {
  return (
    <>
      <BackToDashboard />
      <Card className="p-4 sm:p-6 mb-6 sm:mb-8 bg-gradient-to-br from-primary/20 via-accent/10 to-primary/5 border-primary/10 hover:shadow-lg hover:shadow-primary/5 transition-all duration-300">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-gradient-to-br from-primary to-accent rounded-lg transform hover:scale-105 transition-transform duration-300">
            <Coins className="h-6 w-6 sm:h-8 sm:w-8 text-white animate-pulse" />
          </div>
          <div className="space-y-1">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
              {isLoading ? (
                <Skeleton className="h-8 w-32" />
              ) : (
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">
                  {profile?.balance || 0} Credits
                </span>
              )}
            </h2>
            <p className="text-gray-600 dark:text-gray-300 font-medium">
              Beschikbaar saldo
            </p>
          </div>
        </div>
      </Card>
    </>
  );
};
