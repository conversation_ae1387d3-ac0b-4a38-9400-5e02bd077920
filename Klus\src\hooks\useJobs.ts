import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

const fetchJobs = async (
  userId: string | null,
  userType: string | null,
  userServices: string[] | null = null,
  page: number = 1,
  perPage: number = 10
) => {
  if (!userId || !userType) {
    return [];
  }

  const startIndex = (page - 1) * perPage;

  let query = supabase
    .from("jobs")
    .select(
      `
      *,
      profiles!jobs_user_id_fkey (
        first_name,
        last_name,
        email,
        city
      )
    `
    )
    .is("deleted_at", null)
    .order("created_at", { ascending: false });

  if (userType === "vakman") {
    query = query
      .neq("status", "pending")
      .range(startIndex, startIndex + perPage - 1);
  } else if (userType === "klusaanvrager") {
    query = query.eq("user_id", userId);
  }

  const { data: jobsData, error } = await query;

  if (error) {
    console.error("Error fetching jobs:", error);
    throw error;
  }

  let filteredJobs = jobsData || [];

  // Filter jobs by services for craftman users
  if (userType === "vakman") {
    // If user has no services configured, show no jobs
    if (!userServices || userServices.length === 0) {
      filteredJobs = [];
    } else {
      // Filter jobs to only show those matching user's services
      filteredJobs = filteredJobs.filter((job) => {
        // Check if job has services and if there's any overlap with user services
        if (
          !job.services ||
          !Array.isArray(job.services) ||
          job.services.length === 0
        ) {
          return false;
        }

        // Return true if there's at least one matching service
        return job.services.some((jobService) =>
          userServices.includes(jobService)
        );
      });
    }
  }

  return (
    filteredJobs?.map((job) => ({
      ...job,
      photos: Array.isArray(job.photos)
        ? job.photos.map((photo) => String(photo))
        : [],
      details: {
        ...((job.details as { job_type?: string; category?: string }) || {}),
      },
    })) || []
  );
};

export const useJobs = (
  userType: string | null,
  userId: string | null,
  userServices: string[] | null = null,
  page: number = 1,
  perPage: number = 10
) => {
  const { toast } = useToast();

  const {
    data: jobs = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["jobs", userId, userType, userServices, page, perPage],
    queryFn: () => fetchJobs(userId, userType, userServices, page, perPage),
    enabled: Boolean(userId && userType),
    staleTime: 30000,
    gcTime: 5 * 60 * 1000,
    retry: 1,
    meta: {
      errorMessage: "Er is een fout opgetreden bij het ophalen van de klussen.",
    },
  });

  if (error) {
    console.error("Error in useJobs:", error);
    toast({
      variant: "destructive",
      title: "Fout bij ophalen klussen",
      description:
        error instanceof Error
          ? error.message
          : "Er is een fout opgetreden bij het ophalen van de klussen.",
    });
  }

  return {
    jobs,
    loading: isLoading,
    refetchJobs: refetch,
  };
};
