import { User, Mail, Phone, Receipt, Briefcase, MapPin } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { TransactionHistory } from "@/components/balance/TransactionHistory";
import { supabase } from "@/integrations/supabase/client";
import { Transaction } from "@/types/database";

interface VakmanDetailsDialogProps {
  vakman: any;
  isOpen: boolean;
  onClose: () => void;
}

export const VakmanDetailsDialog = ({
  vakman,
  isOpen,
  onClose,
}: VakmanDetailsDialogProps) => {
  const { data: transactions, isLoading } = useQuery({
    queryKey: ["vakman-transactions", vakman?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("balance_transactions")
        .select("*")
        .eq("user_id", vakman?.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching transactions:", error);
        throw error;
      }

      return data as Transaction[];
    },
    enabled: !!vakman?.id && isOpen,
    staleTime: 0,
    gcTime: 0,
  });

  if (!vakman) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Vakman Details</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header with Avatar and Name */}
          <div className="flex items-start gap-4">
            <Avatar className="h-20 w-20">
              {vakman.profile_photo_url ? (
                <AvatarImage src={vakman.profile_photo_url} />
              ) : (
                <AvatarFallback className="bg-primary">
                  <User className="h-10 w-10 text-white" />
                </AvatarFallback>
              )}
            </Avatar>
            <div>
              <h2 className="text-2xl font-bold">
                {vakman.company_name ||
                  `${vakman.first_name} ${vakman.last_name}`}
              </h2>
              {vakman.company_name && (
                <p className="text-muted-foreground">
                  {vakman.first_name} {vakman.last_name}
                </p>
              )}
            </div>
          </div>

          <Separator />

          {/* Contact Information */}
          <div className="space-y-2">
            <h3 className="font-semibold text-lg">Contactgegevens</h3>
            <div className="grid gap-2">
              {vakman.email && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  <span>{vakman.email}</span>
                </div>
              )}
              {vakman.phone_number && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  <span>{vakman.phone_number}</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Business Information */}
          <div className="space-y-2">
            <h3 className="font-semibold text-lg">Bedrijfsinformatie</h3>
            <div className="grid gap-2">
              {vakman.kvk_number && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Briefcase className="h-4 w-4" />
                  <span>KvK: {vakman.kvk_number}</span>
                </div>
              )}
              {vakman.btw_number && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Receipt className="h-4 w-4" />
                  <span>BTW: {vakman.btw_number}</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Address Information */}
          <div className="space-y-2">
            <h3 className="font-semibold text-lg">Adresgegevens</h3>
            <div className="grid gap-2">
              {vakman.street_address && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <MapPin className="h-4 w-4" />
                  <span>
                    {vakman.street_address} {vakman.house_number}
                    {vakman.house_number_addition &&
                      ` ${vakman.house_number_addition}`}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-3 gap-4 pt-4">
            <div className="bg-muted rounded-lg p-4 text-center">
              <div className="text-2xl font-bold">
                {vakman.completed_jobs || 0}
              </div>
              <div className="text-sm text-muted-foreground">
                Voltooide Klussen
              </div>
            </div>
            <div className="bg-muted rounded-lg p-4 text-center">
              <div className="text-2xl font-bold">
                {vakman.average_rating?.toFixed(1) || "-"}
              </div>
              <div className="text-sm text-muted-foreground">
                Gem. Beoordeling
              </div>
            </div>
            <div className="bg-muted rounded-lg p-4 text-center">
              <div className="text-2xl font-bold">
                € {vakman.balance?.toFixed(2) || "0.00"}
              </div>
              <div className="text-sm text-muted-foreground">Saldo</div>
            </div>
          </div>

          {/* Transaction History */}
          <div className="pt-4">
            <h3 className="font-semibold text-lg mb-4">
              Transactiegeschiedenis
            </h3>
            {isLoading ? (
              <div className="text-center py-4">Transacties laden...</div>
            ) : transactions && transactions.length > 0 ? (
              <TransactionHistory transactions={transactions} />
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                Geen transacties gevonden voor deze vakman.
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
