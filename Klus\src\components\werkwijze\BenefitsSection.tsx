import { Shield, Timer, Star } from "lucide-react";

export const BenefitsSection = () => {
  return (
    <div className="bg-white py-24 mt-16">
      <div className="container mx-auto px-4 max-w-6xl">
        <h2 className="text-2xl md:text-3xl font-display font-semibold text-center mb-12">
          De voordel<PERSON> van Klusgebied
        </h2>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="p-6 rounded-xl bg-secondary/50 hover:bg-secondary transition-all duration-300 animate-fade-in" style={{ animationDelay: '0.1s' }}>
            <Shield className="w-12 h-12 text-primary mb-4" />
            <h3 className="text-xl font-semibold mb-3">Veilig & <PERSON>rouwbaar</h3>
            <p className="text-gray-600">
              Alle vakmannen zijn geverifieerd en beoordeeld. Je kunt met een gerust hart je klus plaatsen.
            </p>
          </div>
          <div className="p-6 rounded-xl bg-secondary/50 hover:bg-secondary transition-all duration-300 animate-fade-in" style={{ animationDelay: '0.2s' }}>
            <Timer className="w-12 h-12 text-primary mb-4" />
            <h3 className="text-xl font-semibold mb-3">Snel Geholpen</h3>
            <p className="text-gray-600">
              Binnen 24 uur ontvang je reacties van geïnteresseerde vakmannen uit jouw regio.
            </p>
          </div>
          <div className="p-6 rounded-xl bg-secondary/50 hover:bg-secondary transition-all duration-300 animate-fade-in" style={{ animationDelay: '0.3s' }}>
            <Star className="w-12 h-12 text-primary mb-4" />
            <h3 className="text-xl font-semibold mb-3">Kwaliteit Gegarandeerd</h3>
            <p className="text-gray-600">
              Bekijk beoordelingen en portfolio's om de beste vakman voor jouw klus te kiezen.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};