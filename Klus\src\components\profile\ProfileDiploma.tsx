import React, { useEffect, useState } from "react";
import {
  Upload,
  AlertCircle,
  FileText,
  X,
  Send,
  CheckCircle,
  Clock,
  XCircle,
} from "lucide-react";

import { useAuth } from "../auth/hooks/useAuth";
import { Diploma } from "@/types/supabase";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import ReviewNotice from "./ReviewNotice";

interface UploadedDocument {
  id: string;
  name: string;
  date: string;
  status: "queued" | "pending" | "approved";
  previewUrl?: string;
  file: File; // Store the actual file
}

const ProfileDiploma: React.FC = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();

  const [existingDocs, setExistingDocs] = useState<Diploma[]>([]);
  const [queuedDocs, setQueuedDocs] = useState<UploadedDocument[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const allowedFileTypes = {
    "application/pdf": [".pdf"],
    "image/jpeg": [".jpg", ".jpeg"],
    "image/png": [".png"],
  };

  useEffect(() => {
    return () => {
      // Cleanup preview URLs when component unmounts
      queuedDocs.forEach((doc) => {
        if (doc.previewUrl) {
          URL.revokeObjectURL(doc.previewUrl);
        }
      });
    };
  }, []);

  useEffect(() => {
    // Load existing diplomas when component mounts
    if (userProfile?.diplomas) {
      setExistingDocs(userProfile.diplomas);
    }
  }, [userProfile]);

  const validateFile = (file: File) => {
    const validTypes = Object.keys(allowedFileTypes);
    if (!validTypes.includes(file.type)) {
      toast({
        title: "Ongeldig bestandstype",
        description: "Alleen PDF, JPG en PNG bestanden zijn toegestaan",
        variant: "destructive",
      });
      return false;
    }
    return true;
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    if (extension === "pdf") {
      return <FileText className="h-7 w-7 text-red-400" />;
    }
    return <FileText className="h-5 w-5 text-blue-400 mr-3" />;
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleAddFile = (file: File) => {
    if (!validateFile(file)) return;

    const newDoc: UploadedDocument = {
      id: Date.now().toString(),
      name: file.name,
      date: new Date().toLocaleDateString(),
      status: "queued",
      previewUrl: file.type.startsWith("image/")
        ? URL.createObjectURL(file)
        : undefined,
      file: file,
    };

    setQueuedDocs((prev) => [...prev, newDoc]);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    handleAddFile(files[0]);
    // Reset input value to allow selecting the same file again
    event.target.value = "";
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (!files.length) return;
    handleAddFile(files[0]);
  };

  const handleRemoveFile = (id: string) => {
    setQueuedDocs((prev) => {
      const doc = prev.find((d) => d.id === id);
      if (doc?.previewUrl) {
        URL.revokeObjectURL(doc.previewUrl);
      }
      return prev.filter((d) => d.id !== id);
    });
  };

  const handleUploadAll = async () => {
    setIsUploading(true);
    try {
      // Upload all new files first
      const uploadPromises = queuedDocs.map(async (doc) => {
        const fileExt = doc.file.name.split(".").pop();
        const fileName = `${crypto.randomUUID()}.${fileExt}`;
        const filePath = `${userProfile?.id}/${fileName}`;

        // Upload file to Supabase Storage
        const { error: uploadError } = await supabase.storage
          .from("diploma")
          .upload(filePath, doc.file);

        if (uploadError) throw uploadError;

        // Get public URL for the uploaded file
        const {
          data: { publicUrl },
        } = supabase.storage.from("diploma").getPublicUrl(filePath);

        // Determine type based on file extension
        const type = doc.file.type.startsWith("image/") ? "image" : "doc";

        return {
          id: crypto.randomUUID(),
          name: doc.file.name,
          url: publicUrl,
          uploadDate: new Date().toISOString(),
          status: "pending" as const,
          type,
        } satisfies Diploma;
      });

      // Wait for all uploads to complete
      const newDiplomas = await Promise.all(uploadPromises);

      // Combine existing and new diplomas
      const allDiplomas = [...existingDocs, ...newDiplomas];

      // Update profiles table with all diplomas in one operation
      const { error: updateError } = await supabase
        .from("profiles")
        .update({
          diplomas: allDiplomas,
        })
        .eq("id", userProfile?.id);

      if (updateError) throw updateError;

      // Update local state after successful update
      setExistingDocs(allDiplomas);
      setQueuedDocs([]);

      toast({
        title: "Uploaden voltooid",
        description: "Documenten succesvol geüpload",
      });

      // After successful profile update, send notification email
      try {
        const { backendApi } = await import("@/lib/backendApi");
        await backendApi.sendEmail({
          to: ["<EMAIL>"],
          subject: `Nieuwe diploma's/certificaten van ${userProfile?.full_name}`,
          html: `
            <h2>Er zijn nieuwe diploma's/certificaten geüpload</h2>
            <p><strong>Gebruiker:</strong> ${userProfile?.full_name}</p>
            <p><strong>Email:</strong> ${userProfile?.email}</p>
            <p><strong>Datum:</strong> ${new Date().toLocaleString("nl-NL")}</p>
            <h3>Geüploade documenten:</h3>
            <ul>
              ${newDiplomas
                .map(
                  (doc) => `
                <li>
                  <strong>${doc.name}</strong><br/>
                  <a href="${doc.url}">Bekijk document</a>
                </li>
              `
                )
                .join("")}
            </ul>
            <p>Deze documenten moeten nog worden beoordeeld.</p>
            <p><a href="https://klusgebied.nl/beheerder/kwalificaties" style="display: inline-block; background-color: #0066cc; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 15px 0;">Beoordeel documenten</a></p>
            <p>Of ga naar: <a href="https://klusgebied.nl/beheerder/kwalificaties">https://klusgebied.nl/beheerder/kwalificaties</a></p>
            <p>Met vriendelijke groet,<br/>Klusgebied.nl</p>
          `,
        });
      } catch (error) {
        console.error("Email notification failed:", error);
        // Log error but don't throw to avoid affecting user experience
      }
    } catch (error) {
      console.error("Upload failed:", error);
      toast({
        title: "Fout",
        description: "Er is een fout opgetreden bij het uploaden",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveExistingDoc = async (docId: string) => {
    try {
      // Update local state first
      const updatedDocs = existingDocs.filter((doc) => doc.id !== docId);
      setExistingDocs(updatedDocs);

      // Update profiles table
      const { error: updateError } = await supabase
        .from("profiles")
        .update({
          diplomas: updatedDocs,
        })
        .eq("id", userProfile?.id);

      if (updateError) throw updateError;

      toast({
        title: "Document verwijderd",
        description: "Het document is succesvol verwijderd",
      });
    } catch (error) {
      console.error("Remove failed:", error);
      // Revert local state if update fails
      setExistingDocs((prev) => [
        ...prev,
        existingDocs.find((d) => d.id === docId)!,
      ]);

      toast({
        title: "Fout",
        description: "Er is een fout opgetreden bij het verwijderen",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="py-3 sm:py-6">
      <div className="bg-white py-4 sm:p-0 space-y-6">
        {/* Header Section */}
        <div className="border-b border-gray-100 pb-6">
          <h2 className="text-lg font-semibold text-accent">
            Diploma's en Certificaten
          </h2>
          <p className="mt-2 text-gray-500">
            Upload hier uw certificaten en diploma's om uw kwalificaties aan te
            tonen.
          </p>
        </div>

        {/* Alert Section */}
        <div className="flex items-start space-x-3 bg-blue-50 rounded-xl p-4 border border-blue-100">
          <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <p className="text-blue-700 text-sm">
            Let op: Geüploade documenten zijn alleen zichtbaar voor
            Klusgebied.nl, niet voor klusplaatsers.
          </p>
        </div>

        {/* Review Notice */}
        {existingDocs.some((doc) => doc.status === "pending") && (
          <ReviewNotice />
        )}

        {/* Existing Diplomas Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {existingDocs.map((doc) => (
            <div
              key={doc.id}
              className="group relative bg-white rounded-xl border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200"
            >
              <div className="p-4 sm:p-5 flex items-start gap-4">
                {/* Document Preview/Icon */}
                {doc.url.match(/\.(jpg|jpeg|png|gif)$/i) ? (
                  <div className="w-20 h-20 sm:w-24 sm:h-24 rounded-lg overflow-hidden flex-shrink-0 border border-gray-200 bg-gray-50">
                    <img
                      src={doc.url}
                      alt={doc.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="w-20 h-20 sm:w-24 sm:h-24 rounded-lg flex-shrink-0 bg-gray-50 flex items-center justify-center border border-gray-200">
                    <FileText className="h-10 w-10 text-red-400" />
                  </div>
                )}

                {/* Document Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex flex-col gap-2">
                    <div className="flex justify-between flex-wrap gap-1 flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 truncate mb-1">
                        {doc.name}
                      </h3>
                      {/* Status Badge */}
                      <div
                        className={`
    inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium
    ${
      doc.status === "approved"
        ? "bg-green-50 text-green-700 border border-green-200"
        : ""
    }
    ${
      doc.status === "pending"
        ? "bg-orange-50 text-orange-700 border border-orange-200"
        : ""
    }
    ${
      doc.status === "rejected"
        ? "bg-red-50 text-red-700 border border-red-200"
        : ""
    }
  `}
                      >
                        {doc.status === "approved" && (
                          <>
                            <CheckCircle className="h-3.5 w-3.5" />
                            <span>Goedgekeurd</span>
                          </>
                        )}
                        {doc.status === "pending" && (
                          <>
                            <Clock className="h-3.5 w-3.5" />
                            <span>In behandeling (1-2 uur)</span>
                          </>
                        )}
                        {doc.status === "rejected" && (
                          <>
                            <XCircle className="h-3.5 w-3.5" />
                            <span>Afgekeurd</span>
                          </>
                        )}
                      </div>
                    </div>

                    <p className="text-sm text-gray-500 mb-2 truncate">
                      Geüpload op{" "}
                      {new Date(doc.uploadDate).toLocaleDateString()}
                    </p>
                  </div>

                  {/* Actions */}
                  <div className="flex sm:flex-row flex-col sm:items-center gap-4 mt-3">
                    <a
                      href={doc.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-1.5 text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                    >
                      <FileText className="h-4 w-4" />
                      Bekijk document
                    </a>
                    <button
                      onClick={() => handleRemoveExistingDoc(doc.id)}
                      className="inline-flex items-center gap-1.5 text-sm font-medium text-gray-500 hover:text-red-600 transition-colors"
                    >
                      <X className="h-4 w-4" />
                      Verwijderen
                    </button>
                  </div>
                </div>
              </div>

              {/* Status Bar */}
              <div
                className={`
        h-1 w-full rounded-b-xl transition-all absolute bottom-0
        ${doc.status === "approved" ? "bg-green-500" : ""}
        ${doc.status === "pending" ? "bg-orange-500" : ""}
        ${doc.status === "rejected" ? "bg-red-500" : ""}
      `}
              />
            </div>
          ))}
        </div>

        {/* Upload Section */}
        <div
          className={`
    relative rounded-xl p-8 sm:p-10 transition-all duration-200
    ${
      isDragging
        ? "bg-primary/5 border-2 border-dashed border-primary border-opacity-70"
        : "bg-gray-50 border-2 border-dashed border-gray-300 hover:border-primary/50"
    }
  `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            accept=".pdf,.jpg,.jpeg,.png"
            className="hidden"
            id="upload-file"
            type="file"
            onChange={handleFileSelect}
          />
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-white shadow-sm flex items-center justify-center border border-gray-200">
              <Upload className="h-10 w-10 text-primary/70" />
            </div>
            <h3 className="text-xl font-medium text-gray-900 mb-3">
              Sleep bestanden hierheen
            </h3>
            <p className="text-gray-500 mb-6">of</p>
            <label htmlFor="upload-file" className="inline-block">
              <div className="bg-primary hover:bg-primary/90 shadow-sm hover:shadow-md focus-within:ring-2 focus-within:ring-primary/50 focus-within:ring-offset-2 text-white px-6 py-3 rounded-xl inline-flex items-center gap-2 transition-all cursor-pointer">
                <Upload className="h-4 w-4" />
                <span>Selecteer bestand</span>
              </div>
            </label>
            <p className="sm:text-sm text-xs text-gray-500 mt-6">
              Toegestane formaten: PDF, JPG, PNG (max. 10MB)
            </p>
          </div>
        </div>

        {/* Queued Files Section */}
        {queuedDocs.length > 0 && (
          <div className="bg-gray-50 rounded-xl p-4 sm:p-6 border border-gray-100">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">
                  Geselecteerde bestanden
                </h3>
                <p className="text-sm text-gray-500">
                  {queuedDocs.length} bestand
                  {queuedDocs.length > 1 ? "en" : ""} geselecteerd
                </p>
              </div>
              <button
                onClick={handleUploadAll}
                disabled={isUploading}
                className="w-full sm:w-auto bg-primary hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md text-white px-5 py-3 rounded-xl inline-flex items-center justify-center gap-2 transition-all"
              >
                <Send className="h-4 w-4" />
                {isUploading ? "Bezig met uploaden..." : "Upload alles"}
              </button>
            </div>

            <div className="space-y-3">
              {queuedDocs.map((doc) => (
                <div
                  key={doc.id}
                  className="flex items-center bg-white p-4 rounded-xl border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all group"
                >
                  {doc.previewUrl ? (
                    <div className="w-16 h-16 sm:w-14 sm:h-14 mr-4 rounded-xl overflow-hidden flex-shrink-0 border border-gray-200">
                      <img
                        src={doc.previewUrl}
                        alt={doc.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-16 h-16 sm:w-14 sm:h-14 mr-4 rounded-xl flex-shrink-0 bg-gray-50 flex items-center justify-center">
                      {getFileIcon(doc.name)}
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">
                      {doc.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      Toegevoegd op {doc.date}
                    </p>
                  </div>
                  <button
                    onClick={() => handleRemoveFile(doc.id)}
                    className="p-2 hover:bg-gray-100 rounded-xl opacity-100 sm:opacity-0 group-hover:opacity-100 transition-all"
                    title="Verwijder bestand"
                  >
                    <X className="h-5 w-5 text-gray-400 hover:text-red-500 transition-colors" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfileDiploma;
