import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/integrations/supabase/client";
import { BackToDashboard } from "./BackToDashboard";
import { VakmanCard } from "./vakman/VakmanCard";
import { PortfolioDialog } from "./vakman/PortfolioDialog";
import { PortfolioProject, Vakman } from "./vakman/types";
import { useToast } from "@/hooks/use-toast";

// Function to calculate profile quality score
const calculateQualityScore = (vakman: any): number => {
  let score = 0;

  // Required fields completeness (40 points total)
  if (vakman.first_name) score += 5;
  if (vakman.last_name) score += 5;
  if (vakman.phone_number) score += 5;
  if (vakman.kvk_number) score += 5;
  if (vakman.btw_number) score += 5;
  if (vakman.company_name) score += 5;
  if (vakman.email) score += 5;
  if (
    vakman.services &&
    Array.isArray(vakman.services) &&
    vakman.services.length > 0
  )
    score += 5;

  // Profile photo (10 points)
  if (vakman.profile_photo_url) score += 10;

  // Additional profile fields (15 points total)
  if (vakman.street_address) score += 5;
  if (vakman.city) score += 5;
  if (vakman.postal_code) score += 5;

  // Portfolio projects (15 points)
  if (vakman.portfolioProjectCount > 0) {
    score += Math.min(vakman.portfolioProjectCount * 3, 15);
  }

  // Reviews and ratings (15 points)
  if (vakman.average_rating !== null && vakman.total_reviews > 0) {
    // Higher rating gets more points
    const ratingScore = (vakman.average_rating / 5) * 10;
    // More reviews get bonus points
    const reviewBonus = Math.min(vakman.total_reviews, 5);
    score += ratingScore + reviewBonus;
  }

  // Profile status (5 points)
  if (vakman.status === "active") score += 5;

  // Diplomas (bonus points)
  if (
    vakman.diplomas &&
    Array.isArray(vakman.diplomas) &&
    vakman.diplomas.length > 0
  ) {
    score += Math.min(vakman.diplomas.length * 2, 10);
  }

  return Math.round(score);
};

const fetchVakmannenAndJobServices = async (jobId: string | null) => {
  // Fetch vakmannen
  const { data: vakmannen, error: vakmanError } = await supabase.rpc(
    "get_filtered_vakmannen",
    { job_id: jobId }
  );

  if (vakmanError) {
    console.error("Error fetching vakmannen:", vakmanError);
    throw vakmanError;
  }

  // Fetch job services if jobId is provided
  let jobServices = null;
  if (jobId) {
    const { data: jobData, error: jobError } = await supabase
      .from("jobs")
      .select("services")
      .eq("id", jobId)
      .single();

    if (jobError) {
      console.error("Error fetching job services:", jobError);
    } else {
      jobServices = jobData?.services;
    }
  }

  const vakmannenWithRatingsAndProjects = await Promise.all(
    vakmannen.map(async (vakman) => {
      // Calculate quality score
      const qualityScore = calculateQualityScore(vakman);

      return {
        ...vakman,
        qualityScore,
      };
    })
  );

  // Sort by quality score (highest first), then by average rating, then by total reviews
  const sortedVakmannen = vakmannenWithRatingsAndProjects.sort((a, b) => {
    // Primary sort: Quality score (highest first)
    if (a.qualityScore !== b.qualityScore) {
      return (b.qualityScore || 0) - (a.qualityScore || 0);
    }

    // Secondary sort: Average rating (highest first)
    if (a.average_rating !== b.average_rating) {
      return (b.average_rating || 0) - (a.average_rating || 0);
    }

    // Tertiary sort: Total reviews (most first)
    return (b.total_reviews || 0) - (a.total_reviews || 0);
  });

  return {
    vakmannen: sortedVakmannen || [],
    jobServices,
  };
};

export const VakmanList = () => {
  const [selectedVakman, setSelectedVakman] = useState<Vakman | null>(null);
  const [portfolioProjects, setPortfolioProjects] = useState<
    PortfolioProject[]
  >([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const jobId = searchParams.get("id");

  const { data, isLoading, error } = useQuery({
    queryKey: ["vakmannen", jobId],
    queryFn: () => fetchVakmannenAndJobServices(jobId),
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  const vakmannen = data?.vakmannen;
  const jobServices = data?.jobServices;

  const handleContactVakman = async (vakman: Vakman) => {
    try {
      setSelectedVakman(vakman);

      const { data: portfolioData } = await supabase
        .from("portfolio_projects")
        .select("id, title, description, photos, budget, created_at")
        .eq("user_id", vakman.id)
        .throwOnError();

      if (!portfolioData) {
        setPortfolioProjects([]);
        return;
      }

      const formattedProjects: PortfolioProject[] = portfolioData.map(
        (project) => ({
          id: project.id,
          title: project.title,
          description: project.description,
          budget: project.budget,
          created_at: project.created_at,
          photos: Array.isArray(project.photos)
            ? project.photos.map((photo: any) => ({
                photo_url: photo?.photo_url || "",
              }))
            : [],
        })
      );

      setPortfolioProjects(formattedProjects);
    } catch (error) {
      console.error("Failed to fetch portfolio projects:", error);
      setPortfolioProjects([]);
    } finally {
      setIsDialogOpen(true);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-85px)] bg-background">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-6 bg-background min-h-[calc(100vh-85px)]">
        <BackToDashboard />
        <div className="text-red-500">
          Er is een fout opgetreden bij het laden van de vakmannen.
        </div>
      </div>
    );
  }

  const sendRequest = async (newId: string) => {
    if (!jobId || !newId) return;

    const newSelectedIds = [...selectedIds, newId];
    setSelectedIds(newSelectedIds);

    if (newSelectedIds.length === 10) {
      toast({
        variant: "destructive",
        title: "Directe aanvraag beperkt",
        description: "U kunt geen rechtstreeks verzoek meer verzenden.",
      });
    }

    try {
      const { error } = await supabase
        .from("jobs")
        .update({ direct_request: newSelectedIds })
        .eq("id", jobId);
      if (error) throw error;
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="py-6 space-y-6 px-6 overflow-y-auto h-[calc(100vh-85px)]">
      <BackToDashboard />
      <div className="flex justify-between items-center">
        <div className="space-y-4">
          <h1 className="text-3xl font-bold text-foreground">
            Beschikbare Vakmannen
          </h1>
          <p className="text-muted-foreground">
            {jobServices && Array.isArray(jobServices) && jobServices.length > 0
              ? `Vakmannen gefilterd op diensten: ${jobServices.join(", ")}`
              : "Bekijk hier alle beschikbare vakmannen op ons platform"}
            {vakmannen && vakmannen.length > 0 && (
              <span className="block text-sm mt-1">
                Gesorteerd op profielkwaliteit - {vakmannen.length} vakmannen
                gevonden
              </span>
            )}
          </p>
        </div>
      </div>

      <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
        {!vakmannen || vakmannen.length === 0 ? (
          <p className="text-muted-foreground col-span-full">
            Er zijn momenteel geen vakmannen beschikbaar.
          </p>
        ) : (
          vakmannen.map((vakman) => (
            <VakmanCard
              key={vakman.id}
              vakman={vakman}
              onPortfolioClick={handleContactVakman}
              sendRequest={sendRequest}
              selectedIds={selectedIds}
            />
          ))
        )}
      </div>

      {isDialogOpen && selectedVakman && (
        <PortfolioDialog
          isOpen={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          vakmanName={selectedVakman.company_name}
          portfolioProjects={portfolioProjects}
        />
      )}
    </div>
  );
};
