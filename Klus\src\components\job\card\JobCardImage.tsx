import { Briefcase, MapPin, Clock } from "lucide-react";
import { useEffect, useState } from "react";
import { format } from "date-fns";
import { nl } from "date-fns/locale";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { supabase } from "@/integrations/supabase/client";
import { Badge } from "@/components/ui/badge";

interface JobCardImageProps {
  photos?: string[];
  title: string;
  status?: string;
  responseCount?: number;
  isDirect: boolean;
  job: any;
}

export const JobCardImage = ({
  photos = [],
  title,
  status = "open",
  responseCount = 0,
  isDirect,
  job,
}: JobCardImageProps) => {
  const [imageUrls, setImageUrls] = useState<string[]>([]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-blue-500 hover:bg-blue-600";
      case "in_behandeling":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "accepted":
        return "bg-green-500 hover:bg-green-600";
      case "completed":
        return "bg-green-700 hover:bg-green-800";
      case "cancelled":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "open":
        return "Open";
      case "in_behandeling":
        return "In behandeling";
      case "accepted":
        return "Geaccepteerd";
      case "completed":
        return "Afgerond";
      case "cancelled":
        return "Geannuleerd";
      default:
        return status;
    }
  };

  useEffect(() => {
    const loadImages = async () => {
      if (!photos || photos.length === 0) return;

      try {
        const urls = photos.map((photoPath) => {
          if (photoPath.startsWith("http")) {
            return photoPath;
          }

          const { data } = supabase.storage
            .from("job-photos")
            .getPublicUrl(photoPath);

          return data.publicUrl;
        });

        setImageUrls(urls);
      } catch (error) {
        console.error("Error loading images:", error);
      }
    };

    loadImages();
  }, [photos]);

  const formattedDate = job.created_at
    ? format(new Date(job.created_at), "d MMM yyyy HH:mm", { locale: nl })
    : null;

  return (
    <div className="flex flex-col">
      {/* Mobile header - only visible on mobile */}
      <div className="md:hidden px-4 py-4">
        <div className="flex justify-between gap-4">
          <div>
            <h3 className="text-lg font-semibold text-foreground dark:text-foreground line-clamp-2">
              {title}
            </h3>
            <div className="flex flex-col gap-2 mt-2">
              {/* Status badges for mobile */}
              <div className="flex flex-wrap gap-2 text-sm">
                <Badge
                  className={`text-white px-2 py-0.5 font-medium ${
                    isDirect ? "bg-purple-500" : getStatusColor(status)
                  }`}
                >
                  {isDirect ? "Direct" : getStatusText(status)}
                </Badge>
                {status === "open" && responseCount > 0 && (
                  <Badge
                    variant="secondary"
                    className="bg-gray-100 text-gray-700 px-2 py-0.5 font-medium"
                  >
                    {responseCount} reactie{responseCount !== 1 ? "s" : ""}
                  </Badge>
                )}
              </div>

              {/* Location and date info */}
              <div className="flex flex-col gap-2 text-sm text-muted-foreground">
                {job.profiles?.city && (
                  <div className="flex items-center gap-1.5">
                    <MapPin className="w-4 h-4 flex-shrink-0" />
                    <span>{job.profiles?.city}</span>
                  </div>
                )}
                {formattedDate && (
                  <span className="inline-flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {formattedDate}
                  </span>
                )}
              </div>
            </div>
          </div>
          {/* Mobile image */}
          {!photos || photos.length === 0 || imageUrls.length === 0 ? null : (
            <div className="flex items-center">
              <div className="w-24 h-24 flex-shrink-0">
                <img
                  src={imageUrls[0]}
                  alt="Foto van de klus"
                  className="h-full w-full object-cover rounded-lg"
                  onError={(e) => {
                    console.error("Image failed to load:", imageUrls[0]);
                    e.currentTarget.src = "/placeholder.svg";
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Desktop header - hidden on mobile */}
      <div className="hidden md:block px-6 pt-6">
        <h3 className="text-lg font-semibold text-foreground dark:text-foreground group-hover:text-primary transition-colors line-clamp-2">
          {title}
        </h3>
      </div>

      {/* Image section */}
      <div className="relative mt-4 hidden md:block">
        {!photos || photos.length === 0 || imageUrls.length === 0 ? (
          <div className="h-48 bg-muted flex items-center justify-center">
            <Briefcase className="w-10 h-10 text-muted-foreground/50" />
          </div>
        ) : (
          <div className="hidden md:block">
            <Carousel className="h-48 w-full">
              <CarouselContent>
                {imageUrls.map((url, index) => (
                  <CarouselItem key={index}>
                    <div className="h-48 w-full relative group">
                      <img
                        src={url}
                        alt={`Foto ${index + 1} van de klus`}
                        className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                        onError={(e) => {
                          console.error("Image failed to load:", url);
                          e.currentTarget.src = "/placeholder.svg";
                        }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              {imageUrls.length > 1 && (
                <>
                  <CarouselPrevious className="left-2" />
                  <CarouselNext className="right-2" />
                </>
              )}
            </Carousel>
          </div>
        )}

        {/* Desktop badges - hidden on mobile */}
        <div className="absolute top-0 right-0 p-4 z-10 hidden md:flex flex-col gap-2 items-end">
          <Badge
            className={`text-white px-3 py-1.5 font-medium shadow-lg backdrop-blur-sm ${
              isDirect
                ? "bg-purple-500 hover:bg-purple-600"
                : getStatusColor(status)
            }`}
          >
            {isDirect ? "Directe Aanvraag" : getStatusText(status)}
          </Badge>
          {status === "open" && responseCount > 0 && (
            <Badge
              variant="secondary"
              className="bg-white/90 text-gray-800 px-3 py-1.5 font-medium whitespace-nowrap shadow-lg backdrop-blur-sm"
            >
              {responseCount} reactie{responseCount !== 1 ? "s" : ""}
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
};
