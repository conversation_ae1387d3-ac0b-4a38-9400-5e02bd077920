/**
 * @description This component renders a comprehensive and SEO-optimized detail page for upholstery services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout, mirroring the structure of other top-tier service pages for consistency. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for upholsterers.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Wrench,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
  Sofa,
  Scissors,
  Award,
} from "lucide-react";

const Service_StoffeerderPage = () => {
  usePageTitle("Stoffeerder Nodig? | Klusgebied - Meubels & Trappen Stofferen");
  const navigate = useNavigate();

  const upholsteryServices = [
    {
      icon: Sofa,
      title: "Meubels Herstofferen",
      description:
        "Geef uw favoriete stoel, bank of fauteuil een tweede leven met nieuwe bekleding.",
      points: [
        "Duurzame keuze, vaak voordeliger dan nieuw.",
        "Grote keuze in hoogwaardige stoffen en leer.",
        "Reparatie van frame en vulling mogelijk.",
        "Persoonlijk advies voor de perfecte match.",
      ],
    },
    {
      icon: Wrench, // Using Wrench as a placeholder for stairs
      title: "Trapbekleding",
      description:
        "Vakkundig bekleden van uw trap met tapijt, voor een warme en veilige uitstraling.",
      points: [
        "Verhoogt veiligheid en dempt geluid.",
        "Keuze uit diverse tapijtsoorten en kleuren.",
        "Professionele plaatsing voor een strak resultaat.",
        "Ook voor open en dichte trappen.",
      ],
    },
    {
      icon: Award,
      title: "Vloerbedekking Leggen",
      description:
        "Professioneel leggen van tapijt en andere zachte vloerbedekking.",
      points: [
        "Perfect gelegd tapijt, vinyl of marmoleum.",
        "Egaliseren van de ondervloer voor het beste resultaat.",
        "Advies over de juiste vloer voor uw ruimte.",
        "Snelle en vakkundige service.",
      ],
    },
    {
      icon: Scissors,
      title: "Advies in Stoffen & Materialen",
      description:
        "Hulp bij het kiezen van de juiste stof die past bij uw interieur en levensstijl.",
      points: [
        "Advies over slijtvastheid, kleur en textuur.",
        "Stalen beschikbaar om thuis te bekijken.",
        "Hulp bij het combineren van stoffen en patronen.",
        "Duurzame en milieuvriendelijke opties.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Award className="w-8 h-8 text-white" />,
      title: "Ambachtelijk Vakwerk",
      description:
        "Onze stoffeerders combineren traditionele technieken met moderne materialen.",
    },
    {
      icon: <Sofa className="w-8 h-8 text-white" />,
      title: "Tweede Leven voor Meubels",
      description:
        "Herstofferen is een duurzame keuze en vaak voordeliger dan nieuw kopen.",
    },
    {
      icon: <Scissors className="w-8 h-8 text-white" />,
      title: "Grote Keuze in Stoffen",
      description:
        "Wij bieden een breed scala aan hoogwaardige meubelstoffen in vele kleuren en dessins.",
    },
  ];

  const faqs = [
    {
      question: "Is herstofferen goedkoper dan een nieuw meubel kopen?",
      answer:
        "In veel gevallen wel, zeker als het gaat om een kwalitatief goed meubel. Herstofferen geeft u de kans om een uniek en persoonlijk meubelstuk te creëren.",
    },
    {
      question: "Hoe lang duurt het stofferen van een stoel?",
      answer:
        "Afhankelijk van de complexiteit van de stoel en de levertijd van de stof, duurt het proces gemiddeld 1 tot 3 weken.",
    },
    {
      question: "Welke stof is het meest slijtvast en kindvriendelijk?",
      answer:
        "Stoffen met een hoge Martindale-score (slijtvastheid) en materialen zoals microvezel of speciaal behandeld textiel zijn zeer geschikt voor intensief gebruik en makkelijk te reinigen.",
    },
    {
      question: "Kunnen jullie ook leer verwerken?",
      answer:
        "Jazeker. Onze stoffeerders hebben ervaring met het verwerken van zowel echt leer als kunstleer voor een luxe uitstraling.",
    },
  ];

  const reviews = [
    {
      name: "Linda de Boer",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Mijn oude designstoel is weer als nieuw! Prachtig vakwerk en de stof is precies wat ik zocht. Heel blij mee!",
      highlighted: false,
    },
    {
      name: "Familie Janssen",
      location: "Eindhoven",
      rating: 5,
      quote:
        "De trap is zo mooi geworden met het nieuwe tapijt. Veel stiller en veiliger voor de kinderen. Top service!",
      highlighted: true,
    },
    {
      name: "Mark Visser",
      location: "Den Haag",
      rating: 4,
      quote:
        "Goed advies gekregen over de juiste stof voor onze bank. Het resultaat is super. Communicatie kon iets sneller.",
      highlighted: false,
    },
    {
      name: "Chantal Willems",
      location: "Groningen",
      rating: 5,
      quote:
        "Snel, vakkundig en een eerlijke prijs. Onze eetkamerstoelen kunnen weer jaren mee. Aanrader!",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    { type: "image", url: "https://heyboss.heeyo.ai/**********-392d39b8.webp" },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-**********-efd6c1ff04f6?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-*************-22790b461013?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    { type: "image", url: "https://heyboss.heeyo.ai/**********-46ecf841.webp" },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description:
        "Beschrijf je wensen. Voeg foto's toe van het meubelstuk of de trap.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Vakmannen reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde stoffeerders.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & start de klus",
      description:
        "Vergelijk profielen en kies de beste vakman voor jouw project. Plan en start!",
      microcopy: "Vergelijk profielen en beoordelingen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Stoffeerders in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "stoffeerder",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Stoffeerder Nodig? | Klusgebied - Meubels & Trappen Stofferen
        </title>
        <meta
          name="description"
          content="Vind een vakkundige stoffeerder voor het herstofferen van meubels, trapbekleding en meer. Plaats je klus gratis en ontvang offertes van geverifieerde vakmannen in jouw regio."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Stoffeerder",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.8",
              reviewCount: "189",
            },
            review: reviews.map((review) => ({
              "@type": "Review",
              author: { "@type": "Person", name: review.name },
              reviewRating: {
                "@type": "Rating",
                ratingValue: review.rating,
                bestRating: "5",
              },
              reviewBody: review.quote,
            })),
            offers: {
              "@type": "Offer",
              priceCurrency: "EUR",
              priceSpecification: {
                "@type": "PriceSpecification",
                priceCurrency: "EUR",
                minPrice: "150",
                maxPrice: "1500",
                valueAddedTaxIncluded: true,
                description:
                  "Prijs is afhankelijk van het meubelstuk en de gekozen stof. Vraag een offerte aan.",
              },
            },
          })}
        </script>
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            mainEntity: faqs.map((faq) => ({
              "@type": "Question",
              name: faq.question,
              acceptedAnswer: {
                "@type": "Answer",
                text: faq.answer,
              },
            })),
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-orange-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-orange-100 border border-orange-200/80 rounded-full px-4 py-2 mb-6 pl-[16px] pr-[14px]">
                    <Sofa className="w-5 h-5 text-orange-600" />
                    <span className="text-orange-800 font-semibold text-sm">
                      Stoffeer Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Stoffeerder nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-amber-500 mt-[10px] !pt-[9px] !pb-[9px]">
                      Geef meubels een 2e leven
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-[226px] mt-[12px]">
                    Geef uw meubels, trap of interieur een nieuwe, frisse
                    uitstraling met professioneel stoffeerwerk.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() => navigate("/plaats-een-klus")}
                      className="group inline-flex items-center justify-center bg-orange-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/30 transform hover:-translate-y-1"
                    >
                      Vind jouw stoffeerder
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                  <div className="flex items-center justify-center lg:justify-start mt-6 gap-x-2 text-slate-600">
                    <div className="flex items-center text-yellow-400">
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                      <Star className="w-5 h-5 fill-current" />
                    </div>
                    <span className="font-semibold text-slate-800">4.8/5</span>
                    <span>gebaseerd op 189 klussen</span>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://heyboss.heeyo.ai/**********-392d39b8.webp"
                    alt="Stoffeerder aan het werk met een meubelstuk"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte stoffeerder.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-orange-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Stoffeerdiensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Ambachtelijk vakmanschap voor een duurzaam en prachtig
                resultaat.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {upholsteryServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-orange-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-orange-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een stoffeerder?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten zijn afhankelijk van het meubelstuk, de gekozen stof
                en de werkuren. Vraag altijd een vrijblijvende offerte aan voor
                een exacte prijs.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-orange-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Eetkamerstoel:{" "}
                    <strong className="text-slate-900">vanaf €75</strong> per
                    stoel
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-orange-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Fauteuil:{" "}
                    <strong className="text-slate-900">vanaf €400</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-orange-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Trap bekleden:{" "}
                    <strong className="text-slate-900">vanaf €500</strong>
                  </span>
                </li>
              </ul>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Prijzen zijn exclusief stof. Vraag een offerte op maat aan.
              </div>
              <button
                onClick={() => navigate("/plaats-een-klus")}
                className="bg-orange-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een stoffeerder vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-orange-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      \n{" "}
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-orange-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-orange-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-orange-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center mt-12">
              <a
                href="#"
                className="text-orange-600 font-semibold hover:underline"
              >
                Bekijk alle beoordelingen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een ervaren stoffeerder en geniet weer jaren van uw
                meubels.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-orange-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <button
                onClick={() => navigate("/over-ons")}
                className="text-orange-300 font-semibold hover:text-white transition-colors"
              >
                Bekijk waarom vakmannen en klanten voor ons kiezen{" "}
                <ArrowRight className="inline-block w-4 h-4" />
              </button>
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(`/stad/${city.toLowerCase().replace(/\s+/g, "-")}`)
                  }
                  className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-orange-600 font-medium transition-all duration-300"
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-orange-500 to-amber-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Is uw favoriete meubel versleten?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Gooi het niet weg! Vraag een vrijblijvende offerte aan voor
              herstoffering en geef het een tweede leven.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus")}
              className="bg-white text-orange-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Vraag nu een offerte aan
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus")}
          className="w-full group inline-flex items-center justify-center bg-orange-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/30 transform hover:-translate-y-1"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_StoffeerderPage;
