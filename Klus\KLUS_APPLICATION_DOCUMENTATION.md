# Klus Application - Comprehensive Documentation

## Table of Contents

1. [Overview](#overview)
2. [Technology Stack](#technology-stack)
3. [Architecture](#architecture)
4. [Backend Server Integration](#backend-server-integration)
5. [System Architecture Diagrams](#system-architecture-diagrams)
6. [Database Schema](#database-schema)
7. [Authentication & Authorization](#authentication--authorization)
8. [Core Functionalities](#core-functionalities)
9. [User Types & Roles](#user-types--roles)
10. [Job Management System](#job-management-system)
11. [Messaging System](#messaging-system)
12. [Payment & Balance System](#payment--balance-system)
13. [Portfolio Management](#portfolio-management)
14. [Admin Dashboard](#admin-dashboard)
15. [Security Features](#security-features)
16. [API Integrations](#api-integrations)
17. [Deployment & Infrastructure](#deployment--infrastructure)
18. [File Structure](#file-structure)
19. [Development Setup](#development-setup)

## Overview

Klus is a comprehensive Dutch marketplace platform that connects homeowners (klusaanvragers) with skilled craftsmen (vakmannen) for various home improvement and construction projects. The application facilitates job posting, bidding, communication, and payment processing in a secure and user-friendly environment.

### Key Features

- **Job Marketplace**: Post and browse construction/home improvement jobs
- **Real-time Messaging**: Direct communication between clients and craftsmen
- **Payment Processing**: Integrated Mollie payment system with balance management
- **Portfolio Management**: Craftsmen can showcase their previous work
- **Admin Dashboard**: Comprehensive platform management tools
- **Multi-factor Authentication**: Enhanced security with MFA support
- **Analytics & Reporting**: Detailed platform insights and statistics

## Technology Stack

### Frontend

- **React 18.3.1**: Modern React with hooks and functional components
- **TypeScript 5.5.3**: Type-safe development
- **Vite 5.4.1**: Fast build tool and development server
- **React Router DOM 6.26.2**: Client-side routing
- **Tailwind CSS 3.4.11**: Utility-first CSS framework
- **shadcn/ui**: Modern component library built on Radix UI
- **Framer Motion 12.7.3**: Animation library
- **React Hook Form 7.53.0**: Form management with validation
- **Zod 3.23.8**: Schema validation
- **Jotai 2.12.2**: State management
- **TanStack React Query 5.62.11**: Server state management
- **Recharts 2.12.7**: Chart components for analytics

### Backend & Database

- **Express.js Backend Server**: Custom Node.js/TypeScript backend
  - RESTful API endpoints
  - JWT authentication middleware
  - File upload handling
  - Payment webhook processing
  - PDF generation services
- **Supabase**: Backend-as-a-Service (BaaS) platform
  - PostgreSQL database
  - Real-time subscriptions
  - Row Level Security (RLS)
  - Storage for file uploads
- **Supabase Auth**: Authentication and authorization
- **Backend API Client**: Centralized frontend API communication

### Payment Processing

- **Mollie API**: Dutch payment service provider
- **Webhook Integration**: Real-time payment status updates

### Additional Libraries

- **Lucide React**: Icon library
- **Date-fns**: Date manipulation
- **React Dropzone**: File upload handling
- **React Google ReCAPTCHA**: Bot protection
- **PDF-lib**: PDF generation
- **Moment.js**: Date formatting
- **Lodash**: Utility functions

## Architecture

### Application Structure

```
Klus/
├── src/
│   ├── components/          # Reusable UI components
│   ├── pages/              # Page components
│   ├── hooks/              # Custom React hooks
│   ├── contexts/           # React contexts
│   ├── types/              # TypeScript type definitions
│   ├── utils/              # Utility functions
│   ├── schemas/            # Zod validation schemas
│   ├── states/             # Jotai state atoms
│   ├── config/             # Configuration files
│   ├── integrations/       # External service integrations
│   └── lib/                # Library functions
├── supabase/
│   ├── functions/          # Edge functions
│   ├── migrations/         # Database migrations
│   └── config.toml         # Supabase configuration
└── public/                 # Static assets
```

### Component Architecture

The application follows a modular component architecture:

1. **Layout Components**: Header, navigation, and layout wrappers
2. **Feature Components**: Job-related, messaging, portfolio components
3. **UI Components**: Reusable UI elements (buttons, forms, modals)
4. **Page Components**: Top-level page components
5. **Admin Components**: Administrative interface components

### State Management

- **Jotai**: Global state management for user sessions, job states, and UI states
- **React Query**: Server state management for API calls and caching
- **React Context**: Component-specific state sharing
- **Local State**: Component-level state using useState

## Backend Server Integration

The Klus application has been enhanced with a dedicated Express.js backend server that replaces Supabase Edge Functions for improved performance, debugging capabilities, and deployment flexibility.

### Migration from Supabase Edge Functions

The application previously relied on Supabase Edge Functions for server-side operations. These have been migrated to a custom Express.js backend server for the following benefits:

- **Better Performance**: Dedicated server resources and optimized request handling
- **Enhanced Debugging**: Full control over logging, error handling, and monitoring
- **Improved Scalability**: Horizontal scaling capabilities and load balancing
- **Development Experience**: Local development environment and hot reloading
- **Deployment Flexibility**: Multiple deployment options (Docker, PM2, cloud platforms)

### Backend Server Architecture

The Express.js backend server is built with:

- **TypeScript**: Type-safe server-side development
- **Express.js**: Fast, unopinionated web framework
- **Middleware Stack**: Authentication, CORS, error handling, validation
- **Supabase Integration**: Database operations and authentication
- **External APIs**: Mollie payments, email services, SMS, geocoding

### API Client Integration

The frontend uses a centralized API client (`src/lib/backendApi.ts`) that provides:

- **Unified Interface**: Single point of API communication
- **Authentication Handling**: Automatic JWT token management
- **Error Management**: Consistent error handling across the application
- **Type Safety**: TypeScript interfaces for all API operations
- **Request/Response Transformation**: Data formatting and validation

### Function Migration Mapping

| Original Supabase Function | New Express Endpoint                   | Purpose             |
| -------------------------- | -------------------------------------- | ------------------- |
| `send-email`               | `POST /api/communications/email/send`  | Email notifications |
| `send-sms`                 | `POST /api/communications/sms/send`    | SMS messaging       |
| `create-mollie-payment`    | `POST /api/payments/mollie/create`     | Payment creation    |
| `mollie-webhook`           | `POST /api/payments/mollie/webhook`    | Payment webhooks    |
| `upload-chat-attachment`   | `POST /api/utilities/chat/upload`      | File uploads        |
| `verify-recaptcha`         | `POST /api/utilities/recaptcha/verify` | Bot protection      |
| `geocode-address`          | `POST /api/utilities/geocode`          | Address geocoding   |
| `create-admin-user`        | `POST /api/users/admin/create`         | User management     |
| `schedule-cron-job`        | `POST /api/cron/schedule`              | Job scheduling      |
| `sync-hubspot`             | `POST /api/hubspot/sync`               | CRM integration     |
| `generate-transaction-pdf` | `POST /api/pdf/transaction`            | PDF generation      |

### Environment Configuration

The backend integration requires additional environment variables:

```env
# Backend Server Configuration
VITE_BACKEND_URL=http://localhost:3000

# Backend Server Environment Variables
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
MOLLIE_API_KEY=your_mollie_api_key
RESEND_API_KEY=your_resend_api_key
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

## System Architecture Diagrams

The Klus application follows a modern three-tier architecture with a React frontend, Express.js backend server, and Supabase database layer.

**Key Components:**

- **Frontend Layer**: React application with TypeScript and centralized API client
- **Backend Layer**: Express.js server with middleware stack and RESTful API routes
- **Database Layer**: Supabase PostgreSQL with authentication and file storage
- **External Services**: Third-party APIs for payments, communications, and utilities

### API Request Flow

The application uses a standardized request flow for all API communications:

1. **User Interaction**: User performs an action in the frontend
2. **API Client**: Centralized client handles the request with automatic authentication
3. **Backend Processing**: Express server validates, processes, and routes the request
4. **External Services**: Integration with third-party APIs when required
5. **Database Operations**: Supabase operations for data persistence
6. **Response Handling**: Structured JSON responses with error handling

### Payment Processing Architecture

The payment system integrates Mollie API with webhook handling for real-time payment updates:

**Payment Flow:**

1. User initiates payment in frontend
2. Backend creates Mollie payment with webhook URL
3. User completes payment on Mollie platform
4. Mollie sends webhook to backend server
5. Backend verifies payment and updates database
6. User balance and transaction status updated
7. Frontend receives real-time updates

### Backend Server Structure

The Express.js backend server is organized into distinct layers:

**Server Architecture:**

- **Entry Point**: Main server configuration and startup
- **Middleware Stack**: Authentication, CORS, validation, error handling
- **Route Handlers**: Organized by feature (payments, communications, users)
- **Services Layer**: Business logic and external API integrations

**Route Organization:**

- `/api/payments/*` - Payment processing and webhooks
- `/api/communications/*` - Email and SMS services
- `/api/users/*` - User management and authentication
- `/api/utilities/*` - Utility functions and tools
- `/api/admin/*` - Administrative functions

## Database Schema

#### profiles

```sql
- id: UUID (Primary Key)
- email: TEXT
- first_name: TEXT
- last_name: TEXT
- full_name: TEXT
- phone_number: TEXT
- street_address: TEXT
- house_number: TEXT
- house_number_addition: TEXT
- user_type: TEXT (vakman/klusaanvrager/admin)
- balance: NUMERIC
- btw_number: TEXT (VAT number for businesses)
- kvk_number: TEXT (Chamber of Commerce number)
- profile_photo_url: TEXT
- services: JSONB (Array of services offered)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### jobs

```sql
- id: UUID (Primary Key)
- user_id: UUID (Foreign Key to profiles)
- title: TEXT
- description: TEXT
- postal_code: TEXT
- house_number: TEXT
- house_number_addition: TEXT
- category: TEXT
- status: TEXT (pending/open/in_progress/completed/cancelled)
- budget: NUMERIC
- response_cost: NUMERIC (Cost to respond to job)
- photos: JSONB (Array of photo URLs)
- details: JSONB (Additional job details)
- deleted_at: TIMESTAMP
- created_at: TIMESTAMP
```

#### job_responses

```sql
- id: UUID (Primary Key)
- job_id: UUID (Foreign Key to jobs)
- user_id: UUID (Foreign Key to profiles)
- message: TEXT
- status: TEXT (pending/accepted/rejected)
- created_at: TIMESTAMP
```

#### messages

```sql
- id: UUID (Primary Key)
- job_id: UUID (Foreign Key to jobs)
- sender_id: UUID (Foreign Key to profiles)
- receiver_id: UUID (Foreign Key to profiles)
- content: TEXT
- read: BOOLEAN
- attachment_url: TEXT
- attachment_type: TEXT
- created_at: TIMESTAMP
```

#### balance_transactions

```sql
- id: UUID (Primary Key)
- user_id: UUID (Foreign Key to profiles)
- amount: NUMERIC
- type: TEXT (deposit/withdrawal/response_cost)
- description: TEXT
- status: TEXT (pending/accepted/failed)
- mollie_payment_id: TEXT
- created_at: TIMESTAMP
- completed_at: TIMESTAMP
```

#### portfolio_projects

```sql
- id: UUID (Primary Key)
- user_id: UUID (Foreign Key to profiles)
- title: TEXT
- description: TEXT
- budget: NUMERIC
- photos: JSONB (Array of photo objects)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

## Authentication & Authorization

### Authentication Flow

1. **Email Verification**: Users enter email address
2. **Password Creation**: Users create secure password with validation
3. **Profile Registration**: Users complete profile with personal/business information
4. **User Type Selection**: Choose between "vakman" (craftsman) or "klusaanvrager" (client)
5. **MFA Setup**: Optional multi-factor authentication setup

### Password Requirements

- Minimum 6 characters
- Must contain lowercase letters
- Must contain uppercase letters
- Must contain numbers
- Must contain special characters

### Authorization Levels

- **Public Routes**: Landing pages, authentication pages
- **Protected Routes**: Dashboard, job management, messaging
- **Role-based Access**: Different features for different user types
- **Admin Routes**: Administrative functions and analytics

### Multi-Factor Authentication (MFA)

- **AAL2 Level**: Authenticator Assurance Level 2
- **TOTP Support**: Time-based One-Time Password
- **Enforced for Admins**: Required for administrative access
- **Optional for Users**: Available for enhanced security

## Core Functionalities

### 1. User Registration & Onboarding

- **Multi-step Registration**: Email → Password → Profile → User Type
- **Business Information**: KVK number, BTW number for craftsmen
- **Address Verification**: Postal code and house number validation
- **Profile Photo Upload**: Avatar management
- **Service Selection**: Craftsmen can select offered services

### 2. Dashboard System

- **Klusaanvrager Dashboard**: Job management, responses, messaging
- **Vakman Dashboard**: Available jobs, responses, portfolio, balance
- **Admin Dashboard**: Platform analytics, user management, job oversight

### 3. Job Management

- **Job Creation**: Detailed job posting with photos and specifications
- **Job Browsing**: Filtered job listings for craftsmen
- **Job Responses**: Craftsmen can respond to jobs (€5 cost)
- **Job Status Tracking**: Pending → Open → In Progress → Completed
- **Job Moderation**: Admin approval for new jobs

### 4. Real-time Messaging

- **Direct Messaging**: Communication between clients and craftsmen
- **File Attachments**: Photo and document sharing
- **Read Receipts**: Message read status tracking
- **Chat History**: Persistent conversation storage
- **Notification System**: Unread message indicators

### 5. Payment & Balance System

- **Balance Management**: User account balances
- **Mollie Integration**: Secure payment processing
- **Transaction History**: Complete payment records
- **Response Costs**: €5 fee for job responses
- **Top-up System**: Easy balance replenishment

### 6. Portfolio Management

- **Project Showcase**: Craftsmen can display previous work
- **Photo Galleries**: Multiple photos per project
- **Project Details**: Descriptions, budgets, completion dates
- **Public Profiles**: Visible to potential clients

## User Types & Roles

### Klusaanvrager (Client)

**Primary Functions:**

- Post new jobs with detailed descriptions
- Browse and select craftsmen responses
- Communicate with selected craftsmen
- Manage job status and completion
- Review and rate completed work

**Dashboard Features:**

- New job creation
- My jobs management
- Response tracking
- Messaging center
- Profile management

### Vakman (Craftsman)

**Primary Functions:**

- Browse available jobs matching their services
- Respond to jobs (€5 cost per response)
- Communicate with clients
- Manage portfolio and showcase work
- Handle payments and balance

**Dashboard Features:**

- Available jobs browsing
- My responses management
- Portfolio management
- Balance and payment handling
- Messaging center

### Admin

**Primary Functions:**

- Platform oversight and moderation
- User management and support
- Analytics and reporting
- Job approval and moderation
- System configuration

**Dashboard Features:**

- User analytics and statistics
- Job management and moderation
- Message monitoring
- Financial reporting
- Platform configuration

## Job Management System

### Job Creation Process

1. **Job Details**: Title, description, category, budget
2. **Location Information**: Postal code, house number, address
3. **Photo Upload**: Multiple photos with drag-and-drop interface
4. **Additional Details**: Specific requirements, timing, preferences
5. **Review & Submit**: Final review before posting
6. **Admin Approval**: Jobs require admin approval before going live

### Job Categories

- Ventilation (Ventilatie)
- IKEA Furniture Assembly (IKEA Montage)
- Dormer Construction (Dakkapel)
- Bathroom Installation (Badkamer)
- Leak Repair (Lekkage)
- Insulation (Isolatie)
- Stair Renovation (Traprenovatie)
- CV Boiler (CV-ketel)
- Garden Paving (Tuinbestrating)
- General Contractor (Aannemer)
- Construction (Bouwbedrijf)
- Roofing (Dakbedekking)
- Plumbing (Waterleiding)

### Job Status Flow

1. **Pending**: Awaiting admin approval
2. **Open**: Available for craftsmen responses
3. **In Progress**: Work has begun
4. **Completed**: Job finished successfully
5. **Cancelled**: Job cancelled by client or admin

### Response System

- **Response Cost**: €5 per response (deducted from balance)
- **Response Content**: Message, pricing, timeline
- **Client Selection**: Client chooses preferred craftsman
- **Direct Communication**: Selected craftsman can message client

## Messaging System

### Chat Features

- **Real-time Messaging**: Instant message delivery
- **File Attachments**: Photos and documents
- **Read Receipts**: Message status tracking
- **Chat History**: Persistent conversation storage
- **User Avatars**: Profile photos in chat interface

### Message Types

- **Text Messages**: Standard text communication
- **Image Attachments**: Photo sharing
- **Document Attachments**: PDF, documents, etc.
- **System Messages**: Platform notifications

### Chat Interface

- **Message Bubbles**: Distinct styling for sender/receiver
- **Timestamp Display**: Message timing information
- **Attachment Preview**: Image and document previews
- **Scroll Management**: Auto-scroll to latest messages
- **Input Validation**: Message content validation

## Payment & Balance System

### Balance Management

- **Account Balance**: User account funds
- **Transaction History**: Complete payment records
- **Top-up Options**: Multiple payment amounts
- **Minimum/Maximum Limits**: €0.01 - €10,000

### Mollie Integration

- **Payment Methods**: iDEAL, credit cards, bank transfers
- **Secure Processing**: PCI-compliant payment handling
- **Webhook Notifications**: Real-time payment status updates
- **Error Handling**: Comprehensive error management

### Transaction Types

- **Deposits**: Adding funds to account
- **Withdrawals**: Removing funds from account
- **Response Costs**: €5 fee for job responses

### Payment Flow

1. **User Initiates Top-up**: Selects amount and payment method
2. **Mollie Payment**: Redirected to Mollie payment page
3. **Payment Processing**: Secure payment handling
4. **Webhook Notification**: Real-time status update
5. **Balance Update**: Account balance updated automatically
6. **Transaction Record**: Complete transaction history

## Portfolio Management

### Project Features

- **Project Creation**: Add new portfolio projects
- **Photo Management**: Multiple photos per project
- **Project Details**: Title, description, budget, timeline
- **Public Display**: Visible to potential clients
- **Edit & Delete**: Project management capabilities

### Photo Handling

- **Drag & Drop Upload**: Easy photo upload interface
- **Multiple Formats**: Support for various image formats
- **Storage Management**: Efficient cloud storage
- **Preview System**: Image preview before upload
- **Gallery Display**: Responsive photo galleries

### Portfolio Benefits

- **Credibility Building**: Showcase previous work
- **Client Trust**: Visual proof of capabilities
- **Service Examples**: Demonstrate specific skills
- **Professional Presentation**: Organized project display

## Admin Dashboard

### Analytics & Reporting

- **User Analytics**: User growth and distribution charts
- **Job Statistics**: Job posting and completion metrics
- **Financial Reports**: Revenue and transaction analysis
- **Platform Performance**: System usage and performance metrics
- **Date Range Filtering**: Customizable time periods

### User Management

- **User Overview**: Complete user database
- **Profile Management**: Edit user profiles and settings
- **Account Status**: Active/suspended account management
- **User Types**: Filter by user type (vakman/klusaanvrager)
- **Support Tools**: User assistance and problem resolution

### Job Moderation

- **Job Approval**: Review and approve new job postings
- **Content Moderation**: Ensure appropriate content
- **Status Management**: Update job statuses
- **Bulk Operations**: Mass job management
- **Quality Control**: Maintain platform standards

### Message Monitoring

- **Chat Oversight**: Monitor user communications
- **Content Review**: Ensure appropriate messaging
- **Dispute Resolution**: Handle user conflicts
- **System Messages**: Send platform notifications
- **Communication Tools**: Admin-to-user messaging

### Financial Management

- **Transaction Monitoring**: Track all financial transactions
- **Payment Processing**: Handle payment issues
- **Refund Management**: Process refunds when necessary
- **Revenue Analysis**: Platform revenue tracking
- **Cost Management**: Platform operational costs

## Security Features

### Authentication Security

- **Multi-Factor Authentication**: TOTP-based MFA
- **Password Requirements**: Strong password policies
- **Session Management**: Secure session handling
- **Account Lockout**: Protection against brute force attacks
- **Email Verification**: Required email confirmation

### Data Protection

- **Row Level Security**: Database-level access control
- **Encrypted Storage**: Sensitive data encryption
- **Secure API**: Protected API endpoints
- **Input Validation**: Comprehensive input sanitization
- **XSS Protection**: Cross-site scripting prevention

### Payment Security

- **PCI Compliance**: Payment card industry standards
- **Encrypted Transactions**: Secure payment processing
- **Webhook Verification**: Secure webhook handling
- **Fraud Detection**: Payment fraud monitoring
- **Secure Storage**: Encrypted payment data storage

### Platform Security

- **HTTPS Enforcement**: Secure communication
- **CORS Configuration**: Cross-origin resource sharing
- **Rate Limiting**: API rate limiting
- **Error Handling**: Secure error management
- **Logging & Monitoring**: Security event tracking

## API Integrations

### Express.js Backend Integration

- **RESTful API**: Standardized API endpoints
- **Authentication Middleware**: JWT token validation
- **Error Handling**: Consistent error responses
- **File Upload**: Multipart form handling
- **Webhook Processing**: Payment notification handling
- **PDF Generation**: Server-side document creation

### Supabase Integration

- **Database**: PostgreSQL with real-time subscriptions
- **Authentication**: Built-in auth with social providers
- **Storage**: File storage for images and documents
- **Real-time**: Live data synchronization

### Mollie Payment API

- **Payment Methods**: Multiple payment options
- **Webhook Handling**: Real-time payment notifications
- **Error Management**: Comprehensive error handling
- **Refund Processing**: Automated refund handling
- **Transaction Tracking**: Complete payment history

### Google Maps Integration

- **Address Geocoding**: Convert addresses to coordinates
- **Map Display**: Interactive map components
- **Location Services**: Location-based features
- **Distance Calculation**: Proximity-based matching

### Email Services

- **Transactional Emails**: Automated email notifications
- **Marketing Emails**: Promotional communications
- **Email Templates**: Consistent email formatting
- **Delivery Tracking**: Email delivery monitoring

## Deployment & Infrastructure

### Frontend Deployment

- **Vite Build**: Optimized production builds
- **Static Hosting**: CDN-based static file serving
- **Environment Configuration**: Environment-specific settings
- **Performance Optimization**: Code splitting and lazy loading
- **SEO Optimization**: Search engine optimization

### Backend Server Deployment

- **Express.js Server**: Node.js application deployment
- **Docker Support**: Containerized deployment with Docker
- **PM2 Process Management**: Production process management
- **Load Balancing**: Horizontal scaling capabilities
- **Health Checks**: Application health monitoring
- **Logging**: Comprehensive application logging

### Infrastructure Options

**Development Environment:**

- Local Express server with hot reloading
- Supabase local development setup
- Environment variable management

**Production Deployment:**

- **Docker**: Containerized deployment with multi-stage builds
- **Cloud Platforms**: AWS, Google Cloud, Azure deployment
- **VPS Hosting**: Traditional server deployment
- **Serverless**: Serverless function deployment options

### Backend Infrastructure

- **Supabase Hosting**: Managed database and authentication services
- **Database Scaling**: Automatic database scaling
- **CDN Integration**: Global content delivery
- **Monitoring**: Performance and error monitoring

### Environment Management

- **Development**: Local development environment
- **Staging**: Pre-production testing environment
- **Production**: Live production environment
- **Environment Variables**: Secure configuration management
- **Secrets Management**: Secure credential storage

### Performance Optimization

- **Code Splitting**: Dynamic import optimization
- **Lazy Loading**: Component and route lazy loading
- **Image Optimization**: Compressed and optimized images
- **Caching Strategy**: Effective caching implementation
- **Bundle Analysis**: Regular bundle size monitoring

## File Structure

### Component Organization

```
src/components/
├── admin/                 # Admin dashboard components
│   ├── analytics/        # Analytics and reporting
│   ├── database/         # Database management
│   ├── jobs/            # Job moderation tools
│   ├── map/             # Craftsmen map view
│   ├── messages/        # Message monitoring
│   ├── reports/         # Reporting tools
│   ├── template/        # Job templates
│   └── users/           # User management
├── auth/                # Authentication components
│   ├── hooks/           # Auth-related hooks
│   ├── registration/    # Registration forms
│   └── sections/        # Auth page sections
├── balance/             # Balance management
├── chat/                # Messaging components
├── dashboard/           # Dashboard components
├── forms/               # Form components
├── job/                 # Job-related components
│   ├── card/           # Job card components
│   ├── chat/           # Job-specific chat
│   ├── detail/         # Job detail views
│   ├── hooks/          # Job-related hooks
│   └── info/           # Job information
├── layout/              # Layout components
├── menu/                # Navigation menu
├── messages/            # Message components
├── modal/               # Modal dialogs
├── portfolio/           # Portfolio management
├── profile/             # Profile management
├── responses/           # Job response components
├── routing/             # Routing components
├── security/            # Security components
├── ui/                  # Reusable UI components
├── vakman/              # Craftsman-specific components
└── werkwijze/           # Workflow components
```

### Page Organization

```
src/pages/
├── admin/               # Admin pages
├── Auth.tsx            # Authentication page
├── Index.tsx           # Main dashboard
├── Balance.tsx         # Balance management
├── ChatPage.tsx        # Chat interface
├── Reviews.tsx         # Review system
└── [Landing Pages]     # Service-specific landing pages
```

### Type Definitions

```
src/types/
├── auth.ts             # Authentication types
├── chat.ts             # Chat system types
├── database/           # Database type definitions
│   ├── index.ts       # Main database types
│   └── tables/        # Table-specific types
├── supabase.ts         # Supabase integration types
└── [Feature Types]     # Feature-specific types
```

## Development Setup

### Prerequisites

- Node.js 18+ and npm
- Git for version control
- Supabase account and project
- Mollie API credentials
- Additional API keys (Resend, MessageBird, Google Maps, etc.)

### Installation Steps

#### Frontend Setup

```bash
# Clone the repository
git clone <repository-url>
cd Klus

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Configure environment variables

# Start development server
npm run dev
```

#### Backend Server Setup

```bash
# Navigate to backend server directory
cd klus_backend_server

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Configure backend environment variables

# Start development server
npm run dev
```

#### Full Stack Development

```bash
# Terminal 1: Start backend server
cd klus_backend_server
npm run dev

# Terminal 2: Start frontend
cd Klus
npm run dev
```

### Environment Configuration

#### Frontend Environment Variables (.env)

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Backend Server Configuration
VITE_BACKEND_URL=http://localhost:3000

# Google Maps API
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Google Analytics
VITE_GA_MEASUREMENT_ID=your_ga_measurement_id

# reCAPTCHA
VITE_RECAPTCHA_SITE_KEY=your_recaptcha_site_key

# Environment
VITE_NODE_ENV=development
```

#### Backend Server Environment Variables (.env)

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Mollie Payment Configuration
MOLLIE_API_KEY=your_mollie_api_key
MOLLIE_TEST_API_KEY=your_mollie_test_api_key

# Email Configuration (Resend)
RESEND_API_KEY=your_resend_api_key

# SMS Configuration (MessageBird)
MESSAGEBIRD_WORKSPACE_ID=your_messagebird_workspace_id
MESSAGEBIRD_CHANNEL_ID=your_messagebird_channel_id
MESSAGEBIRD_ACCESS_KEY=your_messagebird_access_key

# reCAPTCHA Configuration
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key

# HubSpot Configuration
HUBSPOT_API_KEY=your_hubspot_api_key

# Google Maps Configuration
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Application URLs
SITE_URL=http://localhost:3000
BASE_URL=http://localhost:3000
```

### Development Commands

#### Frontend Commands

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run build:dev    # Build for development
npm run lint         # Run ESLint
npm run preview      # Preview production build
```

#### Backend Server Commands

```bash
npm run dev          # Start development server with hot reload
npm run build        # Build TypeScript to JavaScript
npm start            # Start production server
npm test             # Run tests
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
```

#### Docker Commands

```bash
# Build Docker image
docker build -t klus-backend .

# Run Docker container
docker run -p 3000:3000 --env-file .env klus-backend

# Docker Compose (full stack)
docker-compose up -d
```

### Database Setup

1. **Create Supabase Project**: Set up new Supabase project
2. **Run Migrations**: Execute database migrations
3. **Configure RLS**: Set up Row Level Security policies
4. **Create Functions**: Deploy Supabase Edge Functions
5. **Set up Storage**: Configure file storage buckets

### Testing Strategy

- **Unit Testing**: Component and utility testing
- **Integration Testing**: API and database testing
- **E2E Testing**: End-to-end user flow testing
- **Performance Testing**: Load and stress testing
- **Security Testing**: Vulnerability assessment

### Code Quality

- **ESLint**: Code linting and formatting
- **TypeScript**: Type safety and IntelliSense
- **Prettier**: Code formatting
- **Husky**: Git hooks for quality checks
- **Commitlint**: Conventional commit messages

## Migration from Supabase Edge Functions to Express.js

The Klus application has undergone a significant architectural improvement by migrating from Supabase Edge Functions to a dedicated Express.js backend server. This migration provides several benefits:

### Migration Benefits

1. **Enhanced Performance**

   - Dedicated server resources
   - Optimized request handling
   - Reduced cold start times
   - Better caching capabilities

2. **Improved Development Experience**

   - Local development environment
   - Hot reloading for faster iteration
   - Better debugging capabilities
   - Comprehensive logging

3. **Deployment Flexibility**

   - Multiple deployment options (Docker, PM2, cloud platforms)
   - Horizontal scaling capabilities
   - Custom infrastructure configuration
   - Environment-specific optimizations

4. **Better Error Handling**

   - Centralized error management
   - Detailed error logging
   - Custom error responses
   - Consistent error patterns

5. **Enhanced Security**
   - Custom authentication middleware
   - Request validation
   - Rate limiting capabilities
   - Security headers management

### Migration Process

The migration process involved:

1. **Analysis**: Identifying all Supabase Edge Functions and their dependencies
2. **Design**: Creating a structured Express.js server architecture
3. **Implementation**: Converting each function to an Express route
4. **Frontend Updates**: Creating a centralized API client
5. **Testing**: Comprehensive testing of all endpoints
6. **Deployment**: Setting up production deployment

### Future Improvements

The new architecture enables several future improvements:

1. **API Versioning**: Support for versioned API endpoints
2. **Rate Limiting**: Request rate limiting for security
3. **Caching Layer**: Response caching for improved performance
4. **Monitoring**: Advanced monitoring and alerting
5. **Documentation**: Auto-generated API documentation

---

This comprehensive documentation provides a complete overview of the Klus application, its architecture, functionalities, and development processes. The platform serves as a robust marketplace connecting Dutch homeowners with skilled craftsmen, featuring advanced payment processing, real-time communication, and comprehensive administrative tools. The migration to an Express.js backend server enhances the application's performance, scalability, and developer experience.
