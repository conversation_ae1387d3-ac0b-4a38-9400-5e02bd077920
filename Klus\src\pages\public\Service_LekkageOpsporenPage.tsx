/**
 * @description This component renders a comprehensive and SEO-optimized detail page for leak detection services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for leak detection.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  ShieldCheck,
  Clock,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  UserCheck,
  CheckCircle,
  MapPin,
  Euro,
  Pipette,
  Thermometer,
  Search,
} from "lucide-react";

const Service_LekkageOpsporenPage = () => {
  usePageTitle(
    "Lekkage Opsporen | Klusgebied - Snel & Zonder Hak- & Breekwerk"
  );
  const navigate = useNavigate();

  const leakDetectionServices = [
    {
      icon: Thermometer,
      title: "Thermografische Inspectie",
      description:
        "Met een warmtebeeldcamera sporen we warmteverlies en vochtproblemen op.",
      points: [
        "Detecteert lekkages in vloerverwarming en waterleidingen.",
        "Zichtbaar maken van koudebruggen en isolatieproblemen.",
        "Geen hak- of breekwerk nodig.",
        "Snel en zeer nauwkeurig.",
      ],
    },
    {
      icon: Search,
      title: "Ultrasone Detectie",
      description:
        "Met geluidsgolven vinden we lekkages in leidingen onder druk.",
      points: [
        "Lokaliseert de exacte plek van een lekkage.",
        "Ideaal voor leidingen in muren en onder vloeren.",
        "Voorkomt onnodig breekwerk.",
        "Effectief voor zowel water- als gasleidingen.",
      ],
    },
    {
      icon: Pipette,
      title: "Endoscopie",
      description:
        "Met een kleine camera inspecteren we de binnenkant van leidingen en holle ruimtes.",
      points: [
        "Inspectie van afvoeren, riolering en spouwmuren.",
        "Visuele bevestiging van de oorzaak van het probleem.",
        "Detecteert verstoppingen, breuken en verzakkingen.",
        "Gedetailleerd beeld van de staat van uw leidingen.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Rookgasdetectie",
      description:
        "Met rookgas maken we de kleinste scheurtjes in platte daken en leidingen zichtbaar.",
      points: [
        "Zeer effectief voor platte daken en riolering.",
        "De rook is onschadelijk en laat geen sporen na.",
        "Maakt zelfs de kleinste haarscheurtjes zichtbaar.",
        "Snelle en betrouwbare methode.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Search className="w-8 h-8 text-white" />,
      title: "Zonder Hak- & Breekwerk",
      description:
        "Onze geavanceerde technieken voorkomen onnodige schade aan uw woning.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Uitgebreid Rapport",
      description: "U ontvangt een gedetailleerd rapport voor uw verzekering.",
    },
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "Snelle Respons",
      description:
        "Onze specialisten zijn snel ter plaatse om de bron van de lekkage te vinden.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost het opsporen van een lekkage?",
      answer:
        "De kosten voor lekdetectie starten meestal rond de €350. Dit is inclusief een uitgebreid rapport dat u kunt gebruiken voor uw verzekering. De kosten worden vaak vergoed door de opstalverzekering.",
    },
    {
      question: "Hoe lang duurt een lekdetectie onderzoek?",
      answer:
        "Een standaard onderzoek duurt gemiddeld 2 tot 3 uur. Na het onderzoek ontvangt u direct een voorlopige rapportage en advies.",
    },
    {
      question: "Repareren jullie de lekkage ook direct?",
      answer:
        "Onze focus ligt op het exact lokaliseren van de lekkage. Voor de reparatie koppelen we u aan een van onze geverifieerde loodgieters, die met ons rapport direct aan de slag kan.",
    },
    {
      question: "Wordt lekdetectie vergoed door mijn verzekering?",
      answer:
        "In de meeste gevallen worden de kosten voor lekdetectie vergoed door uw opstalverzekering, omdat het helpt om gevolgschade te beperken. Controleer uw polisvoorwaarden.",
    },
  ];

  const reviews = [
    {
      name: "Familie de Vries",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Na weken van onduidelijkheid over een vochtplek, heeft Klusgebied de lekkage binnen een uur gevonden zonder iets te slopen. Geweldig!",
      highlighted: true,
    },
    {
      name: "Mark Visser",
      location: "Rotterdam",
      rating: 5,
      quote:
        "Zeer professioneel en een duidelijk rapport voor de verzekering. De loodgieter kon daarna direct aan de slag. Heeft me veel geld en ellende bespaard.",
      highlighted: false,
    },
    {
      name: "Linda de Boer",
      location: "Utrecht",
      rating: 5,
      quote:
        "Snelle service en een zeer kundige specialist. De lekkage in de badkamer was snel gevonden. Een aanrader!",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1574461568706-1131776b756b?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxsZWFrJTIwZGV0ZWN0aW9uJTJDJTIwcGx1bWJpbmclMkMlMjBwcm9mZXNzaW9uYWwlMjBzZXJ2aWNlfGVufDB8fHx8MTc1MTc0OTE1N3ww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1581720604719-ee1b1a4e44b1?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHx3YXRlciUyMGxlYWslMkMlMjBob21lJTIwcmVwYWlyJTJDJTIwcGx1bWJpbmclMjBzZXJ2aWNlfGVufDB8fHx8MTc1MTc0OTE1N3ww&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1629350909073-10f19ee2c334?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwyfHxsZWFrJTJDJTIwd2F0ZXJ8ZW58MHx8fHwxNzUyMTA0MjcyfDA&ixlib=rb-4.1.0?w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1556912172-45b7abe8b7e1?ixlib=rb-4.1.0&w=600&h=600",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Meld uw lekkage",
      description:
        "Beschrijf de symptomen van de lekkage. Waar ziet u vochtplekken of hoort u gedruppel?",
      microcopy: "24/7 spoedservice beschikbaar",
    },
    {
      icon: MessageSquare,
      title: "Plan een afspraak",
      description:
        "Onze specialist komt langs met geavanceerde apparatuur om de bron te lokaliseren.",
      microcopy: "Snel een afspraak ingepland",
    },
    {
      icon: UserCheck,
      title: "Ontvang rapport & offerte",
      description:
        "U ontvangt een gedetailleerd rapport en een offerte voor de reparatie door een aangesloten vakman.",
      microcopy: "Duidelijkheid voor u en uw verzekering",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Lekkage Specialisten in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "lekkage specialist",
    color: "blue",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Lekkage Opsporen | Klusgebied - Snel & Zonder Hak- & Breekwerk
        </title>
        <meta
          name="description"
          content="Voorkom verdere schade. Onze specialisten gebruiken geavanceerde technieken om elke lekkage snel en zonder onnodig hak- en breekwerk te vinden."
        />
      </Helmet>
      <main>
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-blue-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-blue-100 border border-blue-200/80 rounded-full px-4 py-2 mb-6">
                    <Pipette className="w-5 h-5 text-blue-600" />
                    <span className="text-blue-800 font-semibold text-sm">
                      Lekkage Opsporen
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Lekkage?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-cyan-500 mt-2">
                      Snel & Zonder Schade
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voorkom verdere schade. Onze specialisten gebruiken
                    geavanceerde technieken om elke lekkage snel en zonder
                    onnodig hak- en breekwerk te vinden.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate("/plaats-een-klus/lekkage-verhelpen")
                      }
                      className="group inline-flex items-center justify-center bg-blue-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
                    >
                      Meld direct een lekkage
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>
              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1574461568706-1131776b756b?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxsZWFrJTIwZGV0ZWN0aW9uJTJDJTIwcGx1bWJpbmclMkMlMjBwcm9mZXNzaW9uYWwlMjBzZXJ2aWNlfGVufDB8fHx8MTc1MTc0OTE1N3ww&ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Specialist spoort lekkage op met apparatuur"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de oorzaak van uw lekkage.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-blue-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Detectiemethoden
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Geavanceerde technieken voor een nauwkeurige diagnose.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {leakDetectionServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-blue-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-blue-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost lekdetectie?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten voor een lekdetectie onderzoek worden vaak vergoed
                door uw opstalverzekering.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Vast tarief:{" "}
                    <strong className="text-slate-900">vanaf €350</strong>
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>Inclusief uitgebreid rapport voor de verzekering</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <span>Geen voorrijkosten</span>
                </li>
              </ul>
              <button
                onClick={() => navigate("/plaats-een-klus/lekkage-verhelpen")}
                className="bg-blue-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-blue-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis prijsvoorstellen aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een lekkage lieten opsporen.
              </p>
            </div>
            <div className="relative">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-blue-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-blue-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-blue-100"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-blue-200"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van Klusgebied
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een specialist en voorkom onnodige schade en kosten.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-blue-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-blue-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        <section className="bg-gradient-to-r from-blue-500 to-cyan-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Lekkage? Wacht niet langer!
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Meld uw lekkage en ontvang snel hulp van een specialist om de
              schade te beperken.
            </p>
            <button
              onClick={() => navigate("/plaats-een-klus/lekkage-verhelpen")}
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Meld direct een lekkage
            </button>
          </div>
        </section>
      </main>
      <Footer />
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/lekkage-verhelpen")}
          className="w-full group inline-flex items-center justify-center bg-blue-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-blue-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_LekkageOpsporenPage;
