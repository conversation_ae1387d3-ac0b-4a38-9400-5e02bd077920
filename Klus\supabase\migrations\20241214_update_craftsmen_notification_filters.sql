-- Update the notify_craftsmen_new_job function to include proper filtering
-- This ensures only eligible craftsmen receive job notifications

CREATE OR REPLACE FUNCTION notify_craftsmen_new_job(
  p_job_id UUID,
  p_job_title VARCHAR(255),
  p_services TEXT[]
)
RETURNS INTEGER AS $$
DECLARE
  craftsman_record RECORD;
  notification_count INTEGER := 0;
  portfolio_count INTEGER;
BEGIN
  -- Find craftsmen with matching services and proper filtering
  FOR craftsman_record IN
    SELECT DISTINCT p.id, p.first_name
    FROM profiles p
    WHERE p.user_type = 'vakman' 
    AND p.status = 'active'  -- Only active craftsmen
    AND p.balance > 0  -- Must have positive balance
    AND p.first_name IS NOT NULL  -- Profile completeness checks
    AND p.last_name IS NOT NULL
    AND p.street_address IS NOT NULL
    AND p.house_number IS NOT NULL
    AND p.kvk_number IS NOT NULL
    AND p.btw_number IS NOT NULL
    AND p.services && p_services  -- Array overlap operator for matching services
    AND p.id != (SELECT user_id FROM jobs WHERE id = p_job_id)  -- Don't notify job creator
  LOOP
    -- Check if craftsman has at least one portfolio project
    SELECT COUNT(*) INTO portfolio_count
    FROM portfolio_projects pp
    WHERE pp.user_id = craftsman_record.id;
    
    -- Only notify if craftsman has portfolio projects
    IF portfolio_count > 0 THEN
      -- Create notification for each eligible craftsman
      PERFORM create_notification(
        craftsman_record.id,
        'Nieuwe klus beschikbaar',
        'Er is een nieuwe klus beschikbaar die past bij jouw diensten: ' || p_job_title,
        'info',
        '/banen/' || p_job_id::text
      );
      
      notification_count := notification_count + 1;
    END IF;
  END LOOP;
  
  RETURN notification_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment explaining the filtering criteria
COMMENT ON FUNCTION notify_craftsmen_new_job(UUID, VARCHAR(255), TEXT[]) IS 
'Notifies eligible craftsmen about new jobs. Filters include: active status, positive balance, complete profile (first_name, last_name, street_address, house_number, kvk_number, btw_number), matching services, and at least one portfolio project.';
