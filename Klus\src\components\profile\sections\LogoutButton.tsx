import { LogOut } from "lucide-react";

import { Button } from "@/components/ui/button";

interface LogoutButtonProps {
  onLogout: () => void;
}

export const LogoutButton = ({ onLogout }: LogoutButtonProps) => {
  return (
    <div className="w-full flex items-center justify-center gap-2">
      <Button
        variant="destructive"
        size="lg"
        onClick={onLogout}
        className="hover:text-red-500"
      >
        <LogOut className="h-4 w-4" />
        Uitloggen
      </Button>
    </div>
  );
};
