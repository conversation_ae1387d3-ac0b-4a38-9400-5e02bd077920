/**
 * @description This component renders a comprehensive and SEO-optimized detail page for construction company services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for construction companies. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Construction,
  Building,
  ShieldCheck,
  ArrowRight,
  Star,
  Edit3,
  MessageS<PERSON>re,
  <PERSON>r<PERSON>heck,
  CheckCircle,
  MapPin,
  Euro,
} from "lucide-react";

const Service_BouwbedrijfServicePage = () => {
  usePageTitle("Bouwbedrijf Nodig? | Klusgebied - Voor Nieuwbouw & Renovatie");
  const navigate = useNavigate();

  const services = [
    {
      icon: Building,
      title: "Nieuwbouwprojecten",
      description:
        "Realisatie van uw droomhuis of bedrijfspand van de eerste paal tot de laatste steen.",
      points: [
        "Volledige begeleiding van ontwerp tot oplevering.",
        "Bouw van woningen, appartementen en bedrijfspanden.",
        "Gebruik van duurzame en hoogwaardige materialen.",
        "Vaste prijs en duidelijke planning.",
      ],
    },
    {
      icon: Construction,
      title: "Grote Renovaties",
      description:
        "Complete renovatie en modernisering van woningen en commercieel vastgoed.",
      points: [
        "Modernisering van verouderde panden.",
        "Verbouwingen voor een nieuwe indeling of functie.",
        "Energetische renovaties voor een lagere energierekening.",
        "Restauratie van monumentale panden.",
      ],
    },
    {
      icon: CheckCircle,
      title: "Utiliteitsbouw",
      description:
        "Bouw van functionele gebouwen zoals kantoren, scholen en zorginstellingen.",
      points: [
        "Functioneel en toekomstbestendig ontwerp.",
        "Ervaring met specifieke eisen voor diverse sectoren.",
        "Focus op duurzaamheid en lage onderhoudskosten.",
        "Sleutelklare oplevering.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Duurzaam Bouwen",
      description:
        "Toepassing van duurzame materialen en energiezuinige technieken.",
      points: [
        "Advies over en installatie van warmtepompen en zonnepanelen.",
        "Gebruik van circulaire en milieuvriendelijke materialen.",
        "Realisatie van energieneutrale (ENG) woningen.",
        "Voor een lagere ecologische voetafdruk en energierekening.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Construction className="w-8 h-8 text-white" />,
      title: "Ervaren Team",
      description:
        "Ons team bestaat uit vakkundige timmerlieden, metselaars en andere specialisten.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "Kwaliteit & Garantie",
      description:
        "Wij staan voor de hoogste kwaliteit en bieden garantie op al ons werk.",
    },
    {
      icon: <Building className="w-8 h-8 text-white" />,
      title: "Betrouwbare Partner",
      description:
        "Een solide en betrouwbare partner voor uw complete bouwproject.",
    },
  ];

  const faqs = [
    {
      question: "Wat is het verschil tussen een aannemer en een bouwbedrijf?",
      answer:
        "Een bouwbedrijf heeft vaak eigen personeel voor diverse specialisaties, terwijl een aannemer vaker onderaannemers inhuurt. Voor grote, complexe projecten is een bouwbedrijf vaak een logische keuze.",
    },
    {
      question: "Hoe ziet het bouwproces eruit?",
      answer:
        "Het proces omvat doorgaans: ontwerp & vergunning, voorbereiding, ruwbouw (fundering, muren, dak), afbouw (installaties, afwerking) en oplevering.",
    },
    {
      question: "Met welke keurmerken werken jullie?",
      answer:
        "Wij werken met erkende keurmerken zoals BouwGarant en Woningborg, wat u zekerheid biedt over kwaliteit en financiële afhandeling.",
    },
    {
      question: "Kunnen jullie helpen met de bouwvergunning?",
      answer:
        "Jazeker. Wij kunnen het volledige vergunningstraject voor u begeleiden, van de tekeningen tot de aanvraag bij de gemeente.",
    },
  ];

  const reviews = [
    {
      name: "Projectontwikkelaar B.V.",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Voor ons nieuwbouwproject in Amsterdam zochten we een betrouwbare partner. Klusgebied koppelde ons aan een uitstekend bouwbedrijf dat kwaliteit en planning serieus neemt.",
      highlighted: true,
    },
    {
      name: "Familie van Leeuwen",
      location: "Eindhoven",
      rating: 5,
      quote:
        "Ons droomhuis is gebouwd door een fantastisch team. De communicatie was helder en het resultaat is boven verwachting. Een aanrader voor iedereen met bouwplannen.",
      highlighted: false,
    },
    {
      name: "Zorginstelling De Horizon",
      location: "Groningen",
      rating: 5,
      quote:
        "De renovatie van onze zorglocatie is vakkundig en met minimale overlast uitgevoerd. Het bouwbedrijf dacht mee en was zeer flexibel.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1637044875244-6a002ad32799?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjb25zdHJ1Y3Rpb24lMjBzaXRlJTJDJTIwYnVpbGRpbmclMjBjcmFuZSUyQyUyMHVuZGVyJTIwY29uc3RydWN0aW9ufGVufDB8fHx8MTc1MTc0MjQyNHww&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1665069181618-5618c9b621ec?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxyZW5vdmF0aW9uJTIwcHJvamVjdCUyQyUyMGNvbnN0cnVjdGlvbiUyMHdvcmtlcnMlMkMlMjBob21lJTIwaW1wcm92ZW1lbnR8ZW58MHx8fHwxNzUxNzQyNDI0fDA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1487958449943-2429e8be8625?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1612043548602-0e66563b0ec9?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBhcmNoaXRlY3R1cmUlMkMlMjBidWlsZGluZyUyMGRlc2lnbiUyQyUyMGNvbnN0cnVjdGlvbiUyMG1hdGVyaWFsc3xlbnwwfHx8fDE3NTE3NDI0MjR8MA&ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Dien uw bouwplan in",
      description:
        "Deel uw plannen, tekeningen en wensen voor uw nieuwbouw- of renovatieproject.",
      microcopy: "Vrijblijvend en vertrouwelijk",
    },
    {
      icon: MessageSquare,
      title: "Ontvang offertes",
      description:
        "Geselecteerde bouwbedrijven nemen contact op voor een kennismaking en een gedetailleerde offerte.",
      microcopy: "Binnen 5 werkdagen reacties",
    },
    {
      icon: UserCheck,
      title: "Selecteer & start",
      description:
        "Kies het bouwbedrijf dat het beste bij uw project past en start de realisatie van uw droom.",
      microcopy: "Kies op basis van expertise en vertrouwen",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Bouwbedrijven in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "bouwbedrijf",
    color: "gray",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>
          Bouwbedrijf Nodig? | Klusgebied - Voor Nieuwbouw & Renovatie
        </title>
        <meta
          name="description"
          content="Vind een betrouwbaar bouwbedrijf voor uw nieuwbouw, verbouw of renovatieproject. Klusgebied verbindt u met gekwalificeerde bouwbedrijven voor een succesvol resultaat."
        />
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-teal-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-gray-100 border border-gray-200/80 rounded-full px-4 py-2 mb-6">
                    <Building className="w-5 h-5 text-gray-600" />
                    <span className="text-gray-800 font-semibold text-sm">
                      Bouwbedrijf Service
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Bouwbedrijf Nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-gray-700 to-slate-800 mt-2">
                      Betrouwbaar & Vakkundig
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Uw betrouwbare partner voor nieuwbouw, verbouw en
                    grootschalige renovatieprojecten.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <button
                    onClick={() =>
                      navigate("/plaats-een-klus/bouwbedrijf-inschakelen")
                    }
                    className="group inline-flex items-center justify-center bg-slate-800 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-slate-900 transition-all duration-300 shadow-lg hover:shadow-slate-800/30 transform hover:-translate-y-1"
                  >
                    Start uw bouwproject
                    <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                  </button>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1637044875244-6a002ad32799?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxjb25zdHJ1Y3Rpb24lMjBzaXRlJTJDJTIwYnVpbGRpbmclMjBjcmFuZSUyQyUyMHVuZGVyJTIwY29uc3RydWN0aW9ufGVufDB8fHx8MTc1MTc0MjQyNHww&ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Bouwplaats met een kraan en een gebouw in aanbouw"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen het juiste bouwbedrijf voor uw
                project.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-slate-800 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-slate-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Bouwdiensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vakmanschap en expertise voor elk type bouwproject.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-gray-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-gray-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een bouwproject?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten van een bouwproject zijn volledig afhankelijk van de
                omvang, materialen en complexiteit. Wij zorgen altijd voor een
                transparante en gedetailleerde offerte.
              </p>
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-lg px-4 py-3 mb-8 font-semibold">
                Vraag een vrijblijvende offerte aan voor een prijs op maat.
              </div>
              <button
                onClick={() =>
                  navigate("/plaats-een-klus/bouwbedrijf-inschakelen")
                }
                className="bg-slate-800 text-white px-8 py-3 rounded-xl font-semibold hover:bg-slate-900 transition-all duration-300 shadow-lg hover:shadow-slate-800/30 transform hover:-translate-y-1"
              >
                Vraag gratis offertes aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een bouwbedrijf vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-slate-800 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-slate-600"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted
                            ? "text-slate-300"
                            : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted
                            ? "text-slate-400"
                            : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                De Zekerheid van een Bouwbedrijf
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Kies voor een solide partner die kwaliteit, betrouwbaarheid en
                vakmanschap garandeert.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-gray-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-gray-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-gray-700 to-slate-800">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Klaar om uw droom te bouwen?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Neem contact op voor een vrijblijvend gesprek over uw nieuwbouw-
              of renovatieplannen.
            </p>
            <button
              onClick={() =>
                navigate("/plaats-een-klus/bouwbedrijf-inschakelen")
              }
              className="bg-white text-slate-800 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Neem contact op
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() => navigate("/plaats-een-klus/bouwbedrijf-inschakelen")}
          className="w-full group inline-flex items-center justify-center bg-slate-800 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-slate-900 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je project
        </button>
      </div>
    </div>
  );
};

export default Service_BouwbedrijfServicePage;
