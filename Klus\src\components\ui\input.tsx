import * as React from "react";
import { Loader2 } from "lucide-react";

import { cn } from "@/lib/utils";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  isLoading?: boolean;
  label?: string;
  labelClassName?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    { className, type, isLoading = false, label, labelClassName, id, ...props },
    ref
  ) => {
    // Generate unique ID if not provided
    const inputId = id || React.useId();

    return (
      <div className="w-full space-y-1.5">
        {label && (
          <label
            htmlFor={inputId}
            className={cn(
              "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
              labelClassName
            )}
          >
            {label}
          </label>
        )}
        <div className="flex items-center w-full rounded-md border border-input bg-background">
          <input
            id={inputId}
            type={type}
            className={cn(
              "px-3 py-2 file:border-0 w-full rounded-md file:bg-transparent file:text-base file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-none disabled:cursor-not-allowed disabled:opacity-50 text-base",
              className
            )}
            ref={ref}
            {...props}
          />
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        </div>
      </div>
    );
  }
);

Input.displayName = "Input";

export { Input };
