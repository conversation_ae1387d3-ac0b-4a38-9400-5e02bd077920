# Backend Integration Guide

This document outlines the changes made to integrate the frontend with the new Express.js backend server.

## Overview

The frontend has been updated to use the new Express.js backend server instead of Supabase Edge Functions. All function calls have been migrated to use a centralized API client.

## Changes Made

### 1. Backend API Client (`src/lib/backendApi.ts`)

Created a centralized API client that handles:

- Authentication with JWT tokens
- Error handling
- Type safety
- All backend endpoints

### 2. Environment Configuration

Added new environment variable:

- `VITE_BACKEND_URL` - URL of the Express.js backend server (default: http://localhost:3000)

### 3. Function Migrations

#### Email Functions

**Before:**

```typescript
await supabase.functions.invoke("send-email", {
  body: { to, subject, html: content },
});
```

**After:**

```typescript
const { backendApi } = await import("@/lib/backendApi");
await backendApi.sendEmail({ to, subject, html: content });
```

**Files Updated:**

- `src/lib/utils.ts` - `sendNotificationEmail`
- `src/pages/public/FeedbackPage.tsx`
- `src/pages/public/LoginPage.tsx`
- `src/components/werkwijze/CompanyFormSection.tsx`
- `src/components/bonus/ReferralSection.tsx`
- `src/components/werkwijze/new-jobs/GeneralNewJobForm.tsx`
- `src/pages/BoekhoudingUitbestedenPage.tsx`

#### Payment Functions

**Before:**

```typescript
await supabase.functions.invoke("create-mollie-payment", {
  body: paymentData,
});
```

**After:**

```typescript
const { backendApi } = await import("@/lib/backendApi");
await backendApi.createMolliePayment(paymentData);
```

**Files Updated:**

- `src/components/balance/BalanceCard.tsx`

#### File Upload Functions

**Before:**

```typescript
await supabase.functions.invoke("upload-chat-attachment", {
  body: formData,
});
```

**After:**

```typescript
// Direct Supabase storage upload (no backend API needed)
const { error: uploadError } = await supabase.storage
  .from("chat_attachments")
  .upload(filePath, file, {
    contentType: file.type,
    upsert: false,
  });
```

**Files Updated:**

- `src/components/chat/MessageInput.tsx`

**Note:** File upload functionality has been moved to direct frontend implementation using Supabase storage, eliminating the need for a backend API endpoint.

#### Utility Functions

**Before:**

```typescript
await supabase.functions.invoke("geocode-address", {
  body: { address },
});
```

**After:**

```typescript
const { backendApi } = await import("@/lib/backendApi");
await backendApi.geocodeAddress(address);
```

**Files Updated:**

- `src/components/admin/map/CraftsmenMap.tsx`

#### reCAPTCHA Verification

**Before:**

```typescript
// Simple token check without backend verification
if (token) return true;
```

**After:**

```typescript
const { backendApi } = await import("@/lib/backendApi");
const result = await backendApi.verifyRecaptcha(token);
return result.success === true;
```

**Files Updated:**

- `src/hooks/useRecaptcha.ts`

#### Admin Functions

**Before:**

```typescript
await supabase.functions.invoke("schedule-cron-job", { body });
```

**After:**

```typescript
const { backendApi } = await import("@/lib/backendApi");
await backendApi.scheduleCronJob(data);
```

**Files Updated:**

- `src/pages/admin/JobTemplatePage.tsx`

## Setup Instructions

### 1. Environment Variables

Create or update your `.env` file:

```bash
# Copy the example file
cp .env.example .env

# Add your backend URL
VITE_BACKEND_URL=http://localhost:3000
```

### 2. Start the Backend Server

```bash
cd klus_backend_server
npm install
npm run dev
```

### 3. Start the Frontend

```bash
cd Klus
npm install
npm run dev
```

## API Endpoints Mapping

| Supabase Function            | Express Endpoint                    | Method          |
| ---------------------------- | ----------------------------------- | --------------- |
| `send-email`                 | `/api/communications/email/send`    | POST            |
| `send-sms`                   | `/api/communications/sms/send`      | POST            |
| `create-mollie-payment`      | `/api/payments/mollie/create`       | POST            |
| `create-mollie-test-payment` | `/api/payments/mollie/create-test`  | POST            |
| `mollie-webhook`             | `/api/payments/mollie/webhook`      | POST            |
| `mollie-test-webhook`        | `/api/payments/mollie/test-webhook` | POST            |
| `upload-chat-attachment`     | _Direct Supabase Storage_           | _Frontend Only_ |
| `verify-recaptcha`           | `/api/utilities/recaptcha/verify`   | POST            |
| `geocode-address`            | `/api/utilities/geocode`            | POST            |
| `schedule-cron-job`          | `/api/cron/schedule`                | POST            |
| `unschedule-cron-job`        | `/api/cron/unschedule`              | DELETE          |
| `get-cron-job-schedule`      | `/api/cron/schedule/get`            | POST            |
| `create-admin-user`          | `/api/users/admin/create`           | POST            |
| `delete-admin-user`          | `/api/users/admin/:id`              | DELETE          |
| `delete-user`                | `/api/users/:id`                    | DELETE          |
| `send-password-reset`        | `/api/users/password-reset`         | POST            |
| `sync-hubspot`               | `/api/hubspot/sync`                 | POST            |
| `trigger-hubspot-sync`       | `/api/hubspot/trigger-sync`         | POST            |
| `generate-transaction-pdf`   | `/api/pdf/transaction`              | POST            |

## Error Handling

The backend API client includes comprehensive error handling:

- Automatic JWT token retrieval
- HTTP error status handling
- JSON error response parsing
- Fallback error messages

## Testing

To test the integration:

1. **Email Functions**: Try submitting feedback or creating a new user
2. **Payment Functions**: Test balance top-up functionality
3. **File Upload**: Upload files in chat
4. **Geocoding**: View craftsmen on the admin map
5. **reCAPTCHA**: Submit forms with reCAPTCHA enabled
6. **Admin Functions**: Toggle the job bot in admin panel

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure the backend server is running and CORS is properly configured
2. **Authentication Errors**: Check that JWT tokens are being passed correctly
3. **Network Errors**: Verify the `VITE_BACKEND_URL` environment variable
4. **Function Not Found**: Ensure all function calls have been migrated

### Debug Tips

1. Check browser console for error messages
2. Verify backend server logs
3. Test API endpoints directly with tools like Postman
4. Ensure environment variables are loaded correctly

## Next Steps

1. Update any remaining Supabase function calls
2. Add comprehensive error handling for specific use cases
3. Implement retry logic for critical operations
4. Add monitoring and logging for production deployment
