/**
 * @description This component displays key trust metrics and statistics for the Klusgebied platform with animated counters and stunning visual effects.
 * It showcases important numbers like completed jobs, customer ratings, and verified professionals to build user confidence.
 * The component uses animated counters, hover effects, and progressive loading animations to create an engaging presentation.
 * Key variables include trustMetrics array containing statistics with animated counter effects and hover interactions.
 */
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { CheckCircle, Star, Users, TrendingUp, Shield } from 'lucide-react';

const AnimatedCounter = ({ end, duration = 2000, suffix = '', prefix = '' }) => {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  useEffect(() => {
    if (!isVisible) return;

    const startTime = Date.now();
    const endTime = startTime + duration;

    const updateCounter = () => {
      const currentTime = Date.now();
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentCount = Math.floor(easeOutQuart * end);
      
      setCount(currentCount);

      if (currentTime < endTime) {
        requestAnimationFrame(updateCounter);
      } else {
        setCount(end);
      }
    };

    requestAnimationFrame(updateCounter);
  }, [isVisible, end, duration]);

  return (
    <span ref={ref} className="tabular-nums">
      {prefix}{count.toLocaleString()}{suffix}
    </span>
  );
};

const TrustBar = () => {
  const navigate = useNavigate();

  const trustMetrics = [
    {
      icon: CheckCircle,
      number: 15000,
      suffix: '+',
      label: 'Afgeronde klussen',
      color: 'text-teal-500',
      bgColor: 'bg-teal-50',
      hoverColor: 'hover:bg-teal-100',
      link: '/klussen-afgerond'
    },
    {
      icon: Star,
      number: 4.8,
      suffix: '/5',
      label: 'Gemiddelde score',
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50',
      hoverColor: 'hover:bg-yellow-100'
    },
    {
      icon: Users,
      number: 2600,
      suffix: '+',
      label: 'Actieve vakmannen',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      hoverColor: 'hover:bg-blue-100'
    },
    {
      icon: TrendingUp,
      number: 2.5,
      prefix: '€',
      suffix: 'M+',
      label: 'Uitgekeerd aan vakmannen',
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      hoverColor: 'hover:bg-green-100'
    }
  ];

  return (
    <section className="bg-gradient-to-br from-slate-50 to-white py-12 md:py-16 lg:py-20 relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2314B8A6' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="grid lg:grid-cols-5 gap-8 lg:gap-12 items-center">
          {/* Trust Metrics */}
          <div className="lg:col-span-3 grid grid-cols-2 gap-6 lg:gap-8">
            {trustMetrics.map((metric, index) => {
              const MetricWrapper = ({ children }) => 
                metric.link ? (
                  <div
                    onClick={() => navigate(metric.link)}
                    className={`text-center group motion-preset-slide-up motion-delay-${index * 150} hover:scale-105 transition-all duration-500 cursor-pointer`}
                  >
                    {children}
                  </div>
                ) : (
                  <div className={`text-center group motion-preset-slide-up motion-delay-${index * 150} hover:scale-105 transition-all duration-500 cursor-pointer`}>
                    {children}
                  </div>
                );

              return (
                <MetricWrapper key={metric.label}>
                  <div className="bg-white rounded-2xl p-6 lg:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-slate-100 hover:border-slate-200 h-full flex flex-col justify-center">
                    <div className="flex justify-center mb-4">
                    <div className={`p-4 rounded-2xl ${metric.bgColor} ${metric.hoverColor} transition-all duration-300 group-hover:scale-110 group-hover:rotate-3`}>
                      <metric.icon className={`w-8 h-8 ${metric.color}`} />
                    </div>
                  </div>
                  <div className="text-3xl lg:text-4xl font-bold text-slate-800 mb-2 group-hover:text-teal-600 transition-colors duration-300">
                    <AnimatedCounter 
                      end={metric.number} 
                      prefix={metric.prefix || ''} 
                      suffix={metric.suffix || ''} 
                    />
                  </div>
                  <div className="text-slate-600 font-medium text-sm lg:text-base leading-tight">
                      {metric.label}
                    </div>
                  </div>
                </MetricWrapper>
              );
            })}
          </div>

          {/* Trust Statement */}
          <div className="lg:col-span-2 motion-preset-slide-up motion-delay-600">
            <div className="bg-white p-8 rounded-2xl shadow-xl border border-slate-200/80 relative overflow-hidden">
              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-4">
                  <Shield className="w-8 h-8 text-teal-500" />
                  <div className="text-2xl font-bold text-slate-800">Gebouwd op Vertrouwen</div>
                </div>
                <p className="text-slate-600 mb-6 leading-relaxed">
                  Bij Klusgebied staat uw tevredenheid centraal. Wij garanderen kwaliteit, transparantie en betrouwbaarheid bij elke klus.
                </p>
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <span className="text-slate-700"><strong>Geverifieerde Vakmannen:</strong> Alleen de beste, gescreende professionals.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <span className="text-slate-700"><strong>Heldere Afspraken:</strong> Geen verrassingen, duidelijke offertes vooraf.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                    <span className="text-slate-700"><strong>Klanttevredenheidsgarantie:</strong> Wij zijn pas tevreden als u dat bent.</span>
                  </li>
                </ul>
              </div>
              <div className="absolute -bottom-12 -right-12 w-48 h-48 bg-teal-500/10 rounded-full"></div>
              <div className="absolute -top-10 -left-16 w-40 h-40 bg-yellow-500/10 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustBar;
