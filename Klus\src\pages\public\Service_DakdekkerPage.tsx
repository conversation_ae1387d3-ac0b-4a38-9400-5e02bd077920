/**
 * @description This component renders a comprehensive and SEO-optimized detail page for roofer services. It features a dynamic hero section, detailed service descriptions, benefits, a project gallery, an FAQ section, and a strong call-to-action. The page is designed to be visually stunning, highly informative, and conversion-focused, using world-class animations and a responsive layout. Key variables include data for services, benefits, FAQs, and media gallery items, all tailored specifically for roofers. This version includes expanded content, a visual 'How it Works' section, a dynamic customer review slider, a new pricing block, extra CTAs, a local SEO section, a sticky mobile CTA, and enhanced SEO with structured data.
 */
import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";

import Footer from "@/components/landing/Footer";
import ServiceGallery from "@/components/landing/ServiceGallery";
import FAQSection from "@/components/landing/FAQSection";
import usePageTitle from "@/hooks/usePageTitle";
import {
  ArrowLeft,
  Home,
  ShieldCheck,
  Clock,
  ArrowRight,
  Star,
  Edit3,
  MessageSquare,
  User<PERSON>heck,
  CheckCircle,
  MapPin,
  Euro,
  Umbrella,
} from "lucide-react";

const Service_DakdekkerPage = () => {
  usePageTitle("Dakdekker Nodig? | Klusgebied - Dakreparatie & Nieuw Dak");
  const navigate = useNavigate();

  const rooferServices = [
    {
      icon: Umbrella,
      title: "Dakreparatie",
      description: "Snelle reparatie van lekkages en beschadigde dakpannen.",
      points: [
        "24/7 spoedservice voor urgente lekkages.",
        "Reparatie van platte en hellende daken.",
        "Vervangen van kapotte of verschoven dakpannen.",
        "Inspectie en reparatie van daklood en nokvorsten.",
      ],
    },
    {
      icon: Home,
      title: "Nieuw Dakbedekking",
      description: "Complete vervanging van uw dak met moderne materialen.",
      points: [
        "Keuze uit bitumen, EPDM, dakpannen, en meer.",
        "Inclusief verwijderen en afvoeren oude dakbedekking.",
        "Professionele installatie met 10+ jaar garantie.",
        "Advies over de beste materialen voor uw woning.",
      ],
    },
    {
      icon: ShieldCheck,
      title: "Dakgoot Onderhoud",
      description: "Reiniging en reparatie van dakgoten en regenpijpen.",
      points: [
        "Periodieke reiniging om verstoppingen te voorkomen.",
        "Reparatie van lekkende dakgoten en regenpijpen.",
        "Installatie van bladscheiders en vogelwering.",
        "Inspectie op slijtage en zwakke plekken.",
      ],
    },
    {
      icon: Clock,
      title: "Dakisolatie",
      description: "Isolatie van uw dak voor energiebesparing en comfort.",
      points: [
        "Isolatie van binnenuit of buitenaf (warm dak).",
        "Aanzienlijke besparing op uw energierekening.",
        "Verhoogt het wooncomfort in zomer en winter.",
        "Gebruik van hoogwaardige, duurzame isolatiematerialen.",
      ],
    },
  ];

  const benefits = [
    {
      icon: <Clock className="w-8 h-8 text-white" />,
      title: "24/7 Spoedservice",
      description: "Dag en nacht bereikbaar voor urgente daklekkages.",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      title: "10 Jaar Garantie",
      description: "Wij geven uitgebreide garantie op al ons dakwerk.",
    },
    {
      icon: <Umbrella className="w-8 h-8 text-white" />,
      title: "Gegarandeerd Waterdicht",
      description: "Elk project wordt afgewerkt met een waterdichte garantie.",
    },
  ];

  const faqs = [
    {
      question: "Wat kost een nieuw dak per m2?",
      answer:
        "De kosten variëren sterk per materiaal. Reken op €70-€150 per m² voor een pannendak en €60-€100 per m² voor een plat dak met bitumen of EPDM. Vraag altijd een specifieke offerte aan.",
    },
    {
      question: "Hoe lang duurt het vervangen van een dak?",
      answer:
        "Voor een gemiddeld rijtjeshuis duurt een complete dakvervanging meestal 3 tot 5 werkdagen, afhankelijk van het weer en de complexiteit.",
    },
    {
      question: "Wanneer moet ik mijn dak laten vervangen?",
      answer:
        "Een dak moet doorgaans vervangen worden na 25-40 jaar. Tekenen van slijtage zijn terugkerende lekkages, kapotte of poreuze dakpannen, en veel mos- of algengroei.",
    },
    {
      question: "Is een jaarlijkse dakinspectie nodig?",
      answer:
        "Ja, een jaarlijkse inspectie wordt sterk aangeraden. Hiermee kunnen kleine problemen zoals verstopte goten of losse pannen vroegtijdig worden opgespoord, wat grote schade voorkomt.",
    },
  ];

  const reviews = [
    {
      name: "Familie Janssen",
      location: "Amsterdam",
      rating: 5,
      quote:
        "Onze daklekkage was binnen een paar uur verholpen. Super snelle service en zeer vakkundige dakdekkers. Top!",
      highlighted: true,
    },
    {
      name: "Mark de Groot",
      location: "Rotterdam",
      rating: 5,
      quote:
        "Ons nieuwe pannendak is prachtig geworden. Het team werkte hard en liet alles netjes achter. Zeer tevreden met het resultaat.",
      highlighted: false,
    },
    {
      name: "Linda Gerritsen",
      location: "Utrecht",
      rating: 5,
      quote:
        "De dakgoot was verstopt en lekte. De vakman van Klusgebied heeft het snel en professioneel opgelost. Goede communicatie.",
      highlighted: false,
    },
  ];

  const galleryMedia = [
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1582034536883-0d381176366b?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1721493707262-0fc9e5794c27?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1503387762-592deb58ef4e?ixlib=rb-4.1.0&w=1024&h=1024",
    },
    {
      type: "image",
      url: "https://images.unsplash.com/photo-1518780664697-55e3ad937233?ixlib=rb-4.1.0&w=1024&h=1024",
    },
  ];

  const howItWorksSteps = [
    {
      icon: Edit3,
      title: "Plaats je klus",
      description:
        "Beschrijf je dakprobleem. Voeg foto's toe voor een duidelijker beeld.",
      microcopy: "Vrijblijvend en binnen 1 minuut",
    },
    {
      icon: MessageSquare,
      title: "Vakmannen reageren",
      description:
        "Ontvang binnen 24 uur reacties en offertes van geverifieerde dakdekkers.",
      microcopy: "Binnen 24 uur reacties in je inbox",
    },
    {
      icon: UserCheck,
      title: "Kies & start de klus",
      description:
        "Vergelijk profielen en kies de beste vakman voor jouw dak. Plan en start!",
      microcopy: "Vergelijk profielen en beoordelingen, kies zelf",
    },
  ];

  const [currentReview, setCurrentReview] = useState(0);
  const reviewSliderRef = useRef(null);

  useEffect(() => {
    const sliderInterval = setInterval(() => {
      setCurrentReview((prev) => (prev + 1) % reviews.length);
    }, 5000);
    return () => clearInterval(sliderInterval);
  }, [reviews.length]);

  const localSeoProps = {
    title: "Lokale Dakdekkers in Heel Nederland",
    locations: [
      "Amsterdam",
      "Rotterdam",
      "Den Haag",
      "Utrecht",
      "Eindhoven",
      "Groningen",
      "Tilburg",
      "Almere",
      "Breda",
      "Nijmegen",
      "Apeldoorn",
      "Haarlem",
    ],
    serviceName: "dakdekker",
    color: "red",
  };

  return (
    <div className="min-h-screen bg-white">
      <Helmet>
        <title>Dakdekker Nodig? | Klusgebied - Dakreparatie & Nieuw Dak</title>
        <meta
          name="description"
          content="Vind snel een betrouwbare dakdekker voor dakreparatie, dakbedekking, dakisolatie en dakgoot onderhoud. Plaats je klus gratis en ontvang offertes van geverifieerde vakmannen."
        />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            serviceType: "Dakdekker",
            provider: {
              "@type": "LocalBusiness",
              name: "Klusgebied",
              image:
                "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c%20(1)_LRfmi8Vt.png",
              telephone: "085-1305000",
              priceRange: "€€€",
              address: {
                "@type": "PostalAddress",
                streetAddress: "Kabelweg 43",
                addressLocality: "Amsterdam",
                postalCode: "1014 BA",
                addressCountry: "NL",
              },
            },
            areaServed: {
              "@type": "Country",
              name: "Netherlands",
            },
            aggregateRating: {
              "@type": "AggregateRating",
              ratingValue: "4.8",
              reviewCount: "189",
            },
          })}
        </script>
      </Helmet>
      <main>
        {/* Hero Section */}
        <section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-24 items-center">
              <div className="text-center lg:text-left">
                <div className="motion-preset-slide-up">
                  <button
                    onClick={() => navigate("/diensten")}
                    className="inline-flex items-center gap-2 text-slate-600 hover:text-teal-600 transition-colors mb-6"
                  >
                    <ArrowLeft size={18} />
                    <span>Alle diensten</span>
                  </button>
                  <div className="inline-flex items-center space-x-2 bg-red-100 border border-red-200/80 rounded-full px-4 py-2 mb-6">
                    <Home className="w-5 h-5 text-red-600" />
                    <span className="text-red-800 font-semibold text-sm">
                      Dakdekker Diensten
                    </span>
                  </div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
                    Dakdekker Nodig?{" "}
                    <span className="block text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500 mt-2">
                      Snel & Waterdicht
                    </span>
                  </h1>
                  <p className="text-lg sm:text-xl text-slate-600 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-8">
                    Voor professionele dakreparatie, nieuw dakbedekking en
                    onderhoud. Waterdicht werk met 10 jaar garantie.
                  </p>
                </div>
                <div className="motion-preset-slide-up motion-delay-200">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={() =>
                        navigate("/plaats-een-klus/dakrenovatie-of-vervanging")
                      }
                      className="group inline-flex items-center justify-center bg-red-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-red-600 transition-all duration-300 shadow-lg hover:shadow-red-500/30 transform hover:-translate-y-1"
                    >
                      Vind direct jouw dakdekker
                      <ArrowRight className="w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="relative hidden lg:block motion-preset-slide-up motion-delay-300">
                <div className="relative w-full h-full rounded-3xl shadow-2xl overflow-hidden aspect-[4/5]">
                  <img
                    src="https://images.unsplash.com/photo-1582034536883-0d381176366b?ixlib=rb-4.1.0&w=1024&h=1024"
                    alt="Professionele dakdekker aan het werk op een dak"
                    className="w-full h-full object-cover"
                    width="500"
                    height="600"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it works */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Hoe het werkt
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Vind in 3 simpele stappen de perfecte dakdekker voor jouw klus.
              </p>
            </div>
            <div className="relative grid md:grid-cols-3 gap-8">
              <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-slate-200 -translate-y-1/2"></div>
              {howItWorksSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative bg-white p-8 rounded-2xl shadow-md text-center motion-preset-slide-up border border-slate-100"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-500 text-white mb-6 shadow-lg">
                    <step.icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-slate-600 mb-4">{step.description}</p>
                  <p className="text-sm text-red-600 font-semibold">
                    {step.microcopy}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Onze Dakdekker Diensten
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Van lekkage tot nieuw dak, wij zorgen voor een waterdicht
                resultaat.
              </p>
            </div>
            <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
              {rooferServices.map((service, index) => (
                <div
                  key={index}
                  className="bg-slate-50 rounded-2xl shadow-sm p-8 motion-preset-slide-up border border-transparent hover:border-red-200 hover:shadow-lg transition-all duration-300"
                  style={{ "--motion-delay": `${index * 100}ms` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="flex-shrink-0 bg-red-100 p-4 rounded-xl">
                      <service.icon className="w-8 h-8 text-red-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-slate-800 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 mb-4 font-medium">
                        {service.description}
                      </p>
                      <ul className="space-y-2 mt-4">
                        {service.points.map((point, i) => (
                          <li key={i} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-1 flex-shrink-0" />
                            <span className="text-slate-700">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white border border-slate-200 rounded-2xl p-10 text-center motion-preset-slide-up shadow-lg">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-600 mb-6 shadow-sm">
                <Euro className="w-8 h-8" />
              </div>
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                Wat kost een dakdekker via Klusgebied?
              </h2>
              <p className="text-slate-600 leading-relaxed mb-6">
                De kosten zijn afhankelijk van het type klus. U ontvangt altijd
                een duidelijke offerte vooraf.
              </p>
              <ul className="text-left text-slate-700 space-y-3 mb-8 max-w-md mx-auto">
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Dakreparatie:{" "}
                    <strong className="text-slate-900">€100–€350</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Nieuw dak (pannen):{" "}
                    <strong className="text-slate-900">€70–€150</strong> per m²
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-teal-500 mr-3 mt-1 flex-shrink-0" />
                  <span>
                    Nieuw dak (plat):{" "}
                    <strong className="text-slate-900">€60–€100</strong> per m²
                  </span>
                </li>
              </ul>
              <button
                onClick={() =>
                  navigate("/plaats-een-klus/dakrenovatie-of-vervanging")
                }
                className="bg-red-500 text-white px-8 py-3 rounded-xl font-semibold hover:bg-red-600 transition-all duration-300 shadow-lg hover:shadow-red-500/30 transform hover:-translate-y-1"
              >
                Vraag gratis offertes aan{" "}
                <ArrowRight className="inline-block ml-2" />
              </button>
            </div>
          </div>
        </section>

        {/* Reviews Section */}
        <section className="py-16 lg:py-24 bg-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                Wat onze klanten zeggen
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-600">
                Echte ervaringen van klanten die een dakdekker vonden via
                Klusgebied.
              </p>
            </div>
            <div className="relative" ref={reviewSliderRef}>
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentReview * 100}%)` }}
              >
                {reviews.map((review, index) => (
                  <div key={index} className="w-full flex-shrink-0 px-4">
                    <div
                      className={`p-8 rounded-2xl shadow-lg ${
                        review.highlighted
                          ? "bg-red-500 text-white"
                          : "bg-slate-50"
                      }`}
                    >
                      <div className="flex items-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? "text-yellow-400"
                                : review.highlighted
                                ? "text-red-300"
                                : "text-slate-300"
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                      <p
                        className={`italic mb-4 ${
                          review.highlighted ? "text-red-100" : "text-slate-700"
                        }`}
                      >
                        "{review.quote}"
                      </p>
                      <p
                        className={`font-bold ${
                          review.highlighted ? "text-white" : "text-slate-800"
                        }`}
                      >
                        {review.name}
                      </p>
                      <p
                        className={`text-sm ${
                          review.highlighted ? "text-red-200" : "text-slate-500"
                        }`}
                      >
                        {review.location}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 lg:py-24 bg-slate-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold">
                Waarom kiezen voor Klusgebied?
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-slate-300">
                Betrouwbare dakdekkers die staan voor kwaliteit en waterdicht
                werk.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="bg-slate-700/50 p-8 rounded-2xl text-center motion-preset-slide-up"
                  style={{ "--motion-delay": `${index * 150}ms` }}
                >
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-red-500 mb-6">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                  <p className="text-slate-300">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        <ServiceGallery media={galleryMedia} />
        <FAQSection faqs={faqs} />

        {/* Local SEO Section */}
        <section className="py-16 lg:py-24 bg-slate-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900">
                {localSeoProps.title}
              </h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {localSeoProps.locations.map((city) => (
                <button
                  key={city}
                  onClick={() =>
                    navigate(
                      `/stad/${city.toLowerCase().replace(/\\s+/g, "-")}`
                    )
                  }
                  className={`flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md hover:text-red-600 font-medium transition-all duration-300`}
                >
                  <MapPin className="w-5 h-5 mr-2 opacity-50" />
                  <span>{`${
                    localSeoProps.serviceName.charAt(0).toUpperCase() +
                    localSeoProps.serviceName.slice(1)
                  } in ${city}`}</span>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA */}
        <section className="bg-gradient-to-r from-red-500 to-orange-500">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Dak lekt of toe aan vervanging?
            </h2>
            <p className="text-lg opacity-90 mb-8">
              Onze ervaren dakdekkers staan klaar om uw dak weer waterdicht te
              maken.
            </p>
            <button
              onClick={() =>
                navigate("/plaats-een-klus/dakrenovatie-of-vervanging")
              }
              className="bg-white text-red-600 px-8 py-4 rounded-xl font-bold hover:bg-slate-100 transition-all duration-300 shadow-lg text-lg"
            >
              Plaats nu je dak klus
            </button>
          </div>
        </section>
      </main>
      <Footer />
      {/* Sticky Mobile CTA */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/80 backdrop-blur-sm p-4 border-t border-slate-200 shadow-t-lg z-50">
        <button
          onClick={() =>
            navigate("/plaats-een-klus/dakrenovatie-of-vervanging")
          }
          className="w-full group inline-flex items-center justify-center bg-red-500 text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-red-600 transition-all duration-300 shadow-lg"
        >
          <MapPin className="w-5 h-5 mr-2" />
          Plaats je klus
        </button>
      </div>
    </div>
  );
};

export default Service_DakdekkerPage;
