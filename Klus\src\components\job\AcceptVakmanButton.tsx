import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAtomValue } from "jotai";

import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { acceptedCraftmanCountAtom } from "@/states/job";

interface AcceptVakmanButtonProps {
  jobId: string;
  vakmanId: string;
  disabled?: boolean;
}

export const AcceptVakmanButton = ({
  jobId,
  vakmanId,
  disabled,
}: AcceptVakmanButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const { toast } = useToast();
  const navigate = useNavigate();
  const acceptedCraftmanCount = useAtomValue(acceptedCraftmanCountAtom);

  const handleAcceptVakman = async () => {
    setIsLoading(true);

    try {
      const jobUpdateQuery =
        acceptedCraftmanCount === 0
          ? supabase
              .from("jobs")
              .update({
                status: "in_progress",
                first_craftsman_accepted_at: new Date().toISOString(),
              })
              .eq("id", jobId)
          : supabase
              .from("jobs")
              .update({ status: "in_progress" })
              .eq("id", jobId);

      const updatePromises = [
        // Update job status with conditional first_craftsman_accepted_at
        jobUpdateQuery,

        // Update job response status
        supabase
          .from("job_responses")
          .update({ status: "accepted" })
          .eq("job_id", jobId)
          .eq("vakman_id", vakmanId),

        // Update chat status and get the slug
        supabase
          .from("chats")
          .update({ status: "accepted" })
          .eq("job_id", jobId)
          .eq("craftman_id", vakmanId)
          .select("slug")
          .single(),

        // Fetch job details
        supabase.from("jobs").select("user_id, title").eq("id", jobId),
      ];

      const [jobUpdate, responseUpdate, chatUpdate, jobFetch] =
        await Promise.all(updatePromises);

      if (jobUpdate.error) throw new Error("Kon de klus status niet updaten.");
      if (responseUpdate.error)
        throw new Error("Kon de reactie status niet updaten.");
      if (chatUpdate.error) throw new Error("Kon de chat status niet updaten.");
      if (jobFetch.error) throw new Error("Kon de klus gegevens niet ophalen.");

      toast({
        title: "Vakman geaccepteerd",
        description:
          "De vakman is geaccepteerd en jullie kunnen nu communiceren via de chat.",
      });
      navigate(`/gesprekken?chatId=${chatUpdate.data?.slug}`);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Fout bij accepteren",
        description:
          error instanceof Error
            ? error.message
            : "Er is iets misgegaan bij het accepteren van de vakman.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleAcceptVakman}
      disabled={disabled || isLoading}
      className="w-full sm:w-auto text-white"
    >
      {isLoading ? "Bezig met accepteren..." : "Accepteer vakman"}
    </Button>
  );
};
