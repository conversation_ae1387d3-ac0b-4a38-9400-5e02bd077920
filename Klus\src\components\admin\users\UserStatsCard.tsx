import { LucideIcon } from "lucide-react";

import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface UserStatsCardProps {
  icon: LucideIcon;
  title: string;
  value: number;
  loading?: boolean;
  iconClassName?: string;
  iconBgClassName?: string;
  onClick?: () => void;
  active?: boolean;
}

export const UserStatsCard = ({
  icon: Icon,
  title,
  value,
  loading = false,
  iconClassName,
  iconBgClassName,
  onClick,
  active = false,
}: UserStatsCardProps) => {
  return (
    <Card
      className={cn(
        "p-6 cursor-pointer transition-all duration-200 hover:shadow-md",
        active && "ring-2 ring-primary ring-offset-2"
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-4">
        <div
          className={cn("p-2 rounded-lg", iconBgClassName || "bg-primary/10")}
        >
          <Icon className={cn("h-5 w-5", iconClassName || "text-primary")} />
        </div>
        <div>
          <p className="text-sm text-muted-foreground">{title}</p>
          {loading ? (
            <Skeleton className="h-8 w-16" />
          ) : (
            <p className="text-2xl font-bold">{value}</p>
          )}
        </div>
      </div>
    </Card>
  );
};
