import { useState } from "react";
import {
  Phone,
  FileText,
  MapPin,
  Calendar,
  CheckCircle,
  Home,
  Wrench,
  Building,
  Hammer,
  Award,
  Shield,
  Clock,
  Users,
} from "lucide-react";
import { Link } from "react-router-dom";
import {
  ReactCompareSlider,
  ReactCompareSliderImage,
} from "react-compare-slider";
import ContactUsModal from "@/components/modal/ContactUsModal";
import FAQItem from "@/components/FAQItem";
import TestimonialCarousel from "@/components/TestimonialCarousel";

const ContractorLanding = () => {
  const [contactModalOpen, setContactModalOpen] = useState(false);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      rating: 5,
      shortText:
        "Onze uitbouw van 6 meter is tot in de puntjes geregeld. We hadden nergens omkijken naar. De aannemer was professioneel en betrouwbaar.",
      fullText:
        "Onze uitbouw van 6 meter is tot in de puntjes geregeld. We hadden nergens omkijken naar. De aanne<PERSON> was professioneel en betrouwbaar. Het project werd binnen de afgesproken tijd en budget opgeleverd. Zeer tevreden met het eindresultaat!",
      verified: true,
    },
    {
      id: 2,
      name: "Saskia",
      rating: 5,
      shortText:
        "Van offerte tot oplevering alles duidelijk. Professionele aannemer met kennis van zaken. Zeer tevreden met het resultaat.",
      fullText:
        "Van offerte tot oplevering alles duidelijk. Professionele aannemer met kennis van zaken. Zeer tevreden met het resultaat. De communicatie was uitstekend en alle afspraken werden netjes nagekomen.",
      verified: true,
    },
    {
      id: 3,
      name: "Jeroen",
      rating: 5,
      shortText:
        "Binnen budget, op tijd klaar en super strak werk geleverd. Zou deze aannemer zeker aanbevelen aan anderen.",
      fullText:
        "Binnen budget, op tijd klaar en super strak werk geleverd. Zou deze aannemer zeker aanbevelen aan anderen. De kwaliteit van het werk was uitstekend en de werkplek werd altijd netjes achtergelaten.",
      verified: true,
    },
  ];

  const benefits = [
    {
      icon: Users,
      title: "Eén aanspreekpunt",
      description: "Voor het hele project",
    },
    {
      icon: Shield,
      title: "Gecertificeerde aannemers",
      description: "Alleen erkende vakmensen",
    },
    {
      icon: Clock,
      title: "Tijdsbesparing",
      description: "Geen vakmensen los zoeken",
    },
    {
      icon: Award,
      title: "Volledige projectbegeleiding",
      description: "Van start tot oplevering",
    },
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Contact Modal */}
      <ContactUsModal
        isOpen={contactModalOpen}
        setIsOpen={setContactModalOpen}
        jobType="aannemer-inschakelen"
      />

      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 bg-white w-full z-50 shadow-sm">
        <div className="container mx-auto px-4 py-3 flex sm:flex-row flex-col gap-2 items-center justify-between">
          <Link to="/" className="flex gap-2 items-center">
            <img src="/logo.png" alt="Klusgebied Logo" className="w-12 h-12" />
            <h1 className="text-xl md:text-2xl font-bold tracking-wide">
              Klusgebied
            </h1>
          </Link>
          <div className="flex gap-4">
            <button
              onClick={() => setContactModalOpen(true)}
              className="sm:flex items-center hidden gap-2 bg-[#14213d] text-white px-4 py-2 rounded-md text-base md:text-lg tracking-wide leading-relaxed"
            >
              <Phone size={18} />
              <span>Contact opnemen</span>
            </button>
            <button
              onClick={() => setContactModalOpen(true)}
              className="flex items-center gap-2 bg-[#40cfc1] text-white px-4 py-2 rounded-md text-base md:text-lg tracking-wide leading-relaxed"
            >
              <FileText size={18} />
              <span>Vrijblijvende offerte</span>
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-80 relative">
        <div className="absolute inset-0 bg-gray-800 opacity-80 z-10" />
        <div className="relative z-10 container mx-auto px-4 pb-24 md:pb-32 text-center text-white">
          <h2 className="text-lg md:text-xl mb-2 tracking-wide leading-relaxed">
            Verbouwing, renovatie of aanbouw gepland?
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-6"></div>
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 max-w-4xl mx-auto tracking-wide leading-relaxed">
            Aannemer inschakelen – Voor elk project de juiste vakman
          </h1>
          <p className="max-w-2xl mx-auto mb-8 text-lg md:text-xl tracking-wide leading-relaxed">
            Laat jouw verbouwing, renovatie of aanbouw begeleiden door een
            ervaren aannemer. Eén aanspreekpunt, duidelijke planning, geen
            zorgen.
          </p>
          <button
            onClick={() => setContactModalOpen(true)}
            className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-base md:text-lg tracking-wide leading-relaxed"
          >
            <Phone size={18} />
            <span>Contact opnemen</span>
          </button>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16 text-white">
            <div className="flex flex-col items-center">
              <MapPin className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base md:text-lg tracking-wide leading-relaxed">
                Actief in heel Nederland
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base md:text-lg tracking-wide leading-relaxed">
                Vaak dezelfde dag beschikbaar
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Users className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base md:text-lg tracking-wide leading-relaxed">
                Eén aanspreekpunt voor alles
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <CheckCircle className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium text-base md:text-lg tracking-wide leading-relaxed">
                Transparante tarieven zonder verrassingen
              </h3>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-gray-900">
          <img
            src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-blue-bird-7218525.jpg"
            className="w-full h-full object-cover"
          />
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-12 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <p className="text-gray-800 mb-6 text-base md:text-lg tracking-wide leading-relaxed">
              Onze aannemers hebben alle benodigde licenties en certificaten.
            </p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-12">
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-1.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-2.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-3.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[500px] sm:h-[500px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-olly-3772616.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-rezwan-1216589.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Verbouwing zonder aannemer? Dat wordt een uitdaging
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                Een verbouwing zonder aannemer kan leiden tot miscommunicatie
                tussen vakmensen, vertraging in de planning en onverwachte
                kosten. Zonder centrale coördinatie ontstaan vaak problemen die
                achteraf duur uitpakken.
              </p>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                Met een ervaren aannemer heb je één aanspreekpunt die het hele
                project overziet. Hij plant de werkzaamheden, coördineert de
                vakmensen en zorgt dat alles volgens afspraak verloopt.
              </p>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                Een goede aannemer bespaart je tijd, stress en vaak ook geld
                door efficiënte planning en het voorkomen van kostbare fouten.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
              Waarom een aannemer inschakelen via Klusgebied?
            </h2>
            <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-8"></div>
            <p className="text-lg md:text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto tracking-wide leading-relaxed">
              Een aannemer is onmisbaar voor grotere klussen. Hij coördineert de
              juiste vakmensen, bewaakt de planning en zorgt dat je verbouwing
              binnen budget en volgens afspraak verloopt.
            </p>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {benefits.map((benefit, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-[#40cfc1]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <benefit.icon className="w-8 h-8 text-[#40cfc1]" />
                  </div>
                  <h3 className="text-xl md:text-2xl font-semibold mb-2 tracking-wide leading-relaxed">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600 text-base md:text-lg tracking-wide leading-relaxed">
                    {benefit.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Welke projecten voeren onze aannemers uit?
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-8"></div>
          <p className="text-center max-w-3xl mx-auto mb-12 text-base md:text-lg tracking-wide leading-relaxed">
            Wij lossen alle bouwproblemen op, van kleine verbouwingen tot
            complete nieuwbouwprojecten. Onze diensten zijn beschikbaar in heel
            Nederland.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Service Card 1 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center border hover:shadow-lg transition-shadow">
              <div className="mb-6">
                <Home size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-xl md:text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Verbouwingen
              </h3>
              <p className="text-gray-600 text-base md:text-lg tracking-wide leading-relaxed">
                Van dakkapel tot zolderrenovatie, tussenwand of volledige
                woningtransformatie. Onze aannemers regelen alles van A tot Z.
              </p>
            </div>

            {/* Service Card 2 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center border hover:shadow-lg transition-shadow">
              <div className="mb-6">
                <Building size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-xl md:text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Aanbouw of uitbouw
              </h3>
              <p className="text-gray-600 text-base md:text-lg tracking-wide leading-relaxed">
                Je woning uitbreiden? Onze aannemer regelt ontwerp, vergunningen
                en uitvoering voor een perfecte uitbreiding.
              </p>
            </div>

            {/* Service Card 3 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center border hover:shadow-lg transition-shadow">
              <div className="mb-6">
                <Wrench size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-xl md:text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Badkamer- en keukenrenovaties
              </h3>
              <p className="text-gray-600 text-base md:text-lg tracking-wide leading-relaxed">
                Complete afwerking inclusief leidingwerk, betegeling, elektra en
                afmontage door ervaren specialisten.
              </p>
            </div>

            {/* Service Card 4 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center border hover:shadow-lg transition-shadow">
              <div className="mb-6">
                <Hammer size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-xl md:text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Nieuwbouw of casco-opbouw
              </h3>
              <p className="text-gray-600 text-base md:text-lg tracking-wide leading-relaxed">
                Professionele begeleiding van grotere bouwprojecten met ervaren
                projectmanagement en kwaliteitscontrole.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-shkrabaanthony-5493653.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Aanbouw woonkamer resultaat"
                />
              </div>
              <p className="text-base md:text-lg tracking-wide leading-relaxed">
                Een professionele aanbouw woonkamer met schuifpui, volledig
                sleutel-klaar opgeleverd binnen de afgesproken tijd en budget.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-anastasia-shuraeva-5481511.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Badkamer renovatie resultaat"
                />
              </div>
              <p className="text-base md:text-lg tracking-wide leading-relaxed">
                Complete badkamerrenovatie binnen 10 dagen, inclusief
                vloerverwarming en inloopdouche, uitgevoerd door ervaren
                specialisten.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-bidvine-517980-1249611.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Zolderverbouwing resultaat"
                />
              </div>
              <p className="text-base md:text-lg tracking-wide leading-relaxed">
                Zolderverbouwing met dakkapel, dakisolatie en aftimmering voor
                optimaal wooncomfort en extra leefruimte.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Prijzen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Pricing Card 1 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl md:text-3xl font-bold tracking-wide leading-relaxed">
                  Kleine verbouwing
                </h3>
                <p className="text-base md:text-lg tracking-wide leading-relaxed">
                  Slaapkamer, wand verplaatsen
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-140px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Projectplanning
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Coördinatie vakmensen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Kwaliteitscontrole
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Oplevering
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm md:text-base text-gray-500 mr-1 tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl md:text-5xl font-bold tracking-wide">
                      €3.500
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base md:text-lg tracking-wide leading-relaxed"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 2 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl md:text-3xl font-bold tracking-wide leading-relaxed">
                  Badkamer/Keuken
                </h3>
                <p className="text-base md:text-lg tracking-wide leading-relaxed">
                  Renovatie inclusief alles
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Ontwerp en advies
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Leidingwerk en elektra
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Betegeling en afwerking
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Sanitair installatie
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm md:text-base text-gray-500 mr-1 tracking-wide">
                      €7.000 -
                    </span>
                    <span className="text-4xl md:text-5xl font-bold tracking-wide">
                      €25.000
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base md:text-lg tracking-wide leading-relaxed"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 3 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl md:text-3xl font-bold tracking-wide leading-relaxed">
                  Uitbouw/Aanbouw
                </h3>
                <p className="text-base md:text-lg tracking-wide leading-relaxed">
                  Ontwerp tot oplevering
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Architectonisch ontwerp
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Vergunningen regelen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Fundering en bouw
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Complete afwerking
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm md:text-base text-gray-500 mr-1 tracking-wide">
                      €15.000 -
                    </span>
                    <span className="text-4xl md:text-5xl font-bold tracking-wide">
                      €60.000+
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base md:text-lg tracking-wide leading-relaxed"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 4 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl md:text-3xl font-bold tracking-wide leading-relaxed">
                  Zolderverbouwing
                </h3>
                <p className="text-base md:text-lg tracking-wide leading-relaxed">
                  Dakkapel, isolatie, afwerking
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Dakkapel plaatsen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Dakisolatie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Aftimmering
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base md:text-lg tracking-wide leading-relaxed">
                      Elektra en verlichting
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm md:text-base text-gray-500 mr-1 tracking-wide">
                      €10.000 -
                    </span>
                    <span className="text-4xl md:text-5xl font-bold tracking-wide">
                      €25.000
                    </span>
                  </div>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors text-base md:text-lg tracking-wide leading-relaxed"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 24/7 Service Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                24/7 service – Direct hulp bij jouw bouwproject
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                Wij staan dag en nacht voor je klaar.
              </p>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                Of het nu gaat om een spoedklus, dringende reparatie of planning
                van een groot project, we lossen het snel op.
              </p>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                Neem contact met ons op en in veel gevallen kunnen we dezelfde
                dag nog langskomen. Zo heb je snel weer een betrouwbare aannemer
                zonder gedoe.
              </p>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                Onze eigen gecertificeerde experts werken met een persoonlijke
                en doeltreffende aanpak. Geen lange wachttijden, gewoon een
                snelle en efficiënte oplossing.
              </p>
              <p className="mb-6 text-base md:text-lg tracking-wide leading-relaxed">
                Heb je direct hulp nodig? Neem contact met ons op.
              </p>
              <button
                onClick={() => setContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-base md:text-lg tracking-wide leading-relaxed"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-olly-3760532.jpg"
                  className="w-[530px] h-[353px] object-cover rounded-lg"
                  alt="24/7 aannemer service"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Comparison Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[550px] sm:h-[550px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-olly-3760529.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-thisisengineering-3862384.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl md:text-4xl font-bold mb-8 tracking-wide leading-relaxed">
                Jouw voordelen
              </h2>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base md:text-lg tracking-wide leading-relaxed">
                    Toegewijde en deskundige aannemers
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base md:text-lg tracking-wide leading-relaxed">
                    Afspraak gegarandeerd binnen 3 dagen
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base md:text-lg tracking-wide leading-relaxed">
                    Snelle en vakkundige klantenservice; elke vraag is welkom
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base md:text-lg tracking-wide leading-relaxed">
                    Uitstekend beoordeeld door klanten
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-base md:text-lg tracking-wide leading-relaxed">
                    Mogelijkheid om 24/7 een afspraak in te plannen
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialCarousel testimonials={testimonials} />

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Veelgestelde vragen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <FAQItem
              question="Hoe weet ik dat jullie aannemers betrouwbaar zijn?"
              answer="Wij werken alleen met gecertificeerde en gecontroleerde aannemers met aantoonbare ervaring. Alle aannemers worden gescreend op kwaliteit, betrouwbaarheid en klantbeoordelingen."
            />
            <FAQItem
              question="Werkt de aannemer met eigen personeel of onderaannemers?"
              answer="Beide komt voor. In alle gevallen blijft de hoofdaannemer verantwoordelijk voor de kwaliteit en planning. Hij coördineert alle werkzaamheden en zorgt voor een soepel verloop."
            />
            <FAQItem
              question="Heb ik zelf nog andere vakmensen nodig?"
              answer="Nee. De aannemer regelt alles: van timmerman tot loodgieter. Hij heeft een netwerk van betrouwbare vakmensen en zorgt voor de juiste specialisten voor jouw project."
            />
            <FAQItem
              question="Kunnen jullie ook vergunningen regelen?"
              answer="Ja. Indien nodig kan de aannemer vergunningen aanvragen of begeleiden. Hij heeft ervaring met de procedures en zorgt dat alles volgens de regels verloopt."
            />
            <FAQItem
              question="Wat kost een offerte aanvragen?"
              answer="Dat is volledig gratis en vrijblijvend. Je krijgt een duidelijke offerte zonder verborgen kosten, zodat je goed kunt vergelijken en een weloverwogen keuze kunt maken."
            />
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-olly-3760532.jpg"
                  className="w-[530px] h-[339px] object-cover rounded-lg"
                  alt="Over Klusgebied aannemers"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Over Klusgebied
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                Bij Klusgebied zorgen we ervoor dat je bouwproject altijd in
                topconditie is. Of het nu gaat om verbouwing, renovatie of
                nieuwbouw, wij verbinden je met lokaal gecertificeerde aannemers
                die het vakkundig en snel oplossen.
              </p>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                We geloven in service zonder gedoe. Onze specialisten staan
                klaar om net dat extra stapje te zetten, zodat jij helemaal
                tevreden bent. Dat zie je terug in onze 4.8 uit 5
                klantbeoordeling – tevreden klanten zijn voor ons de standaard.
              </p>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                Heb je een bouwproject gepland of wil je een afspraak inplannen?
                Wij maken het eenvoudig en zorgen dat je snel wordt geholpen
                door onze lokale expert bij jou in de buurt.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Neem nu contact met ons op voor jouw bouwproject
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-6 text-base md:text-lg tracking-wide leading-relaxed">
                Onze aannemer experts zijn 24/7 bereikbaar en kunnen vaak
                dezelfde dag nog op de stoep staan. Klik op de onderstaande knop
                om contact op te nemen.
              </p>
              <button
                onClick={() => setContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium text-base md:text-lg tracking-wide leading-relaxed"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-64 h-64 rounded-full overflow-hidden">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/contractor/pexels-olly-3771111.jpg"
                  className="w-full h-full object-cover"
                  alt="Aannemer expert"
                />
              </div>
            </div>
          </div>
          <div className="text-center mt-4 text-sm md:text-base italic tracking-wide leading-relaxed">
            Jan van der Berg – aannemer-expert in heel Nederland, geselecteerd
            door Klusgebied.
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 justify-center">
            <div>
              <div className="flex items-center mb-4 gap-2">
                <img
                  src="/logo.png"
                  alt="Klusgebied Logo"
                  className="w-12 h-12"
                />
                <h3 className="text-xl md:text-2xl font-bold tracking-wide">
                  Klusgebied
                </h3>
              </div>
              <p className="mb-4 text-base md:text-lg tracking-wide leading-relaxed">
                Plaats vandaag nog je klus en ontvang gratis offertes van
                zorgvuldig geselecteerde vakmensen bij jou in de buurt! Binnen
                no-time staat een betrouwbare professional voor je klaar om de
                klus vakkundig uit te voeren. Laat het werk met een gerust hart
                uit handen nemen.
              </p>
            </div>
            <div>
              <h3 className="text-lg md:text-xl font-bold mb-4 tracking-wide">
                Contact
              </h3>
              <ul className="space-y-2">
                <li>
                  <p className="font-medium text-base md:text-lg tracking-wide leading-relaxed">
                    Klusgebied
                  </p>
                </li>
                <li>
                  <p className="text-base md:text-lg tracking-wide leading-relaxed">
                    Slotermeerlaan 58
                  </p>
                </li>
                <li>
                  <p className="text-base md:text-lg tracking-wide leading-relaxed">
                    1064 HC Amsterdam
                  </p>
                </li>
                <li>
                  <p className="text-base md:text-lg tracking-wide leading-relaxed">
                    KVK: 93475101
                  </p>
                </li>
                <li>
                  <p className="text-base md:text-lg tracking-wide leading-relaxed">
                    <EMAIL>
                  </p>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm md:text-base text-gray-400 tracking-wide leading-relaxed">
            <p>
              &copy; 2025 - Alle rechten voorbehouden -{" "}
              <a href="#" className="hover:text-[#40cfc1]">
                Privacyverklaring
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ContractorLanding;
