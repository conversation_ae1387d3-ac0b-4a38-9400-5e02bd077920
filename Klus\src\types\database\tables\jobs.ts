export type Job = {
  Row: {
    id: string;
    created_at: string;
    user_id: string;
    title: string;
    description: string;
    postal_code: string;
    house_number: string;
    house_number_addition: string | null;
    status: string | null;
    photos: string[];
    deleted_at: string | null;
    budget: number | null;
    response_cost: number;
    direct_request: string[];
    details: {
      job_type?: string;
      category?: string;
      [key: string]: any;
    } | null;
    profiles?: {
      first_name: string | null;
      last_name: string | null;
    };
  };
  Insert: {
    id?: string;
    created_at?: string;
    user_id: string;
    title: string;
    description: string;
    postal_code: string;
    house_number: string;
    house_number_addition?: string | null;
    status?: string | null;
    photos?: string[];
    deleted_at?: string | null;
    budget?: number | null;
    response_cost?: number;
    details?: {
      job_type?: string;
      category?: string;
      [key: string]: any;
    } | null;
  };
  Update: {
    id?: string;
    created_at?: string;
    user_id?: string;
    title?: string;
    description?: string;
    postal_code?: string;
    house_number?: string;
    house_number_addition?: string | null;
    status?: string | null;
    photos?: string[];
    deleted_at?: string | null;
    budget?: number | null;
    response_cost?: number;
    details?: {
      job_type?: string;
      category?: string;
      [key: string]: any;
    } | null;
  };
};

export type JobResponse = {
  id: string;
  status: string;
  created_at: string;
  message: string | null;
  jobs: Job["Row"] | null;
};
