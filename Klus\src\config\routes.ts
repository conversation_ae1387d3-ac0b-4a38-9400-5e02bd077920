export const ADMIN_DOMAIN = "admin.klusgebied.nl";

// Route configuration for the application
export const ROUTE_PATHS = {
  // Public routes
  AUTH: "/login",
  PLACE_JOB: "/plaats-een-klus",
  BLOGS: "/weblogs",
  FIND_PERSONNEL: "/personeel-vinden",
  LOGIN: "/inloggen",
  RESET_PASSWORD: "/opnieuw_instellen",
  BLOGDETAILS: "/weblogs/:slug",
  // Landing pages
  VENTILATION: "/ventilatie",
  IKEA_FURNITURE: "/ikea-meubels",
  DAKKAPEL: "/dakkapel",
  BADKAMER: "/badkamer",
  LEKKAGE: "/lekkage",
  ISOLATIE: "/isolatie",
  TRAPRENOVATIE: "/traprenovatie",
  CV_KETEL: "/cv-ketel",
  TUINBESTRATING: "/tuinbestrating",
  CONTRACTOR: "/aannemer",
  CONSTRUCTION: "/bouwbedrijf",
  ROOFING: "/dakbedekking",
  WATERLEIDING: "/waterleiding-vervangen",
  PRIVACY: "/privacy",
  TERMS: "/termen",
  DIRECT_LOGIN: "/direct-inloggen",

  // Package detail pages
  PACKAGE_KLUSGEBIED_PLUS: "/klusgebied-plus",
  PACKAGE_KLUSGEBIED_PRO: "/klusgebied-pro",
  PACKAGE_KLUSGEBIED_PARTNER: "/klusgebied-partner",

  // Protected routes
  HOME: "/dashboard",
  BALANCE: "/evenwicht",
  PROFILE: "/profiel",
  PROFILE_MFA: "/profiel/mfa",
  PORTFOLIO: "/portefeuille",
  JOBS: "/banen",
  MAINTENANCE: "/onderhoud",
  CONTRACT_SIGNUP: "/contract-signup/:packageId",
  JOBS_NEW: "/banen/new",
  JOBS_DETAIL: "/banen/:jobId",
  MY_RESPONSES: "/mijn_antwoorden",
  CHOOSE_CRAFTSMAN: "/kies-uw-vakman",
  REVIEWS: "/beoordelingen",
  CONVERSATIONS: "/gesprekken",
  NOTIFICATIONS: "/notificaties",
  BONUS: "/bonus",
  BOEKHOUDING_UITBESTEDEN: "/boekhouderij-uitbesteden",

  // Admin routes
  ADMIN: "/beheerder",
  ADMIN_DATABASE: "/beheerder/databank",
  ADMIN_ANALYTICS: "/beheerder/analytisch",
  ADMIN_REPORTS: "/beheerder/rapporten",
  ADMIN_MESSAGES: "/beheerder/berichten",
  ADMIN_COMPANY_REQUESTS: "/beheerder/bedrijfsaanvragen",
  ADMIN_CRAFTSMEN_MAP: "/beheerder/vakmannen-map",
  ADMIN_CRAFTSMEN: "/beheerder/vakmannen",
  ADMIN_CUSTOMERS: "/beheerder/klusaanvrager",
  ADMIN_JOB_TEMPLATES: "/beheerder/klussjablonen",

  // Blog admin routes
  ADMIN_BLOG: "/beheerder/blog",
  ADMIN_BLOG_POSTS: "/beheerder/blog/posts",
  ADMIN_BLOG_POSTS_NEW: "/beheerder/blog/posts/new",
  ADMIN_BLOG_POSTS_EDIT: "/beheerder/blog/posts/edit/:id",
  ADMIN_BLOG_AUTHORS: "/beheerder/blog/authors",
  ADMIN_BLOG_SUBSCRIBERS: "/beheerder/blog/subscribers",
  KLUS_LINK: "/klus-link",
} as const;

// Public routes that don't require authentication
export const PUBLIC_ROUTES = [
  ROUTE_PATHS.AUTH,
  ROUTE_PATHS.BLOGS,
  ROUTE_PATHS.BLOGDETAILS,
  ROUTE_PATHS.PLACE_JOB,
  ROUTE_PATHS.FIND_PERSONNEL,
  ROUTE_PATHS.LOGIN,
  ROUTE_PATHS.RESET_PASSWORD,
  ROUTE_PATHS.VENTILATION,
  ROUTE_PATHS.IKEA_FURNITURE,
  ROUTE_PATHS.DAKKAPEL,
  ROUTE_PATHS.BADKAMER,
  ROUTE_PATHS.LEKKAGE,
  ROUTE_PATHS.ISOLATIE,
  ROUTE_PATHS.TRAPRENOVATIE,
  ROUTE_PATHS.CV_KETEL,
  ROUTE_PATHS.TUINBESTRATING,
  ROUTE_PATHS.CONTRACTOR,
  ROUTE_PATHS.CONSTRUCTION,
  ROUTE_PATHS.ROOFING,
  ROUTE_PATHS.WATERLEIDING,
  ROUTE_PATHS.PRIVACY,
  ROUTE_PATHS.TERMS,
  ROUTE_PATHS.KLUS_LINK,
  ROUTE_PATHS.DIRECT_LOGIN,
  ROUTE_PATHS.PACKAGE_KLUSGEBIED_PLUS,
  ROUTE_PATHS.PACKAGE_KLUSGEBIED_PRO,
  ROUTE_PATHS.PACKAGE_KLUSGEBIED_PARTNER,
  // Additional public routes from RouteHandler
  "/",
  "/over-ons",
  "/faq",
  "/pers-en-media",
  "/support",
  "/contact",
  "/klantenservice",
  "/garantie",
  "/geschillen",
  "/feedback",
  "/login",
  "/app",
  "/klussen-afgerond",
  "/vakman",
  "/investeren",
  "/boekhouding",
  "/bedrijven",
  "/diensten/loodgieter",
  "/diensten/elektricien",
  "/diensten/schilder",
  "/diensten/timmerman",
  "/diensten/klusjesman",
  "/diensten/dakdekker",
  "/diensten/tuinman",
  "/diensten/cv-installateur",
  "/diensten/tegelvloer-specialist",
  "/diensten/beveiligingsmonteur",
  "/diensten/monteur",
  "/diensten/interieurontwerper",
  "/diensten/isolatiemonteur",
  "/diensten/parketvloer-specialist",
  "/diensten/airco-installateur",
  "/diensten/verlichting-specialist",
  "/diensten/beveiligingscamera",
  "/diensten/smart-home-specialist",
  "/diensten/telefoon-internet",
  "/diensten/garagedeur-monteur",
  "/diensten/stoffeerder",
  "/diensten/kozijn-specialist",
  "/diensten/aannemer-service",
  "/diensten/badkamer-renovatie",
  "/diensten/bouwbedrijf-service",
  "/diensten/dakbedekking",
  "/diensten/dakkapel-plaatsing",
  "/diensten/ikea-montage-service",
  "/diensten/isolatie-service",
  "/diensten/traprenovatie",
  "/diensten/tuinbestrating",
  "/diensten/ventilatie-service",
  "/diensten/lekkage-opsporen",
  "/diensten/cv-ketel-service",
  "/diensten/waterleiding-vervangen",
  "/diensten/:serviceSlug",
  "/diensten",
  "/stad/:cityName",
  "/onderhoudscontract/cv-ketel",
  "/onderhoudscontract/dakgoot",
  "/onderhoudscontract/klusjesman",
  "/onderhoudscontract/huis-inspectie",
  "/onderhoudscontracten",
] as const;

export const RESTRICTED_PUBLIC_ROUTES = [
  ROUTE_PATHS.AUTH,
  ROUTE_PATHS.PLACE_JOB,
  ROUTE_PATHS.FIND_PERSONNEL,
  ROUTE_PATHS.LOGIN,
  ROUTE_PATHS.VENTILATION,
  ROUTE_PATHS.IKEA_FURNITURE,
  ROUTE_PATHS.DAKKAPEL,
  ROUTE_PATHS.BADKAMER,
  ROUTE_PATHS.LEKKAGE,
  ROUTE_PATHS.ISOLATIE,
  ROUTE_PATHS.TRAPRENOVATIE,
  ROUTE_PATHS.CV_KETEL,
  ROUTE_PATHS.TUINBESTRATING,
  ROUTE_PATHS.CONTRACTOR,
  ROUTE_PATHS.CONSTRUCTION,
  ROUTE_PATHS.ROOFING,
  ROUTE_PATHS.WATERLEIDING,
  ROUTE_PATHS.DIRECT_LOGIN,
];

// Landing page routes configuration
export const LANDING_ROUTES = {
  [ROUTE_PATHS.VENTILATION]: {
    path: ROUTE_PATHS.VENTILATION,
    component: "VentilationLanding",
    title: "Ventilatie",
  },
  [ROUTE_PATHS.IKEA_FURNITURE]: {
    path: ROUTE_PATHS.IKEA_FURNITURE,
    component: "IkeaLanding",
    title: "IKEA Montage",
  },
  [ROUTE_PATHS.DAKKAPEL]: {
    path: ROUTE_PATHS.DAKKAPEL,
    component: "DakkapelLanding",
    title: "Dakkapel plaatsen of renoveren",
  },
  [ROUTE_PATHS.BADKAMER]: {
    path: ROUTE_PATHS.BADKAMER,
    component: "BadkamerLanding",
    title: "Badkamer laten installeren of renoveren",
  },
  [ROUTE_PATHS.LEKKAGE]: {
    path: ROUTE_PATHS.LEKKAGE,
    component: "LekkageLanding",
    title: "Lekkage verhelpen",
  },
  [ROUTE_PATHS.ISOLATIE]: {
    path: ROUTE_PATHS.ISOLATIE,
    component: "IsolatieLanding",
    title: "Huis isoleren",
  },
  [ROUTE_PATHS.TRAPRENOVATIE]: {
    path: ROUTE_PATHS.TRAPRENOVATIE,
    component: "TraprenovatieLanding",
    title: "Traprenovatie",
  },
  [ROUTE_PATHS.CV_KETEL]: {
    path: ROUTE_PATHS.CV_KETEL,
    component: "CVLanding",
    title: "CV-ketel repareren of onderhouden",
  },
  [ROUTE_PATHS.TUINBESTRATING]: {
    path: ROUTE_PATHS.TUINBESTRATING,
    component: "TuinbestratingLanding",
    title: "Tuinbestrating laten aanleggen",
  },
  [ROUTE_PATHS.CONTRACTOR]: {
    path: ROUTE_PATHS.CONTRACTOR,
    component: "ContractorLanding",
    title: "Aannemer",
  },
  [ROUTE_PATHS.CONSTRUCTION]: {
    path: ROUTE_PATHS.CONSTRUCTION,
    component: "ConstructionLanding",
    title: "Bouwbedrijf",
  },
  [ROUTE_PATHS.ROOFING]: {
    path: ROUTE_PATHS.ROOFING,
    component: "RoofingLanding",
    title: "Dakbedekking",
  },
  [ROUTE_PATHS.WATERLEIDING]: {
    path: ROUTE_PATHS.WATERLEIDING,
    component: "WaterleidingLanding",
    title: "Waterleiding vervangen",
  },
} as const;

// in your routes config file

// Check if a route is public (UPDATED to handle dynamic routes)
export const isPublicRoute = (pathname: string): boolean => {
  // Check if we're on admin domain - root path should not be public on admin domain
  const isAdminDomain =
    typeof window !== "undefined" && window.location.hostname === ADMIN_DOMAIN;
  if (isAdminDomain && pathname === "/") {
    return false;
  }

  // First, check for a direct match in the public routes array.
  // This is fast and works for all non-dynamic routes like /auth, /inloggen, etc.
  if (PUBLIC_ROUTES.includes(pathname as any)) {
    return true;
  }

  // If no direct match, check if it matches a dynamic public route pattern.
  for (const route of PUBLIC_ROUTES) {
    // Check if the public route is a dynamic pattern (contains ':')
    if (route.includes(":")) {
      // Convert the pattern (/weblogs/:id) to a base path (/weblogs/)
      const basePath = route.substring(0, route.indexOf(":"));

      // If the current pathname starts with this base path, it's a match.
      // We also check if basePath is not empty to avoid a false positive for "/"
      if (pathname.startsWith(basePath) && basePath !== "/") {
        return true;
      }
    }
  }

  // This check from your original code is also useful, so we'll keep it as a fallback.
  // Note: The logic above already handles `/plaats-een-klus/:id` if you add it to PUBLIC_ROUTES
  if (pathname.includes(ROUTE_PATHS.PLACE_JOB)) {
    return true;
  }

  // If no matches are found, it's a protected route.
  return false;
};

// Check if a route is a landing page
export const isLandingRoute = (pathname: string): boolean => {
  return Object.keys(LANDING_ROUTES).includes(pathname);
};

// Get landing route config
export const getLandingRouteConfig = (pathname: string) => {
  return LANDING_ROUTES[pathname as keyof typeof LANDING_ROUTES];
};

// User types
export const USER_TYPES = {
  ADMIN: "admin",
  VAKMAN: "vakman",
  KLUSAANVRAGER: "klusaanvrager",
} as const;

// MFA configuration
export const MFA_CONFIG = {
  REQUIRED_LEVEL: "aal2",
} as const;

// Session configuration
export const SESSION_CONFIG = {
  COOKIE_NAME: "klusgebied-session",
  MAX_AGE: 60 * 60 * 24 * 7, // 7 days
  REFRESH_THRESHOLD: 60 * 5, // 5 minutes
} as const;

// Toast messages
export const TOAST_MESSAGES = {
  SESSION_EXPIRED: {
    title: "Sessie verlopen",
    description: "Je sessie is verlopen. Log opnieuw in.",
  },
  LOGOUT_SUCCESS: {
    title: "Uitgelogd",
    description: "Je bent succesvol uitgelogd.",
  },
  LOGOUT_ERROR: {
    title: "Uitloggen mislukt",
    description:
      "Er is een fout opgetreden bij het uitloggen. Probeer het opnieuw.",
  },
  ACCESS_DENIED: {
    title: "Geen toegang",
    description: "Als vakman kun je geen klussen plaatsen.",
  },
  NEW_RESPONSE: {
    title: "Nieuw antwoord",
    description: "Sommigen reageren op jouw baan.",
  },
} as const;

// Navigation configuration
export const NAVIGATION_CONFIG = {
  RETURN_URL_PARAM: "returnUrl",
  DEFAULT_REDIRECT: ROUTE_PATHS.HOME,
} as const;
