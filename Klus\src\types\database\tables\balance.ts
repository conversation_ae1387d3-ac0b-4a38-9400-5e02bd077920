import type { J<PERSON> } from '../json';

export type BalanceTransaction = {
  Row: {
    id: string;
    user_id: string;
    amount: number;
    type: string;
    description: string | null;
    created_at: string;
  };
  Insert: {
    id?: string;
    user_id: string;
    amount: number;
    type: string;
    description?: string | null;
    created_at?: string;
  };
  Update: {
    id?: string;
    user_id?: string;
    amount?: number;
    type?: string;
    description?: string | null;
    created_at?: string;
  };
  Relationships: [];
};