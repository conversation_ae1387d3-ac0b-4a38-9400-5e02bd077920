export interface Message {
  id: string;
  content: string;
  created_at: string;
  job_id: string;
  read: boolean | null;
  receiver_id: string;
  sender_id: string;
  attachment_url?: string;
  attachment_type?: "image" | "file";
  sender?: {
    company_name?: string;
    first_name: string | null;
    last_name: string | null;
    full_name: string | null;
    profile_photo_url?: string | null;
  };
  job?:
    | {
        title: string;
        user_id: string;
      }
    | null
    | { error: true };
}
