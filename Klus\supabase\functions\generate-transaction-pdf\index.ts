import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.3";
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { generateTransactionPDF } from "./pdf-generator.ts";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

async function handler(req: Request) {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { transactionId } = await req.json();

    if (!transactionId) {
      throw new Error("Transaction ID is required");
    }

    console.log("Generating PDF for transaction:", transactionId);

    const supabase = createClient(
      Deno.env.get("SUPABASE_URL") || "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || ""
    );

    // Fetch transaction details
    const { data: transaction, error: transactionError } = await supabase
      .from("balance_transactions")
      .select(
        `
        *,
        profiles:user_id (
          first_name,
          last_name,
          company_name,
          btw_number,
          street_address,
          house_number,
          house_number_addition
        )
      `
      )
      .eq("id", transactionId)
      .single();

    if (transactionError || !transaction) {
      console.error("Error fetching transaction:", transactionError);
      throw new Error("Transaction not found");
    }

    console.log("Generating PDF document...");
    const pdfBytes = await generateTransactionPDF(
      transaction,
      transaction.profiles
    );

    console.log("Uploading PDF to storage...");
    const { error: uploadError } = await supabase.storage
      .from("transaction-pdfs")
      .upload(`${transactionId}.pdf`, pdfBytes, {
        contentType: "application/pdf",
        upsert: true,
      });

    if (uploadError) {
      console.error("Error uploading PDF:", uploadError);
      throw uploadError;
    }

    const {
      data: { publicUrl },
    } = supabase.storage
      .from("transaction-pdfs")
      .getPublicUrl(`${transactionId}.pdf`);

    console.log("Updating transaction with PDF URL:", publicUrl);
    const { error: updateError } = await supabase
      .from("balance_transactions")
      .update({ pdf_url: publicUrl })
      .eq("id", transactionId);

    if (updateError) {
      console.error("Error updating transaction:", updateError);
      throw updateError;
    }

    return new Response(JSON.stringify({ url: publicUrl }), {
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Error generating PDF:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json",
      },
    });
  }
}

serve(handler);
