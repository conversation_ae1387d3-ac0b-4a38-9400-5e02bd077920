import { Router } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { validateRequired } from '../middleware/validation';
import { supabaseAdmin } from '../config/supabase';
import { generateTransactionPDF } from '../services/pdfGenerator';

const router = Router();

// Generate transaction PDF
router.post('/transaction',
  validateRequired(['transactionId']),
  asyncHandler(async (req, res) => {
    const { transactionId } = req.body;

    console.log('Generating PDF for transaction:', transactionId);

    // Fetch transaction details
    const { data: transaction, error: transactionError } = await supabaseAdmin
      .from('balance_transactions')
      .select(`
        *,
        profiles:user_id (
          first_name,
          last_name,
          company_name,
          btw_number,
          street_address,
          house_number,
          house_number_addition,
          email
        )
      `)
      .eq('id', transactionId)
      .single();

    if (transactionError || !transaction) {
      console.error('Error fetching transaction:', transactionError);
      throw new Error('Transaction not found');
    }

    console.log('Transaction found, generating PDF...');

    // Generate PDF
    const pdfBuffer = await generateTransactionPDF(transaction, transaction.profiles);

    // Set response headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="transaction-${transactionId}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    // Send PDF buffer
    res.send(pdfBuffer);
  })
);

export default router;
