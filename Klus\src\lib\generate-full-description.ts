export const generateFullDescription = (
  id: string,
  answers: string[],
  description: string
) => {
  switch (id) {
    case "custom":
      return `
Klus Details:
${description}

Aanvullende informatie:
- Type werk: ${answers[0]}
- Locatie: ${answers[1]}
- Soort werk: ${answers[2]}
- Binnen/buiten: ${answers[3]}
- Voorbereidingsstatus: ${answers[4]}
      `.trim();

    case "lekkage-verhelpen":
      return `
Lekkage Details:
${description}

Aanvullende informatie:
- Locatie: ${answers[0]}
- Duur: ${answers[1]}
- Oorzaak: ${answers[2]}
- <PERSON>hade: ${answers[3]}
- Ernst: ${answers[4]}
- Eerdere reparatie: ${answers[5]}
      `.trim();

    case "huisisolatie":
      return `
Isolatie Details:
${description}

Aanvullende informatie:
- Type isolatie: ${answers[0]}
- Bouwjaar woning: ${answers[1]}
- Type woning: ${answers[2]}
- Huidige isolatie: ${answers[3]}
- Hoofdreden: ${answers[4]}
- Vochtproblemen: ${answers[5]}
      `.trim();
    case "traprenovatie":
      return `
Traprenovatie Details:
${description}

Aanvullende informatie:
- Type trap: ${answers[0]}
- Huidige staat: ${answers[1]}
- Materiaal voorkeur: ${answers[2]}
- Specifieke wensen: ${answers[3]}
- Budget: ${answers[4]}
- Planning: ${answers[5]}
      `.trim();

    case "ventilatie-vervangen-of-reinigen":
      return `
Ventilatie Details:
${description}

Aanvullende informatie:
- Huidig type: ${answers[0]}
- Reden vervanging: ${answers[1]}
- Ruimtes: ${answers[2]}
- Bestaand systeem: ${answers[3]}
- Vocht/schimmel: ${answers[4]}
- Warmteterugwinning: ${answers[5]}
      `.trim();

    case "cv":
      return `
CV-ketel Details:
${description}

Aanvullende informatie:
- Probleem: ${answers[0]}
- Leeftijd ketel: ${answers[1]}
- Merk: ${answers[2]}
- Begin probleem: ${answers[3]}
- Eerder onderhoud: ${answers[4]}
- Storing/foutcode: ${answers[5]}
      `.trim();

    case "stucwerk-binnen":
      return `
Stucwerk Details:
${description}

Aanvullende informatie:
- Oppervlakte: ${answers[0]}
- Type stucwerk: ${answers[1]}
- Locatie: ${answers[2]}
- Ondergrond: ${answers[3]}
- Voorbereidend werk: ${answers[4]}
- Planning: ${answers[5]}
      `.trim();

    case "tuin-bestrating":
      return `
Tuinbestrating Details:
${description}

Aanvullende informatie:
- Oppervlakte: ${answers[0]}
- Type bestrating: ${answers[1]}
- Huidige situatie: ${answers[2]}
- Type ondergrond: ${answers[3]}
- Afwatering: ${answers[4]}
- Bereikbaarheid: ${answers[5]}
      `.trim();

    case "waterleiding-vervangen":
      return `
Loodgieter Details:
${description}

Aanvullende informatie:
- Type werk: ${answers[0]}
- Locatie: ${answers[1]}
- Urgentie: ${answers[2]}
- Leeftijd leidingwerk: ${answers[3]}
- Eerder werk: ${answers[4]}
- Bereikbaarheid: ${answers[5]}
      `.trim();

    case "dakrenovatie-of-vervanging":
      return `
Dak Details:
${description}

Aanvullende informatie:
- Type werkzaamheden: ${answers[0]}
- Type dak: ${answers[1]}
- Dakmateriaal: ${answers[2]}
- Oppervlakte: ${answers[3]}
- Lekkage: ${answers[4]}
- Laatste onderhoud: ${answers[5]}
      `.trim();

    case "ikea-meubels":
      return `
IKEA Meubels Details:
${description}

Aanvullende informatie:
- Type meubels: ${answers[0]}
- Aantal meubels: ${answers[1]}
- Leveringsstatus: ${answers[2]}
- Montagelocatie: ${answers[3]}
- Specifieke wensen: ${answers[4]}
      `.trim();

    case "aannemer-inschakelen":
      return `
Aannemer Project Details:
${description}

Aanvullende informatie:
- Type project: ${answers[0]}
- Omvang project: ${answers[1]}
- Bouwjaar pand: ${answers[2]}
- Bouwtekening status: ${answers[3]}
- Vergunning status: ${answers[4]}
- Werkzaamheden: ${answers[5]}
- Gewenste startdatum: ${answers[6]}
- Budget indicatie: ${answers[7]}
- Project omschrijving: ${answers[8]}
      `.trim();

    case "bouwbedrijf-inschakelen":
      return `
Bouwbedrijf Project Details:
${description}

Aanvullende informatie:
- Type bouwproject: ${answers[0]}
- Gewenste startdatum: ${answers[1]}
- Voorbereidingsstatus: ${answers[2]}
- Project omschrijving: ${answers[3]}
      `.trim();

    case "badkamer-renovatie-installatie":
      return `
Badkamer Renovatie & Installatie Details:
${description}

Aanvullende informatie:
- Type badkamerwerk: ${answers[0]}
- Oppervlakte badkamer: ${answers[1]}
- Huidige staat: ${answers[2]}
- Gewenste voorzieningen: ${answers[3]}
- Budget: ${answers[4]}
- Gewenste startdatum: ${answers[5]}
      `.trim();

    case "badkamer-renovatie-installatie":
      return `
Badkamer Renovatie & Installatie Details:
${description}

Aanvullende informatie:
- Type badkamerwerk: ${answers[0]}
- Oppervlakte badkamer: ${answers[1]}
- Huidige staat: ${answers[2]}
- Gewenste voorzieningen: ${answers[3]}
- Budget: ${answers[4]}
- Gewenste startdatum: ${answers[5]}
      `.trim();

    case "elektra-klussen":
      return `
Elektra Klussen Details:
${description}

Aanvullende informatie:
- Soort klus: ${answers[0]}
- Locatie: ${answers[1]}
- Spoed: ${answers[2]}
- Bouwjaar woning: ${answers[3]}
- Omschrijving: ${answers[4]}
      `.trim();

    case "ongedierte-bestrijding":
      return `
Ongedierte Bestrijding Details:
${description}

Aanvullende informatie:
- Type ongedierte: ${answers[0]}
- Locatie probleem: ${answers[1]}
- Ernst overlast: ${answers[2]}
- Eerder bestrijding: ${answers[3]}
- Omschrijving: ${answers[4]}
      `.trim();

    case "timmerman":
      return `
Timmerman Details:
${description}

Aanvullende informatie:
- Type timmerwerk: ${answers[0]}
- Locatie: ${answers[1]}
- Ontwerp/tekening: ${answers[2]}
- Gewenste startmoment: ${answers[3]}
- Omschrijving: ${answers[4]}
      `.trim();

    case "kozijnen":
      return `
Kozijnen Plaatsen of Vervangen Details:
${description}

Aanvullende informatie:
- Type kozijnen: ${answers[0]}
- Vervangen of nieuw plaatsen: ${answers[1]}
- Aantal kozijnen: ${answers[2]}
- Glas laten plaatsen/vervangen: ${answers[3]}
- Gewenste planning: ${answers[4]}
- Omschrijving: ${answers[5]}
      `.trim();

    case "klusjesman":
      return `
Klusjesman Details:
${description}

Aanvullende informatie:
- Type klusjes: ${answers[0]}
- Aantal klussen: ${answers[1]}
- Locatie(s): ${answers[2]}
- Spoed: ${answers[3]}
- Omschrijving: ${answers[4]}
      `.trim();

    case "airco-installateur":
      return `
Airco Installatie Details:
${description}

Aanvullende informatie:
- Type airco: ${answers[0]}
- Aantal ruimtes: ${answers[1]}
- Vervanging of nieuw: ${answers[2]}
- Gewenste koelcapaciteit: ${answers[3]}
- Gewenste installatiedatum: ${answers[4]}
- Omschrijving/wensen: ${answers[5]}
      `.trim();

    case "dakkapel-plaatsen-vervangen":
      return "";
  }
};
