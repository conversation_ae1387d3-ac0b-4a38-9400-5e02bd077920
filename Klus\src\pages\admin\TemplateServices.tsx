import { Check, ChevronsUpDown } from "lucide-react";

import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { serviceCategories } from "@/components/profile/ProfileServices";
import { cn } from "@/lib/utils";

interface TemplateServicesProps {
  selectedServices: string[];
  onServicesChange: (services: string[]) => void;
}

export function TemplateServices({
  selectedServices,
  onServicesChange,
}: TemplateServicesProps) {
  // Flatten all services for search
  const allServices = serviceCategories.flatMap((category) =>
    category.services.map((service) => ({
      category: category.name,
      service,
      icon: category.icon,
    }))
  );

  const toggleService = (service: string) => {
    if (selectedServices.includes(service)) {
      onServicesChange(selectedServices.filter((s) => s !== service));
    } else {
      onServicesChange([...selectedServices, service]);
    }
  };

  return (
    <div className="space-y-4">
      <Popover modal>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            className="w-full justify-between"
          >
            <span className="truncate">
              {selectedServices.length > 0
                ? `${selectedServices.length} dienst${
                    selectedServices.length === 1 ? "" : "en"
                  } geselecteerd`
                : "Selecteer diensten..."}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0 bg-white" align="start">
          <Command>
            <CommandInput placeholder="Zoek een dienst..." />
            <CommandList>
              <CommandEmpty>Geen diensten gevonden.</CommandEmpty>
              <ScrollArea className="h-[300px]">
                {serviceCategories.map((category) => (
                  <CommandGroup key={category.name} heading={category.name}>
                    {category.services.map((service) => (
                      <CommandItem
                        key={service}
                        onSelect={() => toggleService(service)}
                        className="flex items-center gap-2"
                      >
                        <div
                          className={cn(
                            "flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                            selectedServices.includes(service)
                              ? "bg-primary text-primary-foreground"
                              : "opacity-50"
                          )}
                        >
                          {selectedServices.includes(service) && (
                            <Check className={cn("h-3 w-3")} />
                          )}
                        </div>
                        <span>{category.icon}</span>
                        <span>{service}</span>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                ))}
              </ScrollArea>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedServices.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedServices.map((service) => {
            const serviceInfo = allServices.find((s) => s.service === service);
            return (
              <Badge
                key={service}
                variant="secondary"
                className="px-3 py-1 text-sm"
              >
                {serviceInfo?.icon} {service}
                <button
                  className="ml-2 hover:text-destructive focus:outline-none"
                  onClick={() => toggleService(service)}
                >
                  ×
                </button>
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
}
