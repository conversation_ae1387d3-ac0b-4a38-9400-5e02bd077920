import { Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";

interface JobCardContentProps {
  title: string;
  description: string;
  isVakman: boolean;
  status: string;
  hasResponded: boolean;
  jobId: string;
  onRespond: () => void;
  isCheckingResponse: boolean;
}

export const JobCardContent = ({
  description,
  isVakman,
  status,
  hasResponded,
  onRespond,
  isCheckingResponse,
}: JobCardContentProps) => {
  return (
    <div className="p-6 sm:flex hidden flex-col flex-grow space-y-4">
      <p className="text-muted-foreground line-clamp-2 flex-grow">
        {description}
      </p>
      {isVakman && status !== "completed" && (
        <div className="pt-2">
          {isCheckingResponse ? (
            <Button disabled className="w-full">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Laden...
            </Button>
          ) : hasResponded ? (
            <Button
              variant="secondary"
              className="w-full"
              onClick={(e) => {
                e.stopPropagation();
                onRespond();
              }}
            >
              Je hebt gereageerd
            </Button>
          ) : (
            <Button
              className="w-full"
              onClick={(e) => {
                e.stopPropagation();
                onRespond();
              }}
            >
              Reageer op klus
            </Button>
          )}
        </div>
      )}
    </div>
  );
};
