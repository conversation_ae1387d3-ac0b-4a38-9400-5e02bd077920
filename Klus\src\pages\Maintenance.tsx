// src/pages/Maintenance.js

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Check, X, ArrowR<PERSON>, Star } from 'lucide-react';
import { packages, comparisonFeatures } from '@/components/ui/contract-template'; // Import from the new data file

const Maintenance = () => {
    const navigate = useNavigate();

    // This function now navigates to a new page instead of opening a modal.
    const handleChoosePackage = (pkg) => {
        navigate(`/contract-signup/${pkg.id}`);
    };

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Packages Section */}
            <section className="py-20 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16 animate-slide-up">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">On<PERSON></h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto"><PERSON><PERSON> pak<PERSON>, ontworpen voor uw comfort en zekerheid. Kies degene die het beste bij u past.</p>
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                        {packages.map((pkg, index) => (
                            <div 
                                key={pkg.name}
                                className={`relative bg-white rounded-2xl shadow-lg border transition-all duration-300 animate-slide-up ${pkg.highlight ? 'border-teal-400 border-2 scale-105' : 'border-gray-200 hover:shadow-2xl hover:-translate-y-2'}`}
                                style={{ animationDelay: `${index * 150}ms` }}
                            >
                                {pkg.highlight && (
                                    <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-teal-400 text-white px-4 py-1 rounded-full text-sm font-bold flex items-center shadow-md">
                                        <Star size={14} className="mr-2" />
                                        Meest Gekozen
                                    </div>
                                )}
                                <div className="p-8 flex flex-col h-full">
                                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                                    <p className="text-gray-500 mb-6 text-sm">{pkg.targetAudience}</p>
                                    
                                    <div className="mb-6">
                                        <span className="text-4xl font-bold text-gray-900">€{pkg.price.toFixed(2).replace('.', ',')}</span>
                                        <span className="text-gray-500"> /maand</span>
                                    </div>

                                    <div className="flex-grow mb-8">
                                        <h4 className="font-semibold text-gray-800 mb-3">Inbegrepen:</h4>
                                        <ul className="space-y-3">
                                            {pkg.included.map((item, i) => (
                                                <li key={i} className="flex items-start">
                                                    <Check className="h-5 w-5 text-teal-500 mt-1 mr-3 flex-shrink-0" />
                                                    <span className="text-gray-700">{item}</span>
                                                </li>
                                            ))}
                                        </ul>
                                        {pkg.excluded.length > 0 && (
                                            <>
                                                <h4 className="font-semibold text-gray-800 mt-6 mb-3">Niet inbegrepen:</h4>
                                                <ul className="space-y-3">
                                                    {pkg.excluded.map((item, i) => (
                                                        <li key={i} className="flex items-start">
                                                            <X className="h-5 w-5 text-red-400 mt-1 mr-3 flex-shrink-0" />
                                                            <span className="text-gray-700">{item}</span>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </>
                                        )}
                                    </div>

                                    <button
                                        onClick={() => handleChoosePackage(pkg)}
                                        className={`w-full mt-auto inline-flex items-center justify-center px-6 py-3 rounded-lg text-lg font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-1 ${pkg.highlight ? 'bg-teal-400 text-white hover:bg-teal-500' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}`}
                                    >
                                        {pkg.ctaText}
                                        <ArrowRight className="ml-2" size={20} />
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Comparison Table Section */}
            <section className="py-20 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16 animate-slide-up">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Vergelijk de Pakketten</h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">Vind in één oogopslag de verschillen en kies het abonnement dat perfect bij u past.</p>
                    </div>
                    <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200 animate-fade-in">
                        <div className="overflow-x-auto">
                            <table className="w-full text-left">
                                <thead className="bg-gray-100">
                                    <tr>
                                        <th className="p-6 text-lg font-bold text-gray-900">Functie</th>
                                        <th className="p-6 text-center text-lg font-bold text-gray-900">Comfort</th>
                                        <th className="p-6 text-center text-lg font-bold text-teal-600">Zeker</th>
                                        <th className="p-6 text-center text-lg font-bold text-gray-900">All-in Wonen</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {comparisonFeatures.map((item, index) => (
                                        <tr key={index} className="border-t border-gray-200">
                                            <td className="p-5 font-semibold text-gray-800">{item.feature}</td>
                                            <td className="p-5 text-center text-gray-600">{item.comfort}</td>
                                            <td className="p-5 text-center font-bold text-teal-600 bg-teal-50/50">{item.zeker}</td>
                                            <td className="p-5 text-center text-gray-600">{item.allin}</td>
                                        </tr>
                                    ))}
                                    <tr className="border-t border-gray-200 bg-gray-50">
                                        <td className="p-6 font-bold text-gray-900">Prijs per maand</td>
                                        <td className="p-6 text-center font-bold text-xl text-gray-900">€15,95</td>
                                        <td className="p-6 text-center font-bold text-xl text-teal-600 bg-teal-50/50">€39,95</td>
                                        <td className="p-6 text-center font-bold text-xl text-gray-900">€79,95</td>
                                    </tr>
                                    <tr className="border-t border-gray-200">
                                        <td className="p-4"></td>
                                        <td className="p-4 text-center">
                                            <button onClick={() => handleChoosePackage(packages[0])} className="inline-flex items-center justify-center px-5 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-all duration-200 font-medium">Kiezen</button>
                                        </td>
                                        <td className="p-4 text-center bg-teal-50/50">
                                            <button onClick={() => handleChoosePackage(packages[1])} className="inline-flex items-center justify-center px-5 py-2 bg-teal-400 text-white rounded-lg hover:bg-teal-500 transition-all duration-200 font-medium shadow-md hover:shadow-lg">Kiezen</button>
                                        </td>
                                        <td className="p-4 text-center">
                                            <button onClick={() => handleChoosePackage(packages[2])} className="inline-flex items-center justify-center px-5 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-all duration-200 font-medium">Kiezen</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>
            
            {/* The wizard component is now removed from this page. */}
        </div>
    );
};

export default Maintenance;