import { LucideIcon } from "lucide-react";

import { Card } from "@/components/ui/card";

interface StatCardProps {
  title: string;
  value: string;
  icon: LucideIcon;
  color: "primary" | "green" | "blue" | "yellow";
}

const colorMap = {
  primary: "bg-primary/10 text-primary",
  green: "bg-green-100 text-green-600",
  blue: "bg-blue-100 text-blue-600",
  yellow: "bg-yellow-100 text-yellow-600",
};

export const StatCard = ({
  title,
  value,
  icon: Icon,
  color,
}: StatCardProps) => {
  return (
    <Card className="p-6 transition-all duration-300 hover:shadow-lg">
      <div className="flex items-center space-x-4">
        <div className={`p-3 rounded-lg ${colorMap[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
        <div>
          <p className="text-sm text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
        </div>
      </div>
    </Card>
  );
};
